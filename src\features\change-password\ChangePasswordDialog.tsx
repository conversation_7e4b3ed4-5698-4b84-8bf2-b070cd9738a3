// // import Dialog from "@/components/mui-vanced/Dialog";
// import IconButton from "@/components/mui-vanced/IconButton";
// import { useState } from "react";
// import CloseIcon from "@mui/icons-material/CloseRounded";
// import UpdateIcon from "@mui/icons-material/UpdateRounded";
// // import DialogContent from "@/components/mui-vanced/DialogContent";
// import TextField from "@/components/mui-vanced/TextField";
// import FieldTitle from "@/components/mui-vanced/FieldTitle";
// // import DialogActions from "@/components/mui-vanced/DialogActions";
// import Button from "@/components/mui-vanced/Button";
// // import DialogTitle from "@/components/mui-vanced/DialogTitle";
// import { ChangePasswordService } from "@/services/auth.service";
// import { snackbar } from "@/utils/snackbar.util";
// import {
//   CustomDialog,
//   CustomDialogActions,
//   CustomDialogContent,
//   CustomDialogTitle,
// } from "@/components/mui-vanced/CustomDialog";

// export default function ChangePasswordDialog({
//   open,
//   setOpen,
// }: ChangePasswordDialogProps) {
//   const [loading, setLoading] = useState(false);
//   const [oldPassword, setOldPassword] = useState("");
//   const [newPassword, setNewPassword] = useState("");
//   const [oldPasswordHelperText, setOldPasswordHelperText] = useState("");
//   const [newPasswordHelperText, setNewPasswordHelperText] = useState("");

//   const handleCloseDialog = (_event: React.SyntheticEvent, reason?: string) => {
//     // if (reason && reason === closeDialogReasons.backdropClick || reason === closeDialogReasons.escapeKeyDown) {
//     //   return;
//     // }

//     setOpen(false);

//     setTimeout(() => {
//       setLoading(false);
//       setOldPassword("");
//       setNewPassword("");
//       setOldPasswordHelperText("");
//       setNewPasswordHelperText("");
//     }, 500);
//   };

//   const handleSubmit = async (event: React.SyntheticEvent) => {
//     event.preventDefault();

//     let error = false;
//     if (!oldPassword) {
//       setOldPasswordHelperText("Old password is required");
//       error = true;
//     }
//     if (!newPassword) {
//       setNewPasswordHelperText("New password is required");
//       error = true;
//     }
//     if (error) {
//       return;
//     }

//     setLoading(true);
//     const changePasswordResponse = await ChangePasswordService(
//       oldPassword,
//       newPassword
//     );
//     if (changePasswordResponse.success) {
//       snackbar.showSuccessMessage(changePasswordResponse.data);
//       handleCloseDialog(event, "submitButtonClick");
//     } else {
//       setLoading(false);
//       snackbar.showErrorMessage(changePasswordResponse.data);
//     }
//   };

//   return (
//     <CustomDialog
//       open={open}
//       fullWidth
//       maxWidth="xs"
//       onClose={handleCloseDialog}
//     >
//       <CustomDialogTitle>
//         Change Password
//         <IconButton
//           onClick={(event) => {
//             handleCloseDialog(event, "closeButtonClick");
//           }}
//         >
//           <CloseIcon />
//         </IconButton>
//       </CustomDialogTitle>
//       <CustomDialogContent>
//         <FieldTitle title="Old Password" required />
//         <TextField
//           autoFocus
//           type="password"
//           value={oldPassword}
//           error={!!oldPasswordHelperText}
//           helperText={oldPasswordHelperText}
//           placeholder="Enter old password"
//           onChange={(event) => {
//             setOldPassword(event.target.value);
//             setOldPasswordHelperText("");
//           }}
//           sx={{
//             marginBottom: 2,
//           }}
//         />

//         <FieldTitle title="New Password" required />
//         <TextField
//           type="password"
//           value={newPassword}
//           error={!!newPasswordHelperText}
//           helperText={newPasswordHelperText}
//           placeholder="Enter new password"
//           onChange={(event) => {
//             setNewPassword(event.target.value);
//             setNewPasswordHelperText("");
//           }}
//         />
//       </CustomDialogContent>
//       <CustomDialogActions>
//         <Button
//           loading={loading}
//           loadingPosition="start"
//           startIcon={<UpdateIcon />}
//           onClick={handleSubmit}
//         >
//           Update
//         </Button>
//       </CustomDialogActions>
//     </CustomDialog>
//   );
// }

// type ChangePasswordDialogProps = {
//   open: boolean;
//   setOpen: React.Dispatch<React.SetStateAction<boolean>>;
// };

import { useState } from "react";
import Button from "@/components/custom/Button";
import TextField from "@/components/custom/TextField";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { ChangePasswordService } from "@/services/auth.service";
import { snackbar } from "@/utils/snackbar.util";
import { X, Lock, Shield } from "lucide-react";

export default function ChangePasswordDialog({
  open,
  setOpen,
}: ChangePasswordDialogProps) {
  const [loading, setLoading] = useState(false);
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [oldPasswordHelperText, setOldPasswordHelperText] = useState("");
  const [newPasswordHelperText, setNewPasswordHelperText] = useState("");

  const handleCloseDialog = (_event: React.SyntheticEvent, reason?: string) => {
    // if (reason && reason === "backdropClick" || reason === "escapeKeyDown") {
    //   return;
    // }

    setOpen(false);

    setTimeout(() => {
      setLoading(false);
      setOldPassword("");
      setNewPassword("");
      setOldPasswordHelperText("");
      setNewPasswordHelperText("");
    }, 500);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    let error = false;
    if (!oldPassword) {
      setOldPasswordHelperText("Old password is required");
      error = true;
    }
    if (!newPassword) {
      setNewPasswordHelperText("New password is required");
      error = true;
    }
    if (error) {
      return;
    }

    setLoading(true);
    const changePasswordResponse = await ChangePasswordService(
      oldPassword,
      newPassword
    );
    if (changePasswordResponse.success) {
      snackbar.showSuccessMessage(changePasswordResponse.data);
      handleCloseDialog(event, "submitButtonClick");
    } else {
      setLoading(false);
      snackbar.showErrorMessage(changePasswordResponse.data);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) =>
        !isOpen && handleCloseDialog({} as React.SyntheticEvent)
      }
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-sky-100 to-sky-200 rounded-lg flex items-center justify-center">
              <Shield className="h-5 w-5 text-sky-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Change Password</h3>
              <p className="text-sm text-gray-500 font-normal">
                Update your account password
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Current Password <span className="text-red-500">*</span>
            </label>
            <TextField
              type="password"
              value={oldPassword}
              placeholder="Enter your current password"
              onChange={(event) => {
                setOldPassword(event.target.value);
                setOldPasswordHelperText("");
              }}
              error={!!oldPasswordHelperText}
              helperText={oldPasswordHelperText}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              New Password <span className="text-red-500">*</span>
            </label>
            <TextField
              type="password"
              value={newPassword}
              placeholder="Enter your new password"
              onChange={(event) => {
                setNewPassword(event.target.value);
                setNewPasswordHelperText("");
              }}
              error={!!newPasswordHelperText}
              helperText={newPasswordHelperText}
            />
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Password Requirements:</strong> Use a strong password with
              at least 8 characters, including uppercase, lowercase, numbers,
              and special characters.
            </p>
          </div>
        </div>
        <DialogFooter className="gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            loading={loading}
            startIcon={<Lock className="h-4 w-4" />}
          >
            Update Password
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type ChangePasswordDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
};
