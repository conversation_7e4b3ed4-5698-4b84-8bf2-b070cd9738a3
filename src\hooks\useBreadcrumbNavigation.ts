import { useEffect } from "react";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import {
  setCurrentPath,
  updateBreadcrumbItem,
  BreadcrumbItem,
} from "@/redux/slices/breadcrumbSlice";
import { GetProjectService } from "@/services/projects.service";
import { GetJobDetailsService } from "@/services/jobs.service";

const buildBreadcrumbPath = async (router: any, dispatch: any) => {
  const currentPath = router.asPath.split("?")[0];
  const breadcrumbs: BreadcrumbItem[] = [];

  // Dashboard
  if (currentPath === "/dashboard") {
    breadcrumbs.push({
      id: "dashboard",
      label: "Dashboard",
      href: "/dashboard",
      level: 0,
      metadata: { type: "page" },
    });
  }

  // Projects list
  else if (currentPath === "/projects") {
    breadcrumbs.push({
      id: "projects",
      label: "Projects",
      href: "/projects",
      level: 0,
      metadata: { type: "page" },
    });
  }

  // Project detail (shows jobs)
  else if (currentPath.match(/^\/projects\/([^\/]+)$/)) {
    const projectId = router.query.projectId;

    // Add Projects root
    breadcrumbs.push({
      id: "projects",
      label: "Projects",
      href: "/projects",
      level: 0,
      metadata: { type: "page" },
    });

    // Add current project with loading state
    breadcrumbs.push({
      id: `project-${projectId}`,
      label: `Loading...`,
      href: `/projects/${projectId}`,
      level: 1,
      isLoading: true,
      metadata: { type: "project" },
    });

    // Set initial path
    dispatch(setCurrentPath(breadcrumbs));

    // Fetch project name
    try {
      const projectResponse = await GetProjectService(projectId);
      if (projectResponse.success) {
        dispatch(
          updateBreadcrumbItem({
            id: `project-${projectId}`,
            label: projectResponse.data.name,
            isLoading: false,
            metadata: { type: "project" },
          })
        );
      }
    } catch (error) {
      dispatch(
        updateBreadcrumbItem({
          id: `project-${projectId}`,
          label: `Project ${projectId}`,
          isLoading: false,
          metadata: { type: "project" },
        })
      );
    }

    return; // Early return since we handle async update above
  }

  // Job detail (new nested route)
  else if (currentPath.match(/^\/projects\/([^\/]+)\/job\/([^\/]+)$/)) {
    const projectId = router.query.projectId;
    const jobId = router.query.jobId;

    // Build initial path with loading states
    const initialPath: BreadcrumbItem[] = [
      {
        id: "projects",
        label: "Projects",
        href: "/projects",
        level: 0,
        metadata: { type: "page" },
      },
      {
        id: `project-${projectId}`,
        label: "Loading...",
        href: `/projects/${projectId}`,
        level: 1,
        isLoading: true,
        metadata: { type: "project" },
      },
      {
        id: `job-${jobId}`,
        label: "Loading...",
        href: `/projects/${projectId}/job/${jobId}`,
        level: 2,
        isLoading: true,
        metadata: { type: "job" },
      },
    ];

    // Set initial path
    dispatch(setCurrentPath(initialPath));

    // Fetch both project and job details
    try {
      const [jobResponse, projectResponse] = await Promise.all([
        GetJobDetailsService(jobId),
        GetProjectService(projectId),
      ]);

      if (jobResponse.success) {
        dispatch(
          updateBreadcrumbItem({
            id: `job-${jobId}`,
            label: jobResponse.data.name,
            isLoading: false,
            metadata: {
              type: "job",
              processName: jobResponse.data.process_name,
            },
          })
        );
      } else {
        dispatch(
          updateBreadcrumbItem({
            id: `job-${jobId}`,
            label: `Job ${jobId}`,
            isLoading: false,
            metadata: { type: "job" },
          })
        );
      }

      if (projectResponse.success) {
        dispatch(
          updateBreadcrumbItem({
            id: `project-${projectId}`,
            label: projectResponse.data.name,
            isLoading: false,
            metadata: { type: "project" },
          })
        );
      } else {
        dispatch(
          updateBreadcrumbItem({
            id: `project-${projectId}`,
            label: `Project ${projectId}`,
            isLoading: false,
            metadata: { type: "project" },
          })
        );
      }
    } catch (error) {
      dispatch(
        updateBreadcrumbItem({
          id: `job-${jobId}`,
          label: `Job ${jobId}`,
          isLoading: false,
          metadata: { type: "job" },
        })
      );
      dispatch(
        updateBreadcrumbItem({
          id: `project-${projectId}`,
          label: `Project ${projectId}`,
          isLoading: false,
          metadata: { type: "project" },
        })
      );
    }

    return; // Early return since we handle async update above
  }

  // Other pages
  else if (currentPath === "/playground") {
    breadcrumbs.push({
      id: "playground",
      label: "Playground",
      href: "/playground",
      level: 0,
      metadata: { type: "page" },
    });
  } else if (currentPath === "/assign-jobs") {
    breadcrumbs.push({
      id: "assign-jobs",
      label: "Assign Jobs",
      href: "/assign-jobs",
      level: 0,
      metadata: { type: "page" },
    });
  } else if (currentPath === "/manage-users") {
    breadcrumbs.push({
      id: "manage-users",
      label: "Manage Users",
      href: "/manage-users",
      level: 0,
      metadata: { type: "page" },
    });
  } else if (currentPath === "/settings") {
    breadcrumbs.push({
      id: "settings",
      label: "Settings",
      href: "/settings",
      level: 0,
      metadata: { type: "page" },
    });
  }

  // Set the path for non-async routes
  dispatch(setCurrentPath(breadcrumbs));
};

export const useBreadcrumbNavigation = () => {
  const router = useRouter();
  const dispatch = useDispatch();

  useEffect(() => {
    if (!router.isReady) return;

    buildBreadcrumbPath(router, dispatch);
  }, [router.asPath, router.isReady, router.query, dispatch]);
};

// Hook to manually update breadcrumb labels (useful for components that have specific data)
export const useBreadcrumbUpdate = () => {
  const dispatch = useDispatch();

  const updateLabel = (id: string, label: string, isLoading?: boolean) => {
    dispatch(updateBreadcrumbItem({ id, label, isLoading }));
  };

  return { updateLabel };
};
