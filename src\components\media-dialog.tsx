"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import {
  Maximize2,
  Minimize2,
  ChevronLeft,
  ChevronRight,
  Plus,
  X,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MediaRenderer } from "@/components/media-renderer";
import ConfirmationDialog from "@/components/custom/ConfirmationDialog";

interface MediaItem {
  id: number;
  type: "image" | "video" | "audio" | "document";
  title: string;
  url: string;
  thumbnail: string;
  product: any;
  mediaData?: any; // Store original media data for deletion
}

interface MediaDialogProps {
  mediaItems: MediaItem[];
  activeIndex: number;
  onMediaChange: (index: number) => void;
  onAddMediaClick?: () => void;
  onClose?: () => void;
  onMediaDeleted?: (mediaId: string) => void;
  showNavigation?: boolean;
  defaultMinimized?: boolean;
}

export function MediaDialog({
  mediaItems,
  activeIndex,
  onMediaChange,
  onAddMediaClick,
  onClose,
  onMediaDeleted,
  showNavigation = true,
  defaultMinimized = false,
}: MediaDialogProps) {
  const [isMinimized, setIsMinimized] = useState(defaultMinimized);
  const [minimizedPosition, setMinimizedPosition] = useState({ x: 0, y: 0 });
  const [maximizedPosition, setMaximizedPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  
  // Add resize state
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<'left' | 'right' | 'bottom' | null>(null);
  const [dialogSize, setDialogSize] = useState({ width: 520, height: 400 });
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });

  // Get current media item
  const currentMedia = mediaItems[activeIndex] || mediaItems[0];

  // Calculate responsive height based on media type
  const getDialogHeight = (minimized: boolean) => {
    if (minimized) return 100;

    // For audio files, use smaller height since they don't need much visual space
    if (currentMedia?.type === "audio") {
      return Math.min(dialogSize.height, 280);
    }

    // Default height for other media types
    return dialogSize.height;
  };

  // Get current position based on state
  const getCurrentPosition = () => {
    return isMinimized ? minimizedPosition : maximizedPosition;
  };

  // Update position with boundary constraints
  const updatePosition = (newPosition: { x: number; y: number }) => {
    const dialogWidth = isMinimized ? 320 : dialogSize.width;
    const dialogHeight = getDialogHeight(isMinimized);

    const constrainedPosition = {
      x: Math.max(0, Math.min(window.innerWidth - dialogWidth, newPosition.x)),
      y: Math.max(0, Math.min(window.innerHeight - dialogHeight, newPosition.y)),
    };

    if (isMinimized) {
      setMinimizedPosition(constrainedPosition);
    } else {
      setMaximizedPosition(constrainedPosition);
    }
  };

  // Initialize positions at top-right
  useEffect(() => {
    const initializePositions = () => {
      const minimizedWidth = 320;
      const maximizedWidth = dialogSize.width;
      const minimizedHeight = getDialogHeight(true);
      const maximizedHeight = getDialogHeight(false);

      const minimizedPos = {
        x: window.innerWidth - minimizedWidth - 20,
        y: 20,
      };

      const maximizedPos = {
        x: window.innerWidth - maximizedWidth - 20,
        y: 20,
      };

      setMinimizedPosition(minimizedPos);
      setMaximizedPosition(maximizedPos);
    };

    initializePositions();
    window.addEventListener("resize", initializePositions);
    return () => window.removeEventListener("resize", initializePositions);
  }, [currentMedia?.type, dialogSize]);

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent, direction: 'left' | 'right' | 'bottom') => {
    if (!dialogRef.current) return;
    
    setIsResizing(true);
    setResizeDirection(direction);
    const rect = dialogRef.current.getBoundingClientRect();
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: rect.width,
      height: rect.height
    });
  };

  // Handle resize move
  const handleResizeMove = (e: MouseEvent) => {
    if (!isResizing || !resizeDirection || !dialogRef.current) return;

    const deltaX = e.clientX - resizeStart.x;
    const deltaY = e.clientY - resizeStart.y;
    const newSize = { ...dialogSize };

    switch (resizeDirection) {
      case 'left':
        const newWidth = Math.max(320, resizeStart.width - deltaX);
        const newX = Math.min(
          window.innerWidth - 20,
          resizeStart.x + (resizeStart.width - newWidth)
        );
        newSize.width = newWidth;
        updatePosition({ ...getCurrentPosition(), x: newX });
        break;
      case 'right':
        newSize.width = Math.max(320, Math.min(window.innerWidth - getCurrentPosition().x - 20, resizeStart.width + deltaX));
        break;
      case 'bottom':
        newSize.height = Math.max(200, Math.min(window.innerHeight - getCurrentPosition().y - 20, resizeStart.height + deltaY));
        break;
    }

    setDialogSize(newSize);
  };

  // Handle resize end
  const handleResizeEnd = () => {
    setIsResizing(false);
    setResizeDirection(null);
  };

  // Add resize event listeners
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
      return () => {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [isResizing, resizeDirection, resizeStart]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!dialogRef.current) return;

    const rect = dialogRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
    setIsDragging(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const newPosition = {
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y,
    };

    updatePosition(newPosition);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  const handlePrevious = () => {
    const newIndex = activeIndex > 0 ? activeIndex - 1 : mediaItems.length - 1;
    onMediaChange(newIndex);
  };

  const handleNext = () => {
    const newIndex = activeIndex < mediaItems.length - 1 ? activeIndex + 1 : 0;
    onMediaChange(newIndex);
  };

  // Delete media handler
  const handleDeleteMedia = async () => {
    if (!currentMedia?.mediaData?._id || !onMediaDeleted) return;

    setIsDeleting(true);
    try {
      await onMediaDeleted(currentMedia.mediaData._id);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error("Failed to delete media:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const currentPosition = getCurrentPosition();
  const dialogHeight = getDialogHeight(isMinimized);

  if (!currentMedia) {
    return null;
  }

  return (
    <>
      <div
        ref={dialogRef}
        className={`fixed bg-white rounded-md shadow-md border-2 border-silver-200 z-50 transition-all duration-200 select-none flex flex-col ${
          isDragging ? "cursor-crosshair" : "cursor-default"
        }`}
        style={{
          left: `${currentPosition.x}px`,
          top: `${currentPosition.y}px`,
          width: isMinimized ? "320px" : `${dialogSize.width}px`,
          height: `${getDialogHeight(isMinimized)}px`,
          resize: 'none',
        }}
      >
        {/* Resize Handles */}
        {!isMinimized && (
          <>
            <div
              className="absolute left-0 top-0 bottom-0 w-1 cursor-ew-resize hover:bg-sky-500/50 active:bg-sky-500"
              onMouseDown={(e) => handleResizeStart(e, 'left')}
            />
            <div
              className="absolute right-0 top-0 bottom-0 w-1 cursor-ew-resize hover:bg-sky-500/50 active:bg-sky-500"
              onMouseDown={(e) => handleResizeStart(e, 'right')}
            />
            <div
              className="absolute left-0 right-0 bottom-0 h-1 cursor-ns-resize hover:bg-sky-500/50 active:bg-sky-500"
              onMouseDown={(e) => handleResizeStart(e, 'bottom')}
            />
          </>
        )}

        {/* Header - Fixed height */}
        <div
          className="flex items-center justify-between p-3 border-b border-gray-100 cursor-grab active:cursor-grabbing flex-shrink-0 h-[48px]"
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center gap-2 min-w-0">
            <div className="text-sm font-semibold truncate">
              Input Files
              {/* {currentMedia.title} */}
            </div>
            {showNavigation && mediaItems.length > 1 && (
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {activeIndex + 1}/{mediaItems.length}
              </div>
            )}
          </div>
          <div className="flex items-center gap-1">
            {/* Add Media Button */}
            {onAddMediaClick && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onAddMediaClick}
                    className={`${isMinimized ? "h-6 w-6 p-0" : "h-6 px-2"}`}
                  >
                    {isMinimized ? (
                      <Plus className="h-3 w-3" />
                    ) : (
                      <>
                        <Plus className="h-3 w-3 mr-1" />
                        {/* <span className="text-xs">Add Media</span> */}
                      </>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add Media</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Delete Media Button */}
            {onMediaDeleted && currentMedia?.mediaData?._id && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowDeleteConfirm(true)}
                    className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delete Media</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Minimize/Maximize Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="h-6 w-6 p-0"
                >
                  {isMinimized ? (
                    <Maximize2 className="h-3 w-3" />
                  ) : (
                    <Minimize2 className="h-3 w-3" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isMinimized ? "Maximize" : "Minimize"}</p>
              </TooltipContent>
            </Tooltip>

            {/* Close Button */}
            {onClose && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onClose}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Close</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>

        {/* Content - Flexible height */}
        <div className="flex-1 min-h-0 p-2 overflow-auto">
          {isMinimized ? (
            <div className="flex items-center gap-2 h-full">
              <div className="flex-shrink-0">
                <MediaRenderer
                  key={currentMedia.id}
                  mediaItem={currentMedia}
                  isMinimized={true}
                  className="w-8 h-8"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-xs font-medium truncate">
                  {currentMedia.product.name}
                </div>
                {/* <div className="text-xs text-gray-500 truncate">
                  {currentMedia.product.price}
                </div> */}
              </div>
              {showNavigation && mediaItems.length > 1 && (
                <div className="flex gap-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handlePrevious}
                        className="h-6 w-6 p-0"
                      >
                        <ChevronLeft className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Previous</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleNext}
                        className="h-6 w-6 p-0"
                      >
                        <ChevronRight className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Next</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              )}
            </div>
          ) : (
            <div className="h-full flex flex-col">
              {/* Media Preview - Takes remaining height */}
              <div className="flex-1 min-h-0 mb-3 relative">
                <div className="absolute inset-0">
                  <MediaRenderer
                    key={currentMedia.id}
                    mediaItem={currentMedia}
                    isMinimized={false}
                    className="w-full h-full"
                    containerStyle={{
                      minHeight: '200px',
                      height: '100%',
                      maxHeight: 'none'
                    }}
                  />
                </div>
              </div>

              {/* Footer - Fixed height */}
              <div className="flex items-center justify-between flex-shrink-0 h-[40px]">
                <div className="text-sm min-w-0 flex-1 mr-3">
                  <div className="font-medium truncate">
                    {currentMedia.product.name}
                  </div>
                </div>
                {showNavigation && mediaItems.length > 1 && (
                  <div className="flex gap-2 flex-shrink-0">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handlePrevious}
                        >
                          <ChevronLeft className="h-4 w-4 mr-1" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Previous media file</p>
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleNext}
                        >
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Next media file</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Delete Media File"
        description={`Are you sure you want to delete "${
          currentMedia?.mediaData?.filename || currentMedia?.title
        }"? This action cannot be undone.`}
        confirmLabel="Delete"
        cancelLabel="Cancel"
        onConfirm={handleDeleteMedia}
        loading={isDeleting}
        variant="destructive"
      />
    </>
  );
}
