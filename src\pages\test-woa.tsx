import { withAuth } from "@/utils/withAuth";
import AssignJobsPage from "@/features/assign-jobs/AssignJobsPage";
import Head from "next/head";

export default function JobAssignment() {
  return (
    <>
      <Head>
        <title>Test - Aroma</title>
        <meta name="description" content="Assign jobs to agents" />
      </Head>
      <p>test without auh</p>
      {process.env.NEXT_PUBLIC_API_BASE_URL}
    </>
  );
}
