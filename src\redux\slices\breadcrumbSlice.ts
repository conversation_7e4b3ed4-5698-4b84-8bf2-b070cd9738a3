import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface BreadcrumbItem {
  id: string;
  label: string;
  href: string;
  level: number;
  isLoading?: boolean;
  metadata?: {
    type?: "project" | "job" | "page";
    processName?: string;
    [key: string]: any;
  };
}

interface BreadcrumbState {
  currentPath: BreadcrumbItem[];
}

const initialState: BreadcrumbState = {
  currentPath: [],
};

const breadcrumbSlice = createSlice({
  name: "breadcrumb",
  initialState,
  reducers: {
    setCurrentPath: (state, action: PayloadAction<BreadcrumbItem[]>) => {
      state.currentPath = action.payload;
    },

    updateBreadcrumbItem: (
      state,
      action: PayloadAction<{
        id: string;
        label: string;
        isLoading?: boolean;
        metadata?: BreadcrumbItem["metadata"];
      }>
    ) => {
      const { id, label, isLoading, metadata } = action.payload;
      const item = state.currentPath.find((item) => item.id === id);
      if (item) {
        item.label = label;
        if (isLoading !== undefined) {
          item.isLoading = isLoading;
        }
        if (metadata !== undefined) {
          item.metadata = { ...item.metadata, ...metadata };
        }
      }
    },

    clearBreadcrumbs: (state) => {
      state.currentPath = [];
    },
  },
});

export const { setCurrentPath, updateBreadcrumbItem, clearBreadcrumbs } =
  breadcrumbSlice.actions;

export default breadcrumbSlice.reducer;
