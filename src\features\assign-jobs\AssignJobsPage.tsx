import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/router";
import { motion } from "framer-motion";
import {
  GetAllJobs,
  UnassignJobsService,
  AssignJobToAgentService,
} from "@/services/jobs.service";
import { GetUsersService } from "@/services/users.service";
import { snackbar } from "@/utils/snackbar.util";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Briefcase,
  Users,
  Loader2,
  ArrowUpDown,
  ArrowUpNarrowWide,
  ArrowDownWideNarrow,
} from "lucide-react";

// Import our new components
import { JobListItem } from "@/components/assign-jobs/JobListItem";
import { FloatingActionTab } from "@/components/assign-jobs/FloatingActionTab";
import { FloatingHeader } from "@/components/assign-jobs/FloatingHeader";
import { JobListSkeleton } from "@/components/assign-jobs/JobListSkeleton";
import BulkAssignDialog from "@/features/jobs/BulkAssignDialog";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";

// Types
type JobWithAssignee = Job & {
  assigned_to?: string;
  assigned_to_name?: string;
  project_name?: string;
};

type SortOption = {
  field: "created_at" | "name" | "status";
  direction: "asc" | "desc";
  clicked?: boolean;
};

export default function AssignJobsPage() {
  const router = useRouter();
  const [jobs, setJobs] = useState<JobWithAssignee[]>([]);
  const [agents, setAgents] = useState<User[]>([]);
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());

  const [initialLoading, setInitialLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [bulkAssignDialogOpen, setBulkAssignDialogOpen] = useState(false);

  // Infinite scroll state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalJobs, setTotalJobs] = useState(0);
  const limit = 25; // Fixed limit for infinite scroll

  // Refs for scroll tracking and infinite scroll
  const jobsListRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<HTMLDivElement>(null);
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);

  const [sortOption, setSortOption] = useState<SortOption>({
    field: "created_at",
    direction: "desc",
    clicked: false,
  });
  const [sortByCreatedAt, setSortByCreatedAt] = useState(false);
  const [sortAscending, setSortAscending] = useState<boolean | null>(false);

  // Date range filter state - initialize from URL
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    const startDate = router.query.start_date as string;
    const endDate = router.query.end_date as string;
    if (startDate && endDate) {
      return {
        from: new Date(startDate),
        to: new Date(endDate),
      };
    }
    return undefined;
  });

  // Custom scroll detection that works with the layout
  useEffect(() => {
    const handleScroll = () => {
      // Try to find the main scrolling container
      const mainElement = document.querySelector("main") as HTMLElement;
      if (mainElement) {
        const scrollTop = mainElement.scrollTop;
        const shouldShow = scrollTop > 300; // Show after scrolling 300px
        console.log(
          "Scroll position:",
          scrollTop,
          "Should show header:",
          shouldShow
        );
        setShowFloatingHeader(shouldShow);
      }
    };

    // Find and attach to the main element
    const mainElement = document.querySelector("main") as HTMLElement;
    if (mainElement) {
      mainElement.addEventListener("scroll", handleScroll, { passive: true });
      // Check initial position
      handleScroll();

      return () => {
        mainElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, []);

  // Initialize filters from URL
  const [searchTerm, setSearchTerm] = useState(
    (router.query.search as string) || ""
  );
  const [statusFilter, setStatusFilter] = useState<string>(
    (router.query.status as string) || "all"
  );
  const [assigneeFilter, setAssigneeFilter] = useState<string>(
    (router.query.assignee as string) || "all"
  );
  const [unassignedOnly, setUnassignedOnly] = useState(
    router.query.unassigned === "true"
  );

  // URL update function
  const updateUrl = (params: Record<string, any>) => {
    const query: any = { ...router.query, ...params };
    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null || query[key] === "all") {
        delete query[key];
      }
    });
    if (query.unassigned === false) delete query.unassigned;
    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  // Fetch jobs with infinite scroll support
  const fetchJobs = async (
    page = 1,
    isInitial = false,
    isLoadMore = false,
    silent = false
  ) => {
    // Set appropriate loading states
    if (!silent) {
      if (isInitial || initialLoading) {
        setInitialLoading(true);
      } else if (isLoadMore) {
        setLoadingMore(true);
      }
    }

    try {
      // Prepare date range parameters - only include if both dates are present
      const startDate =
        dateRange?.from && dateRange?.to
          ? format(dateRange.from, "yyyy-MM-dd")
          : null;
      const endDate =
        dateRange?.from && dateRange?.to
          ? format(dateRange.to, "yyyy-MM-dd")
          : null;

      console.log("fetchJobs - Date Range Debug:", {
        dateRange,
        startDate,
        endDate,
        hasFrom: !!dateRange?.from,
        hasTo: !!dateRange?.to,
        bothPresent: !!(dateRange?.from && dateRange?.to),
      });

      const response = await GetAllJobs(
        page,
        limit,
        unassignedOnly,
        statusFilter && statusFilter !== "all" ? statusFilter : undefined,
        searchTerm || undefined,
        assigneeFilter && assigneeFilter !== "all" ? assigneeFilter : undefined,
        sortByCreatedAt,
        sortAscending,
        startDate,
        endDate
      );

      if (response.success) {
        const newJobs = response.data.data || [];
        const meta = response.data.meta;

        if (isInitial || !isLoadMore) {
          // Replace jobs for initial load or refresh
          setJobs(newJobs);
          setCurrentPage(1);
        } else {
          // Append jobs for infinite scroll
          setJobs((prev) => [...prev, ...newJobs]);
          setCurrentPage(page);
        }

        // Update infinite scroll state
        setTotalJobs(meta.total);
        setHasMore(page < meta.total_pages);
      } else {
        snackbar.showErrorMessage("Failed to fetch jobs");
      }
    } catch (error) {
      snackbar.showErrorMessage("Error fetching jobs");
    } finally {
      setInitialLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more function for infinite scroll
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore && !initialLoading) {
      fetchJobs(currentPage + 1, false, true, false);
    }
  }, [loadingMore, hasMore, initialLoading, currentPage]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, [loadMore]);

  // Fetch agents
  const fetchAgents = async () => {
    try {
      const response = await GetUsersService(1, 100, "");
      if (response.success) {
        setAgents(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
    }
  };

  // Watch for URL changes
  useEffect(() => {
    const urlSearch = (router.query.search as string) || "";
    const urlStatus = (router.query.status as string) || "all";
    const urlAssignee = (router.query.assignee as string) || "all";
    const urlUnassigned = router.query.unassigned === "true";
    const startDate = router.query.start_date as string;
    const endDate = router.query.end_date as string;

    if (urlSearch !== searchTerm) setSearchTerm(urlSearch);
    if (urlStatus !== statusFilter) setStatusFilter(urlStatus);
    if (urlAssignee !== assigneeFilter) setAssigneeFilter(urlAssignee);
    if (urlUnassigned !== unassignedOnly) setUnassignedOnly(urlUnassigned);

    // Handle date range from URL
    if (startDate && endDate) {
      const newDateRange = {
        from: new Date(startDate),
        to: new Date(endDate),
      };
      if (
        !dateRange ||
        dateRange.from?.getTime() !== newDateRange.from.getTime() ||
        dateRange.to?.getTime() !== newDateRange.to.getTime()
      ) {
        setDateRange(newDateRange);
      }
    } else if (dateRange) {
      setDateRange(undefined);
    }
  }, [
    router.query.search,
    router.query.status,
    router.query.assignee,
    router.query.unassigned,
    router.query.start_date,
    router.query.end_date,
  ]);

  // Initial load
  useEffect(() => {
    if (router.isReady) {
      fetchJobs(1, true, false, false);
      fetchAgents();
    }
  }, [router.isReady]);

  // Filter changes - reset and fetch from beginning
  useEffect(() => {
    if (!initialLoading) {
      setJobs([]);
      setCurrentPage(1);
      setHasMore(true);
      fetchJobs(1, true, false, false);
    }
  }, [statusFilter, assigneeFilter, unassignedOnly, sortAscending, dateRange]);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!initialLoading) {
        updateUrl({
          search: searchTerm || undefined,
          status: statusFilter === "all" ? undefined : statusFilter,
          assignee: assigneeFilter === "all" ? undefined : assigneeFilter,
          unassigned: unassignedOnly || undefined,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
        // Reset and fetch from beginning for search
        setJobs([]);
        setCurrentPage(1);
        setHasMore(true);
        fetchJobs(1, true, false, false);
      }
    }, 300); // Reduced debounce time from 500ms to 300ms

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Since we're using server-side pagination, we use jobs directly
  const paginatedJobs = jobs;

  // Handle job selection
  const handleJobSelect = useCallback(
    (jobId: string) => {
      const newSelectedJobs = new Set(selectedJobs);
      if (newSelectedJobs.has(jobId)) {
        newSelectedJobs.delete(jobId);
      } else {
        newSelectedJobs.add(jobId);
      }
      setSelectedJobs(newSelectedJobs);
    },
    [selectedJobs]
  );

  // Handle select all
  const handleSelectAll = () => {
    if (selectedJobs.size === paginatedJobs.length) {
      setSelectedJobs(new Set());
    } else {
      const allJobIds = new Set(paginatedJobs.map((job) => job._id));
      setSelectedJobs(allJobIds);
    }
  };

  const handleClearSelection = () => {
    setSelectedJobs(new Set());
  };

  // Handle bulk unassign
  const handleBulkUnassign = async () => {
    if (selectedJobs.size === 0) return;

    try {
      const response = await UnassignJobsService(Array.from(selectedJobs));
      if (response.success) {
        snackbar.showSuccessMessage(
          `Successfully unassigned ${selectedJobs.size} job(s)`
        );
        handleClearSelection();
        // Reset and fetch from beginning
        setJobs([]);
        setCurrentPage(1);
        setHasMore(true);
        fetchJobs(1, true, false, false);
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to unassign jobs");
    }
  };

  // Handle bulk assign - open dialog
  const handleBulkAssign = () => {
    if (selectedJobs.size === 0) return;
    setBulkAssignDialogOpen(true);
  };

  // Handle individual job assignment
  const handleAssignJob = async (jobId: string, assigneeId: string) => {
    try {
      const response = await AssignJobToAgentService(jobId, assigneeId);
      if (response.success) {
        snackbar.showSuccessMessage("Job assigned successfully");
        // Refresh current data without resetting scroll position
        fetchJobs(1, true, false, true);
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to assign job");
    }
  };

  // Handle individual job unassignment
  const handleUnassignJob = async (jobId: string) => {
    try {
      const response = await UnassignJobsService([jobId]);
      if (response.success) {
        snackbar.showSuccessMessage("Job unassigned successfully");
        // Refresh current data without resetting scroll position
        fetchJobs(1, true, false, true);
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to unassign job");
    }
  };

  // Handle filter changes
  const handleFilterChange = useCallback(
    (type: string, value: string) => {
      if (type === "status") {
        setStatusFilter(value);
        updateUrl({
          status: value === "all" ? undefined : value,
          search: searchTerm || undefined,
          assignee: assigneeFilter === "all" ? undefined : assigneeFilter,
          unassigned: unassignedOnly || undefined,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
      } else if (type === "assignee") {
        setAssigneeFilter(value);
        updateUrl({
          assignee: value === "all" ? undefined : value,
          search: searchTerm || undefined,
          status: statusFilter === "all" ? undefined : statusFilter,
          unassigned: unassignedOnly || undefined,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
      }

      // Reset infinite scroll state
      setCurrentPage(1);
      setHasMore(true);
    },
    [searchTerm, statusFilter, assigneeFilter, unassignedOnly]
  );

  // Floating header handlers
  const handleFloatingHeaderUnassignedToggle = useCallback(
    (value: string) => {
      if (value === "unassigned") {
        setUnassignedOnly(true);
        setAssigneeFilter("all");
        updateUrl({
          unassigned: true,
          assignee: undefined,
          search: searchTerm || undefined,
          status: statusFilter === "all" ? undefined : statusFilter,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
      } else {
        setUnassignedOnly(false);
        handleFilterChange("assignee", value);
      }
      // Reset infinite scroll state
      setCurrentPage(1);
      setHasMore(true);
    },
    [searchTerm, statusFilter, handleFilterChange]
  );

  // Navigation handlers
  const handleJobClick = (jobId: string) => {
    const job = jobs.find((j) => j._id === jobId);
    if (job) {
      router.push(`/projects/${job.project_id}/job/${job._id}`);
    }
  };

  const handleProjectClick = (projectId: string) => {
    router.push(`/projects/${projectId}`);
  };

  // Toggle sort direction - this will be handled by backend API
  const toggleSortDirection = () => {
    // If not clicked yet, set to ascending
    if (!sortOption.clicked) {
      setSortByCreatedAt(true);
      setSortAscending(true);
      setSortOption({ ...sortOption, direction: "asc", clicked: true });
    }
    // If already clicked and ascending, set to descending
    else if (sortOption.direction === "asc") {
      setSortByCreatedAt(true);
      setSortAscending(false);
      setSortOption({ ...sortOption, direction: "desc" });
    }
    // If already clicked and descending, reset to default (not clicked)
    else {
      setSortByCreatedAt(false);
      setSortAscending(null);
      setSortOption({ ...sortOption, clicked: false });
    }
    // The actual sorting will be handled by the backend API
  };

  // if (true) {
  //   return (
  //     <div className="min-h-screen">
  //       <div className="max-w-[1600px] mx-auto">
  //         <motion.div
  //           initial={{ opacity: 0, y: 20 }}
  //           animate={{ opacity: 1, y: 0 }}
  //           transition={{ duration: 0.6 }}
  //         >
  //           <h1 className="text-3xl font-bold text-[#111827] mb-4">
  //             Assign Jobs
  //           </h1>
  //           <p className="text-md text-[#6B7280] leading-relaxed">
  //             Manage job assignments and monitor task distribution across your
  //             team.
  //           </p>
  //         </motion.div>

  //         <motion.div
  //           initial={{ opacity: 0, y: 20 }}
  //           animate={{ opacity: 1, y: 0 }}
  //           transition={{ duration: 0.6, delay: 0.2 }}
  //         >
  //           <JobListSkeleton count={6} />
  //         </motion.div>
  //       </div>
  //     </div>
  //   );
  // }

  // const handleDateRangeChange = (range: DateRange | undefined) => {
  //   console.log("range -> ", range);
  // };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    // Update state first
    setDateRange(range);

    // Only update URL if both dates are selected or range is cleared
    // The useEffect will handle the API call automatically like status filter
    if (!range || (range.from && range.to)) {
      updateUrl({
        search: searchTerm || undefined,
        status: statusFilter === "all" ? undefined : statusFilter,
        assignee: assigneeFilter === "all" ? undefined : assigneeFilter,
        unassigned: unassignedOnly || undefined,
        start_date: range?.from ? format(range.from, "yyyy-MM-dd") : undefined,
        end_date: range?.to ? format(range.to, "yyyy-MM-dd") : undefined,
      });
    }
    // Note: API call will be triggered by useEffect dependency on dateRange
  };

  return (
    <div className="min-h-screen relative">
      {/* Floating Header */}
      <FloatingHeader
        isVisible={showFloatingHeader && !initialLoading}
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        assigneeFilter={assigneeFilter}
        unassignedOnly={unassignedOnly}
        agents={agents}
        paginatedJobsLength={paginatedJobs.length}
        selectedJobsSize={selectedJobs.size}
        onSearchChange={setSearchTerm}
        onFilterChange={handleFilterChange}
        onUnassignedToggle={handleFloatingHeaderUnassignedToggle}
        onSelectAll={handleSelectAll}
      />

      <div className="max-w-[1600px] mx-auto pb-32">
        {/* Header with fade-in animation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center mb-4">
            <h1 className="text-4xl font-bold text-[#111827]">Assign Jobs</h1>
            {/* {silentLoading && (
              <Loader2 className="w-5 h-5 animate-spin text-sky-600" />
            )} */}
          </div>
          <p className="text-lg text-[#6B7280]">
            Manage job assignments and monitor task distribution across your
            team.
          </p>
        </motion.div>

        {/* Filters */}
        <div className="mb-8 mt-8">
          <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* Search Input */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6B7280]" />
                <Input
                  placeholder="Search jobs or projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 border-[#E5E7EB] focus:border-sky-600 focus:ring-sky-600 w-full"
                />
              </div>

              {/* Status Filter */}
              <div className="w-48">
                <Select
                  value={statusFilter}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <SelectTrigger className="w-full h-10">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee Filter */}
              <div className="w-48 border border-red-500">
                <Select
                  value={unassignedOnly ? "unassigned" : assigneeFilter}
                  onValueChange={(value) => {
                    if (value === "unassigned") {
                      setUnassignedOnly(true);
                      setAssigneeFilter("all");
                      updateUrl({
                        unassigned: true,
                        assignee: undefined,
                        search: searchTerm || undefined,
                        status:
                          statusFilter === "all" ? undefined : statusFilter,
                        start_date: dateRange?.from
                          ? format(dateRange.from, "yyyy-MM-dd")
                          : undefined,
                        end_date: dateRange?.to
                          ? format(dateRange.to, "yyyy-MM-dd")
                          : undefined,
                      });
                    } else {
                      setUnassignedOnly(false);
                      handleFilterChange("assignee", value);
                      updateUrl({
                        unassigned: undefined,
                        assignee: value,
                        search: searchTerm || undefined,
                        status:
                          statusFilter === "all" ? undefined : statusFilter,
                        start_date: dateRange?.from
                          ? format(dateRange.from, "yyyy-MM-dd")
                          : undefined,
                        end_date: dateRange?.to
                          ? format(dateRange.to, "yyyy-MM-dd")
                          : undefined,
                      });
                    }
                    // Reset infinite scroll state
                    setCurrentPage(1);
                    setHasMore(true);
                  }}
                >
                  <SelectTrigger className="w-full h-10">
                    <SelectValue placeholder="Filter by assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Assignees</SelectItem>
                    <SelectItem value="unassigned">Unassigned Only</SelectItem>
                    {agents.map((agent) => (
                      <SelectItem key={agent._id} value={agent._id}>
                        {/* {agent.username} */}
                        <div className="flex items-center gap-2">
                          <span>{agent.username}</span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {agent.role}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Date Range Filter with Integrated Sort Button */}
            <DateRangePicker
              date={dateRange}
              onDateChange={handleDateRangeChange}
              placeholder="Filter by date range"
              className="h-10"
              disableFuture={true}
              rightButton={
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleSortDirection();
                  }}
                  className="h-8 w-8 p-0 mr-1 hover:bg-gray-100"
                  title={
                    !sortOption.clicked
                      ? "Sort"
                      : sortOption.direction === "asc"
                      ? "Sort Ascending"
                      : "Sort Descending"
                  }
                >
                  {!sortOption.clicked ? (
                    <ArrowUpDown className="h-4 w-4 text-sky-600" />
                  ) : sortOption.direction === "asc" ? (
                    <ArrowUpNarrowWide className="h-4 w-4 text-sky-600" />
                  ) : (
                    <ArrowDownWideNarrow className="h-4 w-4 text-sky-600" />
                  )}
                </Button>
              }
            />

            {/* Results count and bulk select */}
            <div className="flex items-center gap-4">
              {/* <div className="flex items-center gap-2 text-sm text-[#6B7280]">
                <Filter className="w-4 h-4" />
                <span>
                  Showing {paginatedJobs.length} of {filteredJobs.length} jobs
                </span>
              </div> */}

              {paginatedJobs.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="h-9 px-3 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white"
                >
                  <Users className="w-4 h-4 mr-2" />
                  {selectedJobs.size === paginatedJobs.length
                    ? "Deselect All"
                    : "Select All"}
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Jobs List */}
        {initialLoading ? (
          <JobListSkeleton count={6} />
        ) : (
          <div ref={jobsListRef}>
            {paginatedJobs.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="border border-[#E5E7EB] bg-white shadow-sm">
                  <CardContent className="text-center py-16">
                    <Briefcase className="w-16 h-16 text-[#9CA3AF] mx-auto mb-6" />
                    <h3 className="text-xl font-semibold text-[#111827] mb-3">
                      No jobs found
                    </h3>
                    <p className="text-[#6B7280] text-lg">
                      Try adjusting your search or filter criteria.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              <>
                <motion.div
                  key={`jobs-${searchTerm}-${statusFilter}-${assigneeFilter}`}
                  className="space-y-4"
                  role="list"
                  aria-label="Job assignments list"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {paginatedJobs.map((job, index) => (
                    <motion.div
                      key={job._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.3,
                        delay: index * 0.03,
                        ease: "easeOut",
                      }}
                    >
                      <JobListItem
                        job={job}
                        isSelected={selectedJobs.has(job._id)}
                        onSelect={handleJobSelect}
                        onJobClick={handleJobClick}
                        onProjectClick={handleProjectClick}
                        onAssignJob={handleAssignJob}
                        onUnassignJob={handleUnassignJob}
                        agents={agents}
                      />
                    </motion.div>
                  ))}
                </motion.div>

                {/* Infinite scroll trigger */}
                {hasMore && (
                  <div ref={observerRef} className="flex justify-center py-8">
                    {loadingMore ? (
                      <div className="flex items-center gap-2 text-gray-500">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Loading more jobs...</span>
                      </div>
                    ) : (
                      <div className="h-4" /> // Invisible trigger element
                    )}
                  </div>
                )}

                {/* End of list indicator */}
                {!hasMore && jobs.length > 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <p>You've reached the end</p>
                    <p className="text-sm mt-1">{totalJobs} total jobs</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Floating Action Tab */}
      <FloatingActionTab
        selectedCount={selectedJobs.size}
        isVisible={!initialLoading}
        onClearSelection={handleClearSelection}
        onBulkAssign={handleBulkAssign}
        onBulkUnassign={handleBulkUnassign}
      />

      {/* Bulk Assign Dialog */}
      <BulkAssignDialog
        open={bulkAssignDialogOpen}
        setOpen={setBulkAssignDialogOpen}
        selectedJobIds={Array.from(selectedJobs)}
        agents={agents}
        onAssignmentComplete={() => {
          fetchJobs(1, true, false, true); // Silent refresh
          handleClearSelection();
        }}
        projectId="" // Not needed for assign jobs page
        onJobRemoved={(jobId) => {
          const newSelectedJobs = new Set(selectedJobs);
          newSelectedJobs.delete(jobId);
          setSelectedJobs(newSelectedJobs);
        }}
      />
    </div>
  );
}
