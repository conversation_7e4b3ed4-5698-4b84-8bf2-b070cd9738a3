import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Button from "@/components/custom/Button";
import { Copy, Download, Clock } from "lucide-react";
import { PlaygroundInput } from "@/features/playground/PlaygroundInput";
import { PlaygroundOutput } from "@/features/playground/PlaygroundOutput";
import { ProcessSaveDialog } from "@/features/playground/ProcessSaveDialog";
import { MediaDialog } from "@/components/media-dialog";
import {
  GenerateSchemaService,
  RunGenericEntityExtractionProcessService,
} from "@/services/process.service";
import { snackbar } from "@/utils/snackbar.util";
import Head from "next/head";
import { withAuth } from "@/utils/withAuth";

// Assuming you have these animations defined in your global CSS or tailwind.config.js
// For example, in your globals.css:
// @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
// .animate-fade-in { animation: fadeIn 0.5s ease-out forwards; }
// @keyframes slideUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
// .animate-slide-up { animation: slideUp 0.5s ease-out forwards; }

// Add utility function for formatting time
const formatElapsedTime = (seconds: number, includeMs: boolean = false) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  if (includeMs) {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `in ${Math.round(seconds)}s`;
};

const formatProcessingTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export default function Playground() {
  const [input, setInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [output, setOutput] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [generatedSchema, setGeneratedSchema] = useState<any>(null);
  const [showSchemaDialog, setShowSchemaDialog] = useState(false);
  const [processingStep, setProcessingStep] = useState<string>("");
  const [shouldOpenInputDialog, setShouldOpenInputDialog] = useState(false);
  const [hasJustCompletedProcessing, setHasJustCompletedProcessing] =
    useState(false);
  const [showInputMediaDialog, setShowInputMediaDialog] = useState(false);
  const [inputAttachments, setInputAttachments] = useState<any[]>([]);
  const [activeMediaIndex, setActiveMediaIndex] = useState(0);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  const [startTime, setStartTime] = useState<number | null>(null);

  // Update useEffect for more precise timing
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let animationFrameId: number;

    if (isProcessing && startTime) {
      // Use requestAnimationFrame for smoother updates
      const updateTime = () => {
        const elapsed = (Date.now() - startTime) / 1000;
        setElapsedTime(elapsed);
        animationFrameId = requestAnimationFrame(updateTime);
      };
      animationFrameId = requestAnimationFrame(updateTime);
    } else if (!isProcessing && output) {
      // Keep the final elapsed time when processing completes
      if (startTime) {
        const finalElapsed = (Date.now() - startTime) / 1000;
        setElapsedTime(finalElapsed);
      }
    } else {
      setElapsedTime(0);
      setStartTime(null);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isProcessing, startTime, output]);

  const handleProcess = async (attachments: any[]) => {
    if (!input.trim() || attachments.length === 0) {
      snackbar.showErrorMessage(
        "Both a prompt and a file are required for processing"
      );
      return;
    }

    setIsProcessing(true);
    setError(null);
    setOutput(null);
    setGeneratedSchema(null);
    setHasJustCompletedProcessing(false);
    setShouldOpenInputDialog(false);
    setStartTime(Date.now()); // Start tracking time

    try {
      // Check if we have files to generate schema from
      if (attachments.length > 0 && input.trim()) {
        // Schema generation workflow:
        // 1. Generate schema from uploaded file + user prompt
        // 2. Process only the generated schema (not the original files)
        setProcessingStep("Analysing input...");

        // Use the first file for schema generation
        const firstFile = attachments[0].file;
        const schemaResponse = await GenerateSchemaService(
          input.trim(),
          firstFile
        );

        if (!schemaResponse.success) {
          // Enhanced error handling for schema generation
          let errorMessage = "Failed to generate schema";

          if (typeof schemaResponse.data === "string") {
            errorMessage = schemaResponse.data;
          } else if (
            schemaResponse.data &&
            typeof schemaResponse.data === "object"
          ) {
            // Handle error objects like {"detail": "error message"}
            if (schemaResponse.data.detail) {
              errorMessage = schemaResponse.data.detail;
            } else if (schemaResponse.data.message) {
              errorMessage = schemaResponse.data.message;
            } else if (schemaResponse.data.error) {
              errorMessage = schemaResponse.data.error;
            } else {
              errorMessage = JSON.stringify(schemaResponse.data);
            }
          }

          // throw new Error(errorMessage);
        }

        // Validate that we received valid schema data
        if (!schemaResponse.data || typeof schemaResponse.data !== "object") {
          // throw new Error("Invalid schema data received from server");
        }

        setGeneratedSchema(schemaResponse.data);
        setProcessingStep("Processing the input...");

        // Convert schema to the format expected by the processing service
        // Only send the generated schema file, not the original uploaded files
        const schemaFile = new File(
          [JSON.stringify(schemaResponse.data)],
          `schema-${Date.now()}.json`,
          { type: "application/json" }
        );

        // Call the entity extraction service with only the schema file
        const processResponse = await RunGenericEntityExtractionProcessService(
          [],
          [schemaFile]
        );

        if (processResponse.success) {
          setOutput(processResponse.data);
          snackbar.showSuccessMessage("Processing completed successfully!");
          // Mark that we just completed processing successfully
          setHasJustCompletedProcessing(true);
        } else {
          // Enhanced error handling for processing
          let errorMessage = "Processing failed";

          if (typeof processResponse.data === "string") {
            errorMessage = processResponse.data;
          } else if (
            processResponse.data &&
            typeof processResponse.data === "object"
          ) {
            // Handle error objects like {"detail": "error message"}
            if (processResponse.data.detail) {
              errorMessage = processResponse.data.detail;
            } else if (processResponse.data.message) {
              errorMessage = processResponse.data.message;
            } else if (processResponse.data.error) {
              errorMessage = processResponse.data.error;
            } else {
              errorMessage = JSON.stringify(processResponse.data);
            }
          }

          setError(errorMessage);
          snackbar.showErrorMessage(errorMessage);
        }
      } else {
        // Direct JSON processing workflow
        setProcessingStep("Processing JSON input...");

        let inputData;
        try {
          inputData = JSON.parse(input);
        } catch (parseError) {
          // throw new Error("Invalid JSON format. Please check your input.");
        }

        if (!Array.isArray(inputData)) {
          // throw new Error("Input must be an array of objects.");
        }

        // Create input files
        const inputFiles: File[] = [];
        inputData.forEach((item: any) => {
          const file = new File(
            [JSON.stringify(item)],
            `input-${Math.random().toString(36).substring(2, 15)}.json`,
            {
              type: "application/json",
            }
          );
          inputFiles.push(file);
        });

        // Call the API
        const processResponse = await RunGenericEntityExtractionProcessService(
          [],
          inputFiles
        );

        if (processResponse.success) {
          setOutput(processResponse.data);
          snackbar.showSuccessMessage("Processing completed successfully!");
          // Mark that we just completed processing successfully
          setHasJustCompletedProcessing(true);
        } else {
          // Enhanced error handling for processing
          let errorMessage = "Processing failed";

          if (typeof processResponse.data === "string") {
            errorMessage = processResponse.data;
          } else if (
            processResponse.data &&
            typeof processResponse.data === "object"
          ) {
            // Handle error objects like {"detail": "error message"}
            if (processResponse.data.detail) {
              errorMessage = processResponse.data.detail;
            } else if (processResponse.data.message) {
              errorMessage = processResponse.data.message;
            } else if (processResponse.data.error) {
              errorMessage = processResponse.data.error;
            } else {
              errorMessage = JSON.stringify(processResponse.data);
            }
          }

          setError(errorMessage);
          snackbar.showErrorMessage(errorMessage);
        }
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred";

      if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      } else if (error && typeof error === "object") {
        // Handle error objects
        if (error.detail) {
          errorMessage = error.detail;
        } else if (error.response?.data?.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else {
          errorMessage = JSON.stringify(error);
        }
      }

      setError(errorMessage);
      snackbar.showErrorMessage(errorMessage);
      console.error("Processing error:", error);
    } finally {
      setIsProcessing(false);
      setProcessingStep("");
    }
  };

  // Handle view input button click
  const handleViewInput = () => {
    if (inputAttachments.length > 0) {
      setShowInputMediaDialog(!showInputMediaDialog);
    }
  };

  // Convert attachment to MediaDialog format
  const convertAttachmentToMediaDialog = (attachment: any) => {
    return {
      id: 0,
      type: attachment.type,
      title: attachment.name,
      url: attachment.url,
      thumbnail: attachment.url,
      product: {
        name: attachment.name,
        price: attachment.type || "Unknown type",
      },
    };
  };

  // Handle media dialog change
  const handleMediaChange = (index: number) => {
    setActiveMediaIndex(index);
  };

  // Auto-open input dialog only after processing is completely finished and output is displayed
  useEffect(() => {
    if (hasJustCompletedProcessing && !isProcessing && output && !error) {
      // Add a small delay to ensure the UI has fully transitioned to the output state
      const timer = setTimeout(() => {
        setShouldOpenInputDialog(true);
        setHasJustCompletedProcessing(false); // Reset the flag
      }, 500); // 500ms delay to ensure UI transition is complete

      return () => clearTimeout(timer);
    }
  }, [hasJustCompletedProcessing, isProcessing, output, error]);

  // This state would ideally live within PlaygroundInput or be lifted if Playground needs it.
  return (
    <>
      <Head>
        <title>Playground</title>
      </Head>
      <div className="h-full flex flex-col mt-8">
        {/* Header */}
        {/* <div className="flex flex-col md:flex-row justify-between items-start mb-6 animate-fade-in">
          <div className="space-y-2 mb-4 md:mb-0">
            <h1 className="text-3xl font-bold text-gray-900">Playground</h1>
            <p className="text-gray-600 max-w-6xl leading-relaxed">
              Upload a file, enter your prompt, and generate AI-powered
              extraction results. Update your prompt and re-generate as needed.
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => setShowSaveDialog(true)}
              // disabled={!output}
              startIcon={<SaveIcon className="w-4 h-4" />}
              className="bg-gradient-to-r from-sky-600 to-sky-700 hover:from-sky-700 hover:to-sky-800 text-white border-none"
            >
              Save Process
            </Button>
          </div>
        </div> */}

        {/* Dynamic Layout based on output state */}
        {!output && !isProcessing ? (
          /* Centered Input Layout - No Output */
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="flex-1 flex flex-col justify-center py-16 pb-80"
          >
            <div className="w-full max-w-5xl mx-auto px-8">
              <div className="text-center mb-16 mt-8">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, duration: 0.8, ease: "easeOut" }}
                  className="w-18 h-18 bg-gradient-to-br from-sky-600 to-sky-700 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg relative overflow-hidden"
                >
                  {/* Background pulse effect */}
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: [1, 1.2, 1], opacity: [0.3, 0.1, 0.3] }}
                    transition={{
                      delay: 1.5,
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="absolute inset-0 bg-gradient-to-br from-sky-600 to-sky-700 rounded-2xl"
                  />
                  <motion.svg
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.4 }}
                    className="w-10 h-10 text-white relative z-10"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <motion.path
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{
                        delay: 0.8,
                        duration: 0.8,
                        ease: "easeInOut",
                      }}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </motion.svg>
                  {/* Sparkle effects */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      delay: 2,
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      ease: "easeInOut",
                    }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full"
                  />
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      rotate: [0, -180, -360],
                    }}
                    transition={{
                      delay: 2.5,
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      ease: "easeInOut",
                    }}
                    className="absolute -bottom-1 -left-1 w-2 h-2 bg-white rounded-full"
                  />
                </motion.div>
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: 1,
                    y: [0, -2, 0],
                  }}
                  transition={{
                    delay: 0.4,
                    duration: 0.5,
                    y: {
                      delay: 2,
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    },
                  }}
                  className="text-3xl font-semibold text-gray-900 mb-4"
                >
                  Ready to Extract Intelligence
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: 1,
                    y: [0, -1, 0],
                  }}
                  transition={{
                    delay: 0.6,
                    duration: 0.5,
                    y: {
                      delay: 2.5,
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    },
                  }}
                  className="text-gray-600 text-lg max-w-md mx-auto leading-relaxed"
                >
                  Upload your file and describe what you want to extract. Our AI
                  will analyze your data and generate structured results.
                </motion.p>
              </div>
            </div>
          </motion.div>
        ) : isProcessing ? (
          /* Processing State - Consistent with initial layout */
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="flex-1 flex flex-col justify-center py-16 pb-80"
          >
            <div className="w-full max-w-5xl mx-auto px-8">
              <div className="text-center mb-16 mt-8">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, duration: 0.6, ease: "easeOut" }}
                  className="w-18 h-18 bg-gradient-to-br from-sky-600 to-sky-700 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                    className="w-8 h-8 border-3 border-white border-t-transparent rounded-full"
                  />
                </motion.div>
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                  className="text-3xl font-semibold text-gray-900 mb-4"
                >
                  {processingStep || "Processing..."}
                </motion.h2>
                {/* <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                  className="flex items-center justify-center gap-2 mb-6"
                >
                  <span className="flex items-center gap-2 bg-gray-100 rounded-md p-2">
                    <Clock className="w-5 h-5 text-sky-600" />
                    <span className="font-mono text-lg text-sky-600 tabular-nums tracking-wider px-2 w-[120px] text-center">
                      {formatProcessingTime(elapsedTime)}
                    </span>
                  </span>
                </motion.div> */}
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                  className="text-gray-600 text-lg max-w-md mx-auto leading-relaxed mb-6"
                >
                  AI is analyzing your input and generating extraction results.
                </motion.p>
                <motion.div
                  initial={{ opacity: 0, scaleX: 0 }}
                  animate={{ opacity: 1, scaleX: 1 }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                  className="w-64 bg-gray-200 rounded-full h-2 mx-auto overflow-hidden"
                >
                  <motion.div
                    animate={{ x: ["-100%", "100%"] }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                    className="w-1/2 bg-gradient-to-r from-sky-600 to-sky-700 h-2 rounded-full"
                  />
                </motion.div>
              </div>
            </div>
          </motion.div>
        ) : (
          /* Full Page Output with Sticky Input */
          <div className="flex-1 flex flex-col min-h-0">
            {/* Output Section - Full Width */}
            <div className="flex-1 flex flex-col min-h-0 pb-[200px]">
              <div className="mb-8 flex items-center justify-between">
                {/* <div>
                  <h2 className="text-3xl font-bold text-gray-900">Output</h2>
                </div> */}
                {/* <Button
                  variant="outline"
                  size="default"
                  onClick={() => {
                    setOutput(null);
                    setError(null);
                    setGeneratedSchema(null);
                  }}
                  className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white transition-all duration-200"
                >
                  Clear Results
                </Button> */}
              </div>
              <div className="flex-1 min-h-0">
                <PlaygroundOutput
                  response={output}
                  isProcessing={isProcessing}
                  error={error}
                  processingStep={processingStep}
                  onViewInput={handleViewInput}
                  showInputMediaDialog={showInputMediaDialog}
                  elapsedTime={elapsedTime}
                  formatElapsedTime={(s) => formatElapsedTime(s, false)}
                />
              </div>
            </div>
          </div>
        )}

        {/* Fixed Input Section at Bottom - Always Present */}
        <div
          className="fixed bottom-0 left-0 md:left-[262px] right-4 z-10 bg-white mt-2 shadow-none"
          style={{ boxShadow: "none" }}
        >
          <div
            className="py-6 pb-3 px-6 flex justify-center shadow-none"
            style={{ boxShadow: "none" }}
          >
            <div
              className="w-full max-w-7xl shadow-none"
              style={{ boxShadow: "none" }}
            >
              <PlaygroundInput
                value={input}
                onChange={setInput}
                onProcess={handleProcess}
                isProcessing={isProcessing}
                processingStep={processingStep}
                generatedSchema={generatedSchema}
                hasCompletedProcessing={!!output && !isProcessing}
                hasOutput={!!output || isProcessing}
                shouldOpenInputDialog={shouldOpenInputDialog}
                onInputDialogOpened={() => setShouldOpenInputDialog(false)}
                onAttachmentsChange={setInputAttachments}
              />
            </div>
          </div>
        </div>

        <ProcessSaveDialog
          open={showSaveDialog}
          onOpenChange={setShowSaveDialog}
          processInput={input}
          processOutput={output}
        />

        {/* Schema Viewing Dialog */}
        {showSchemaDialog && generatedSchema && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto m-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Generated Schema
                </h3>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        JSON.stringify(generatedSchema, null, 2)
                      );
                      snackbar.showSuccessMessage(
                        "Schema copied to clipboard!"
                      );
                    }}
                    startIcon={<Copy className="w-3 h-3" />}
                  >
                    Copy
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const jsonData = JSON.stringify(generatedSchema, null, 2);
                      const blob = new Blob([jsonData], {
                        type: "application/json",
                      });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement("a");
                      a.href = url;
                      a.download = "generated-schema.json";
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    }}
                    startIcon={<Download className="w-3 h-3" />}
                  >
                    Download
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSchemaDialog(false)}
                  >
                    ✕
                  </Button>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                  {JSON.stringify(generatedSchema, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* Input Media Dialog */}
        {showInputMediaDialog && inputAttachments.length > 0 && (
          <MediaDialog
            mediaItems={inputAttachments.map(convertAttachmentToMediaDialog)}
            activeIndex={activeMediaIndex}
            onMediaChange={handleMediaChange}
            onClose={() => setShowInputMediaDialog(false)}
            showNavigation={inputAttachments.length > 1}
            defaultMinimized={false}
          />
        )}
      </div>
    </>
  );
}

export const getServerSideProps = withAuth();
