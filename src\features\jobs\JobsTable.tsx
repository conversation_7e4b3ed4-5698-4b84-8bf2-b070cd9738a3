import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { GetJobsService, DeleteJobService } from "@/services/jobs.service";
import { GetUsersService } from "@/services/users.service";
import JobDialog from "./JobDialog";
import ConfirmationDialog from "@/components/custom/ConfirmationDialog";
import { snackbar } from "@/utils/snackbar.util";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { useAppSelector } from "@/redux";
import {
  LoaderCircle,
  Search,
  Users,
  Plus,
  Briefcase,
  Pencil,
  Trash2,
  Settings,
  X,
  CalendarIcon,
  ClipboardList,
  FileText,
  Mic,
  Image,
  CheckCircle,
  Clock,
  AlertCircle,
  RotateCw,
  User,
} from "lucide-react";
import { Pagination } from "@/components/custom/Pagination";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Checkbox } from "@/components/ui/checkbox";
import TextField from "@/components/custom/TextField";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import BulkAssignDialog from "./BulkAssignDialog";
import AssigneeDropdown from "./AssigneeDropdown";
import Button from "@/components/custom/Button";

// Types
type JobWithAssignee = Job & {
  assigned_to?: string;
  assigned_to_name?: string;
};

type Meta = {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
};

interface JobsTableProps {
  projectId: string;
  projectDetails: { name?: string } | null;
  onJobCountChange?: () => void; // Callback to refresh project status counts
  onJobSelectionChange?: (selectedJobs: Set<string>) => void;
}

export default function JobsTable({
  projectId,
  projectDetails,
  onJobCountChange,
  onJobSelectionChange,
}: JobsTableProps) {
  const router = useRouter();

  // Redux state
  const processes = useAppSelector((state) => state.processes);

  const getQueryNum = (key: string, fallback: number = 1) =>
    typeof router.query[key] === "string"
      ? parseInt(router.query[key])
      : fallback;

  // Helper function to get process label from process name
  const getProcessLabel = (processName: string) => {
    const processKey = Object.keys(processes).find(
      (key) => (processes as any)[key].name === processName
    );
    return processKey
      ? (processes as any)[processKey].label
      : processName || "N/A";
  };

  // Helper function to get job type icon
  const getJobTypeIcon = (processName: string) => {
    const lowerProcessName = (processName || "").toLowerCase();

    if (
      lowerProcessName.includes("legal") ||
      lowerProcessName.includes("document")
    ) {
      return <ClipboardList className="h-5 w-5 text-green-600" />;
    }
    if (
      lowerProcessName.includes("audio") ||
      lowerProcessName.includes("transcribe")
    ) {
      return <Mic className="h-5 w-5 text-purple-600" />;
    }
    if (
      lowerProcessName.includes("image") ||
      lowerProcessName.includes("extract-image")
    ) {
      return <Image className="h-5 w-5 text-blue-600" />;
    }
    if (
      lowerProcessName.includes("entity") ||
      lowerProcessName.includes("extraction")
    ) {
      return <FileText className="h-5 w-5 text-orange-600" />;
    }
    return <Briefcase className="h-5 w-5 text-gray-600" />;
  };

  // Helper function to get status icon and color
  const getStatusDetails = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case "completed":
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          bgColor: "bg-green-500",
          textColor: "text-white",
          text: "COMPLETED",
        };
      case "in-progress":
      case "processing":
        return {
          icon: <RotateCw className="h-4 w-4" />,
          bgColor: "bg-blue-500",
          textColor: "text-white",
          text: "PROCESSING",
        };
      case "pending":
        return {
          icon: <Clock className="h-4 w-4" />,
          bgColor: "bg-yellow-500",
          textColor: "text-white",
          text: "PENDING",
        };
      case "failed":
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          bgColor: "bg-red-500",
          textColor: "text-white",
          text: "FAILED",
        };
      default:
        return {
          icon: <Clock className="h-4 w-4" />,
          bgColor: "bg-gray-500",
          textColor: "text-white",
          text: status.toUpperCase(),
        };
    }
  };

  // Helper function to format username
  const formatUsername = (username?: string) => {
    if (!username) return "Unassigned";
    return username
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [jobs, setJobs] = useState<JobWithAssignee[]>([]);
  const [agents, setAgents] = useState<User[]>([]);
  const [meta, setMeta] = useState<Meta>({
    page: getQueryNum("page", 1),
    limit: getQueryNum("limit", 10),
    total: 0,
    total_pages: 0,
  });

  // Selection and assignment states
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());
  const [bulkAssignDialogOpen, setBulkAssignDialogOpen] = useState(false);
  const [newJobDialogOpen, setNewJobDialogOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<JobWithAssignee | null>(null);

  // Edit and Delete states
  const [editJobDialogOpen, setEditJobDialogOpen] = useState(false);
  const [deleteJobDialogOpen, setDeleteJobDialogOpen] = useState(false);
  const [jobToEdit, setJobToEdit] = useState<JobWithAssignee | null>(null);
  const [jobToDelete, setJobToDelete] = useState<JobWithAssignee | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Filter and search states - initialize from URL
  const [searchTerm, setSearchTerm] = useState(
    (router.query.search as string) || ""
  );
  const [statusFilter, setStatusFilter] = useState(
    (router.query.status as string) || ""
  );
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    const startDate = router.query.created_at_start as string;
    const endDate = router.query.created_at_end as string;
    return {
      from: startDate ? new Date(startDate) : undefined,
      to: endDate ? new Date(endDate) : undefined,
    };
  });

  const updateUrl = (params: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    created_at_start?: string;
    created_at_end?: string;
  }) => {
    const query: any = { ...router.query, ...params };
    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null) delete query[key];
    });
    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  const fetchJobs = async (
    page: number,
    limit: number,
    isInitial = false,
    silent = false
  ) => {
    if (isInitial) setInitialLoading(true);
    else if (!silent) setLoading(true);

    const response = await GetJobsService(
      projectId,
      page,
      limit,
      dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : null,
      dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : null,
      searchTerm || null,
      statusFilter || null
    );

    if (response.success) {
      setJobs(response.data.data);
      setMeta(response.data.meta);
    } else {
      snackbar.showErrorMessage(response.data);
    }

    isInitial ? setInitialLoading(false) : !silent && setLoading(false);
  };

  const fetchAgents = async () => {
    const response = await GetUsersService(1, 100); // Get all users for assignment
    if (response.success) {
      setAgents(response.data.data);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchJobs(meta.page, meta.limit, true);
      fetchAgents();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  useEffect(() => {
    if (projectId && !initialLoading) {
      fetchJobs(meta.page, meta.limit);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta.page, meta.limit]);

  // Watch for URL changes (when status cards are clicked)
  useEffect(() => {
    const urlSearch = (router.query.search as string) || "";
    const urlStatus = (router.query.status as string) || "";
    const urlStartDate = router.query.created_at_start as string;
    const urlEndDate = router.query.created_at_end as string;

    if (urlSearch !== searchTerm) {
      setSearchTerm(urlSearch);
    }
    if (urlStatus !== statusFilter) {
      setStatusFilter(urlStatus);
    }

    // Update date range if URL params change
    const newDateRange = {
      from: urlStartDate ? new Date(urlStartDate) : undefined,
      to: urlEndDate ? new Date(urlEndDate) : undefined,
    };

    if (
      newDateRange.from?.getTime() !== dateRange?.from?.getTime() ||
      newDateRange.to?.getTime() !== dateRange?.to?.getTime()
    ) {
      setDateRange(newDateRange);
    }
  }, [
    router.query.search,
    router.query.status,
    router.query.created_at_start,
    router.query.created_at_end,
  ]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (projectId && !initialLoading) {
        fetchJobs(1, meta.limit);
        setMeta((prev) => ({ ...prev, page: 1 }));
      }
    }, 500);

    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, statusFilter, dateRange]);

  const handleChangeRowsPerPage = (newLimit: number) => {
    setMeta({ ...meta, page: 1, limit: newLimit });
    updateUrl({ limit: newLimit, page: 1 });
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateUrl({ search: value || undefined, page: 1 });
  };

  const handleStatusFilterChange = (value: string) => {
    const newStatus = value === "all" ? "" : value;
    setStatusFilter(newStatus);
    updateUrl({ status: newStatus || undefined, page: 1 });
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setDateRange(undefined);
    updateUrl({
      search: undefined,
      status: undefined,
      created_at_start: undefined,
      created_at_end: undefined,
      page: 1,
    });
  };

  // Check if any filters are active
  const hasActiveFilters =
    searchTerm || statusFilter || dateRange?.from || dateRange?.to;

  const handleJobSaved = (silent = false) => {
    fetchJobs(meta.page, meta.limit, false, silent);
    const emptySelection = new Set<string>();
    setSelectedJobs(emptySelection); // Clear selection after refresh
    onJobSelectionChange?.(emptySelection); // Notify parent of cleared selection

    // Always update project status counts, but silently for assignment changes
    onJobCountChange?.();
  };

  const handleJobRemoved = (jobId: string) => {
    setSelectedJobs((prev) => {
      const newSet = new Set(prev);
      newSet.delete(jobId);
      return newSet;
    });
  };

  const handleEditJob = (job: JobWithAssignee) => {
    setJobToEdit(job);
    setEditJobDialogOpen(true);
  };

  const handleDeleteJob = (job: JobWithAssignee) => {
    setJobToDelete(job);
    setDeleteJobDialogOpen(true);
  };

  const confirmDeleteJob = async () => {
    if (!jobToDelete) return;

    setDeleting(true);
    try {
      const response = await DeleteJobService(jobToDelete._id);

      if (response.success) {
        snackbar.showSuccessMessage("Job deleted successfully");
        handleJobSaved(); // Refresh the jobs list and update counts
      } else {
        snackbar.showErrorMessage(response.data || "Failed to delete job");
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to delete job");
    } finally {
      setDeleting(false);
      setDeleteJobDialogOpen(false);
      setJobToDelete(null);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked
      ? new Set(jobs.map((job) => job._id))
      : new Set<string>();
    setSelectedJobs(newSelection);
    onJobSelectionChange?.(newSelection);
  };

  const handleSelectJob = (jobId: string, checked: boolean) => {
    const newSelection = new Set(selectedJobs);
    if (checked) {
      newSelection.add(jobId);
    } else {
      newSelection.delete(jobId);
    }
    setSelectedJobs(newSelection);
    onJobSelectionChange?.(newSelection);
  };

  const isAllSelected = jobs.length > 0 && selectedJobs.size === jobs.length;
  const isIndeterminate =
    selectedJobs.size > 0 && selectedJobs.size < jobs.length;

  // Job Card Component
  const JobCard = ({ job }: { job: JobWithAssignee }) => {
    const statusDetails = getStatusDetails(job.status);
    const jobIcon = getJobTypeIcon(job.process_name || "");

    return (
      <div className="bg-white border border-gray-200 rounded-md p-4 hover:shadow-md transition-all duration-200 cursor-pointer">
        <div className="flex items-start gap-4">
          {/* Checkbox */}
          <div className="mt-1" onClick={(e) => e.stopPropagation()}>
            <Checkbox
              checked={selectedJobs.has(job._id)}
              onCheckedChange={(checked) =>
                handleSelectJob(job._id, checked as boolean)
              }
              aria-label={`Select ${job.name}`}
              className="data-[state=checked]:bg-sky-600 data-[state=checked]:border-sky-600 border-2"
            />
          </div>

          {/* Job Icon */}
          <div className="flex-shrink-0 mt-1">{jobIcon}</div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              {/* Left side: Title and meta */}
              <div className="flex-1 min-w-0">
                <h3
                  className="text-base font-semibold text-gray-900 truncate hover:text-sky-600 transition-colors"
                  title={job.name}
                  onClick={() =>
                    router.push(`/projects/${projectId}/job/${job._id}`)
                  }
                >
                  {job.name}
                </h3>

                <div className="mt-2 flex items-center flex-wrap gap-x-4 gap-y-2 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <CalendarIcon className="h-4 w-4" />
                    {format(new Date(job.created_at), "MMM dd, yyyy")}
                  </span>
                  <span className="flex items-center gap-1">
                    <Settings className="h-4 w-4" />
                    {getProcessLabel(job.process_name || "")}
                  </span>
                  <span className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {formatUsername(job.assigned_to_name)}
                  </span>
                </div>
              </div>

              {/* Right side: Status and Actions */}
              <div className="flex items-start gap-3">
                {/* Status Badge */}
                <div
                  className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium ${statusDetails.bgColor} ${statusDetails.textColor}`}
                >
                  {statusDetails.icon}
                  {statusDetails.text}
                </div>

                {/* View Details Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(`/projects/${projectId}/job/${job._id}`);
                  }}
                  className="text-sky-600 hover:text-sky-700 hover:bg-sky-50 border-sky-200"
                >
                  View Details
                </Button>
              </div>
            </div>

            {/* Assignment and Actions Row */}
            <div className="mt-3 flex items-center justify-between">
              <div className="flex-1" onClick={(e) => e.stopPropagation()}>
                <AssigneeDropdown
                  job={job}
                  agents={agents}
                  onAssignmentChange={() => handleJobSaved(true)}
                />
              </div>

              <div
                className="flex items-center gap-1 ml-4"
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditJob(job)}
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  title="Edit Job"
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteJob(job)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  title="Delete Job"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Loading Card Skeleton
  const JobCardSkeleton = () => (
    <div className="bg-white border border-gray-200 rounded-md p-4">
      <div className="flex items-start gap-4">
        <Skeleton className="h-4 w-4 mt-1" />
        <Skeleton className="h-5 w-5 mt-1" />
        <div className="flex-1">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1">
              <Skeleton className="h-5 w-3/4 mb-2" />
              <div className="flex items-center gap-4">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-8 w-24 rounded-md" />
            </div>
          </div>
          <div className="mt-3 flex items-center justify-between">
            <Skeleton className="h-8 w-32 rounded-md" />
            <div className="flex items-center gap-1">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* New Job Dialog */}
      <JobDialog
        open={newJobDialogOpen}
        setOpen={setNewJobDialogOpen}
        selectedProject={selectedJob as any}
        setSelectedProject={setSelectedJob as any}
        onProjectCreated={handleJobSaved}
        projectId={projectId}
      />

      {/* Edit Job Dialog */}
      <JobDialog
        open={editJobDialogOpen}
        setOpen={setEditJobDialogOpen}
        selectedProject={jobToEdit as any}
        setSelectedProject={setJobToEdit as any}
        onProjectCreated={handleJobSaved}
        projectId={projectId}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteJobDialogOpen}
        onOpenChange={setDeleteJobDialogOpen}
        title="Delete Job"
        description={`Are you sure you want to delete "${jobToDelete?.name}"? This action cannot be undone.`}
        confirmLabel="Delete Job"
        onConfirm={confirmDeleteJob}
        loading={deleting}
        variant="destructive"
      />

      <BulkAssignDialog
        open={bulkAssignDialogOpen}
        setOpen={setBulkAssignDialogOpen}
        selectedJobIds={Array.from(selectedJobs)}
        agents={agents}
        onAssignmentComplete={() => handleJobSaved(true)}
        projectId={projectId}
        onJobRemoved={handleJobRemoved}
      />

      <div className="bg-white rounded-md border border-red-200 shadow-sm">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div>
                <h2 className="text-xl font-bold text-gray-800 flex items-center gap-3">
                  <div className="w-2 h-2 bg-sky-500 rounded-full"></div>
                  Jobs
                </h2>
                <p className="text-md text-gray-500 font-medium">
                  Manage and track all jobs for this project
                </p>
              </div>
            </div>
            <Button
              onClick={() => {
                setNewJobDialogOpen(true);
                setSelectedJob(null);
              }}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Job
            </Button>
          </div>

          {/* Filters Row - Left aligned */}
          <div className="flex flex-col lg:flex-row gap-4 lg:items-center">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* Search Input - Leftmost */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
                <TextField
                  type="text"
                  placeholder="Search jobs..."
                  className="h-10 pl-10 w-full sm:w-64"
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
              </div>

              {/* Date Range Picker */}
              <DateRangePicker
                className="w-64"
                date={dateRange}
                onDateChange={(range) => {
                  setDateRange(range);
                  // Only make API call and update URL when both dates are selected or cleared
                  if (
                    (range?.from && range?.to) ||
                    (!range?.from && !range?.to)
                  ) {
                    setMeta((prev) => ({ ...prev, page: 1 }));
                    updateUrl({
                      created_at_start: range?.from
                        ? format(range.from, "yyyy-MM-dd")
                        : undefined,
                      created_at_end: range?.to
                        ? format(range.to, "yyyy-MM-dd")
                        : undefined,
                      page: 1,
                    });
                  }
                }}
                placeholder="Filter by date"
                numberOfMonths={2}
                align="start"
                disableFuture={true}
              />

              {/* Status Filter */}
              <Select
                value={statusFilter || "all"}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger className="h-10 w-full sm:w-48" size="default">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="timeout">Timeout</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="partially-completed">
                    Partially Completed
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Clear filters button */}
              {hasActiveFilters && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={handleClearFilters}
                      variant="outline"
                      size="icon"
                      className="h-10 w-10"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear filters</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>

            {/* Bulk assignment button - Right side */}
            {selectedJobs.size > 0 && (
              <Button
                onClick={() => setBulkAssignDialogOpen(true)}
                variant="outline"
                className="h-10 whitespace-nowrap"
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Assignments ({selectedJobs.size})
              </Button>
            )}
          </div>
        </div>

        {/* Jobs Cards */}
        <div className="relative">
          {/* Select All Header */}
          <div className="mb-4 flex items-center gap-3 px-2">
            <Checkbox
              checked={isAllSelected}
              onCheckedChange={handleSelectAll}
              aria-label="Select all jobs"
              className="data-[state=checked]:bg-sky-600 data-[state=checked]:border-sky-600 border-2"
              {...(isIndeterminate && {
                "data-state": "indeterminate",
              })}
            />
            <span className="text-sm text-gray-600">
              {selectedJobs.size > 0
                ? `${selectedJobs.size} of ${jobs.length} jobs selected`
                : `Select all ${jobs.length} jobs`}
            </span>
          </div>

          {/* Cards Container */}
          <div className="space-y-4">
            {initialLoading || (loading && jobs.length === 0) ? (
              Array.from({ length: 5 }).map((_, i) => (
                <JobCardSkeleton key={i} />
              ))
            ) : jobs.length > 0 ? (
              jobs.map((job) => <JobCard key={job._id} job={job} />)
            ) : (
              <div className="text-center py-12 bg-white border border-gray-200 rounded-md">
                <div className="text-gray-400 mb-4">
                  <Briefcase className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No jobs found
                </h3>
                <p className="text-gray-500 mb-4">
                  {hasActiveFilters
                    ? "Try adjusting your search or filters"
                    : "Get started by creating your first job"}
                </p>
                {!hasActiveFilters && (
                  <Button
                    onClick={() => {
                      setNewJobDialogOpen(true);
                      setSelectedJob(null);
                    }}
                    className="btn-primary"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Job
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Pagination */}
          {meta.total > 0 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                page={meta.page - 1} // Pagination component uses 0-based indexing
                count={meta.total}
                rowsPerPage={meta.limit}
                onPageChange={(newPage) => {
                  const adjustedPage = newPage + 1; // Convert back to 1-based
                  setMeta((prev) => ({ ...prev, page: adjustedPage }));
                  updateUrl({ page: adjustedPage });
                }}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </div>
          )}

          {/* Content Loading Overlay - only when there are existing jobs */}
          {loading && !initialLoading && jobs.length > 0 && (
            <div className="absolute inset-0 bg-white/75 flex justify-center items-center rounded-lg">
              <div className="bg-white rounded-lg p-4 shadow-lg">
                <LoaderCircle className="w-8 h-8 animate-spin text-blue-600" />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
