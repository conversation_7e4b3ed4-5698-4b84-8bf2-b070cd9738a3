import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Calendar,
  Pencil,
  Play,
  RotateCcw,
  Settings,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import {
  ExecuteJobService,
  GetJobStatusService,
  DeleteJobService,
  GetJobOutputVerificationCountService,
} from "@/services/jobs.service";
import { GetUsersService } from "@/services/users.service";
import { snackbar } from "@/utils/snackbar.util";
import { motion } from "framer-motion";
import JobDialog from "./JobDialog";
import ConfirmationDialog from "@/components/custom/ConfirmationDialog";
import AssigneeDropdown from "./AssigneeDropdown";
import Button from "@/components/custom/Button";
import { format } from "date-fns";

type JobDetailProps = {
  jobDetails: Job;
  onJobUpdated?: () => void;
  onJobStatusChange?: (newStatus: string) => void;
};

export default function JobDetail({
  jobDetails,
  onJobUpdated,
  onJobStatusChange,
}: JobDetailProps) {
  const router = useRouter();
  const {
    name,
    description,
    status,
    created_at,
    updated_at,
    project_id,
    process_name,
    created_by,
    _id,
  } = jobDetails;

  const [jobStatus, setJobStatus] = useState(status);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isReExecuting, setIsReExecuting] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [agents, setAgents] = useState<User[]>([]);
  const [verificationCounts, setVerificationCounts] = useState<{
    verified: number;
    unverified: number;
    total: number;
  } | null>(null);
  const [verificationLoading, setVerificationLoading] = useState(true);

  const fetchAgents = async () => {
    const response = await GetUsersService(1, 100);
    if (response.success) {
      setAgents(response.data.data);
    }
  };

  const fetchVerificationCounts = async () => {
    setVerificationLoading(true);
    try {
      const counts = await GetJobOutputVerificationCountService(_id);
      setVerificationCounts(counts);
    } catch (error) {
      console.error("Failed to fetch verification counts:", error);
    } finally {
      setVerificationLoading(false);
    }
  };

  useEffect(() => {
    fetchAgents();
    fetchVerificationCounts();
  }, [_id, created_by]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-700";
      case "pending":
        return "bg-yellow-100 text-yellow-700";
      case "failed":
        return "bg-red-100 text-red-700";
      case "running":
      case "in-progress":
        return "bg-blue-100 text-blue-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const executeJob = async (jobId: string, isReExec = false) => {
    isReExec ? setIsReExecuting(true) : setIsExecuting(true);
    setJobStatus("in-progress");
    const result = await ExecuteJobService(jobId);
    if (result.success) {
      pollJobStatus(jobId);
    } else {
      snackbar.showErrorMessage(result.data);
      isReExec ? setIsReExecuting(false) : setIsExecuting(false);
    }
  };

  const pollJobStatus = (jobId: string) => {
    const interval = setInterval(async () => {
      const res = await GetJobStatusService(jobId);
      if (res.success) {
        const status = res.data.status;
        setJobStatus(status);
        onJobStatusChange?.(status);
        if (status !== "in-progress") {
          clearInterval(interval);
          setIsExecuting(false);
          setIsReExecuting(false);
          snackbar.showSuccessMessage("Job finished with status: " + status);
        }
      } else {
        clearInterval(interval);
        setIsExecuting(false);
        setIsReExecuting(false);
        snackbar.showErrorMessage("Failed to get job status");
      }
    }, 2500);
  };

  const handleDeleteJob = async () => {
    console.log("Deleting job:", _id);
    setDeleting(true);
    try {
      const response = await DeleteJobService(_id);
      if (response.success) {
        snackbar.showSuccessMessage("Job deleted successfully");
        router.push(`/projects/${project_id}`);
      } else {
        snackbar.showErrorMessage(response.data || "Failed to delete job");
      }
    } catch (error) {
      console.error("Delete job error:", error);
      snackbar.showErrorMessage("Failed to delete job");
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  const handleJobUpdated = () => {
    console.log("Job updated, refreshing data");
    onJobUpdated?.();
    fetchVerificationCounts();
  };

  const renderActionButtons = () => {
    const currentStatus = jobStatus.toLowerCase();
    const isPending = currentStatus === "pending";

    return (
      <div className="flex items-center gap-2">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            variant="outline"
            size="icon"
            className="border-sky-300 text-sky-600 hover:bg-sky-50 hover:border-sky-400 focus:ring-sky-400 transition-colors"
            onClick={() => {
              console.log("Edit button clicked for job:", name);
              setEditDialogOpen(true);
            }}
            title="Edit Job"
          >
            <Pencil className="w-4 h-4" />
          </Button>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            variant="outline"
            size="icon"
            className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 focus:ring-red-400 transition-colors"
            onClick={() => {
              console.log("Delete button clicked for job:", name);
              setDeleteDialogOpen(true);
            }}
            title="Delete Job"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            disabled={isExecuting || isReExecuting}
            onClick={() => executeJob(_id, !isPending)}
            className={`${
              isPending
                ? "bg-green-600 hover:bg-green-700 focus:ring-green-600"
                : "bg-amber-600 hover:bg-amber-700 focus:ring-amber-600"
            } text-white transition-colors`}
          >
            {isExecuting || isReExecuting ? (
              <>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    rotate: 360,
                  }}
                  transition={{
                    opacity: { duration: 0.3, ease: "easeOut" },
                    scale: { duration: 0.3, ease: "easeOut" },
                    rotate: { duration: 1, ease: "linear", repeat: Infinity },
                  }}
                  className="w-4 h-4 mr-2 border-2 border-current border-t-transparent rounded-full"
                />
                Executing
              </>
            ) : isPending ? (
              <>
                <Play className="w-4 h-4 mr-2" />
                Execute
              </>
            ) : (
              <>
                <RotateCcw className="w-4 h-4 mr-2" />
                Re-execute
              </>
            )}
          </Button>
        </motion.div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="flex flex-col"
    >
      {/* Header Row: Icon, Title/Status, Action Buttons */}
      <div className="flex items-center justify-between mb-6">
        {/* Icon and Title/Status */}
        <div className="flex items-center gap-4">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.6, ease: "easeOut" }}
            className="w-16 h-16 bg-gradient-to-br from-sky-100 to-sky-200 rounded-xl flex items-center justify-center flex-shrink-0"
          >
            <Settings className="h-8 w-8 text-sky-600" />
          </motion.div>
          <div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="mb-2"
            >
              <h1 className="text-3xl font-bold text-gray-900">{name}</h1>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="flex items-center gap-3"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  delay: 0.5,
                  duration: 0.3,
                  type: "spring",
                  stiffness: 200,
                }}
              >
                <Badge className={`${getStatusColor(jobStatus)} font-medium`}>
                  {jobStatus.toUpperCase()}
                </Badge>
              </motion.div>
              {process_name && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                  className="text-sm text-gray-600"
                >
                  • {process_name}
                </motion.div>
              )}
            </motion.div>
          </div>
        </div>
        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.9, duration: 0.5 }}
          className="ml-auto"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.0, duration: 0.3 }}
          >
            {renderActionButtons()}
          </motion.div>
        </motion.div>
      </div>

      {/* Description */}
      {description && (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
          className="text-lg text-gray-600 leading-relaxed mb-6"
        >
          {description}
        </motion.p>
      )}

      {/* Info Cards Grid - Aligned with Icon */}
      <motion.div
        initial={{ opacity: 0, y: 15 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.5 }}
        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      >
        {/* Assigned To Card */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ duration: 0.2 }}
          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex items-center gap-2 mb-2">
            <UserCheck className="h-4 w-4 text-gray-400" />
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
              Assigned To
            </span>
          </div>
          <div className="text-sm">
            <AssigneeDropdown
              job={
                jobDetails as Job & {
                  assigned_to?: string;
                  assigned_to_name?: string;
                }
              }
              agents={agents}
              onAssignmentChange={() => onJobUpdated?.()}
            />
          </div>
        </motion.div>

        {/* Created At Card */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ duration: 0.2 }}
          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
              Created
            </span>
          </div>
          <div className="text-sm font-semibold text-gray-900">
            {format(new Date(created_at), "MMM dd, yyyy")}
          </div>
        </motion.div>

        {/* Updated At Card */}
        {updated_at && (
          <motion.div
            whileHover={{ scale: 1.02, y: -2 }}
            transition={{ duration: 0.2 }}
            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Updated
              </span>
            </div>
            <div className="text-sm font-semibold text-gray-900">
              {format(new Date(updated_at), "MMM dd, yyyy")}
            </div>
          </motion.div>
        )}

        {/* Outputs Verified Card */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ duration: 0.2 }}
          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex items-center gap-2 mb-2">
            <UserCheck className="h-4 w-4 text-gray-400" />
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
              Outputs Verified
            </span>
          </div>
          <div className="text-sm font-semibold text-gray-900">
            {verificationLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-12"></div>
              </div>
            ) : verificationCounts ? (
              <span>
                {verificationCounts.verified}/{verificationCounts.total}
              </span>
            ) : (
              "0/0"
            )}
          </div>
        </motion.div>
      </motion.div>

      {/* Dialogs */}
      <JobDialog
        open={editDialogOpen}
        setOpen={setEditDialogOpen}
        selectedProject={jobDetails}
        setSelectedProject={() => {}}
        onProjectCreated={handleJobUpdated}
        projectId={project_id}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Job"
        description={`Are you sure you want to delete "${name}"? This action cannot be undone.`}
        confirmLabel="Delete Job"
        onConfirm={handleDeleteJob}
        loading={deleting}
        variant="destructive"
      />
    </motion.div>
  );
}

export function JobDetailSkeleton() {
  return (
    <div className="flex flex-col">
      {/* Header Row: Icon, Title/Status, Action Buttons */}
      <div className="flex items-center justify-between mb-6">
        {/* Icon and Title/Status */}
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gradient-to-br from-sky-100 to-sky-200 rounded-xl flex items-center justify-center flex-shrink-0">
            <Settings className="h-8 w-8 text-sky-600" />
          </div>
          <div>
            <div className="mb-2">
              <Skeleton className="h-8 w-64" />
            </div>
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>
        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-auto">
          <Skeleton className="h-10 w-10 rounded-md" />
          <Skeleton className="h-10 w-10 rounded-md" />
          <Skeleton className="h-10 w-24 rounded-md" />
        </div>
      </div>

      {/* Description */}
      <div className="mb-6 space-y-2">
        <Skeleton className="h-5 w-full" />
        <Skeleton className="h-5 w-3/4" />
      </div>

      {/* Info Cards Grid - Aligned with Icon */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {/* Assigned To Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <UserCheck className="h-4 w-4 text-gray-400" />
            <Skeleton className="h-3 w-16" />
          </div>
          <Skeleton className="h-8 w-full rounded-md" />
        </div>
        {/* Created At Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <Skeleton className="h-3 w-12" />
          </div>
          <Skeleton className="h-4 w-20" />
        </div>
        {/* Updated At Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <Skeleton className="h-3 w-12" />
          </div>
          <Skeleton className="h-4 w-20" />
        </div>
        {/* Outputs Verified Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <UserCheck className="h-4 w-4 text-gray-400" />
            <Skeleton className="h-3 w-20" />
          </div>
          <Skeleton className="h-4 w-12" />
        </div>
      </div>
    </div>
  );
}
