import React from "react";
import Button from "@/components/custom/Button";
import { Search } from "lucide-react";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import TextField from "@/components/custom/TextField";
import { DateRange } from "react-day-picker";

interface ProjectsFiltersBarProps {
  searchQuery: string;
  dateRange: DateRange | undefined;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onDateRangeChange: (range: DateRange | undefined) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

const ProjectsFiltersBar: React.FC<ProjectsFiltersBarProps> = React.memo(
  ({
    searchQuery,
    dateRange,
    onSearchChange,
    onDateRangeChange,
    onClearFilters,
    hasActiveFilters,
  }) => {
    return (
      <div className="flex items-center gap-4 mb-8">
        <div className="relative w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <TextField
            placeholder="Search projects..."
            value={searchQuery}
            onChange={onSearchChange}
            className="pl-10 h-10 w-full"
          />
        </div>
        <DateRangePicker
          className="w-64"
          date={dateRange}
          onDateChange={onDateRangeChange}
          placeholder="Filter by date"
          numberOfMonths={2}
          align="start"
          disableFuture={true}
        />
        {hasActiveFilters && (
          <Button
            onClick={onClearFilters}
            variant="ghost"
            className="text-gray-600 hover:text-gray-800 px-3 py-2 h-10"
          >
            Clear filters
          </Button>
        )}
      </div>
    );
  }
);

export default ProjectsFiltersBar;
