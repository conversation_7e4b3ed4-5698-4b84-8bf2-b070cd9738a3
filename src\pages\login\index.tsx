import { useRouter } from "next/router";
import { setCookie } from "cookies-next";
import { GetTenantService, VerifyTokenService } from "@/services/auth.service";
import Head from "next/head";
import Image from "next/image";
import { useState } from "react";
import type { GetServerSidePropsContext } from "next";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import AppInfoSection from "@/components/layout/AppInfoSection";

export default function TenantLoginPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tenantId, setTenantId] = useState("");
  const [tenantIdError, setTenantIdError] = useState("");

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!tenantId) {
      setTenantIdError("Tenant ID is required");
      return;
    }

    setLoading(true);
    const isTenantValid = await GetTenantService(tenantId);
    if (isTenantValid.success) {
      setCookie("tenant_slug", tenantId);
      router.push(`/${tenantId}/login`);
    } else {
      setTenantIdError("Invalid Tenant ID");
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Aroma - Login</title>
      </Head>
      <div className="min-h-screen flex w-full">
        {/* Left Side - App Information */}
        <AppInfoSection />

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-[55%] flex items-center justify-center px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
            {/* Mobile card wrapper */}
            <div className="lg:hidden bg-white rounded-2xl shadow-xl border border-gray-200 p-6 sm:p-8 mb-8 animate-fade-in-up backdrop-blur-sm">
              {/* Mobile header */}
              <div className="text-center mb-6">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg animate-gentle-bounce">
                  <Image
                    src="/logo.png"
                    alt="Aroma Logo"
                    width={48}
                    height={48}
                    className="w-10 h-10 sm:w-12 sm:h-12"
                  />
                </div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 animate-fade-in-up animation-delay-200">
                  Welcome to Aroma
                </h1>
                <p className="text-base sm:text-lg text-gray-600 animate-fade-in-up animation-delay-400">
                  Enter your tenant ID to continue
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6 animate-fade-in-up animation-delay-600">
                <div className="space-y-2">
                  <Label
                    htmlFor="tenantId"
                    className="text-sm font-semibold text-gray-700"
                  >
                    Tenant ID
                  </Label>
                  <Input
                    id="tenantId"
                    type="text"
                    placeholder="Enter your tenant ID"
                    value={tenantId}
                    onChange={(e) => {
                      setTenantId(e.target.value);
                      setTenantIdError("");
                    }}
                    className="h-12 text-sm px-4 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                    disabled={loading}
                  />
                  {tenantIdError && (
                    <p className="text-xs text-red-600 animate-fade-in-left">{tenantIdError}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-bold text-sm py-2 shadow-xl rounded-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] active:scale-[0.98]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">Checking...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-1">
                      <span className="text-sm">Continue</span>
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  )}
                </Button>
              </form>
            </div>

            {/* Desktop content */}
            <div className="hidden lg:block w-full">
              <div className="text-center mb-8 sm:mb-10 lg:mb-12 animate-fade-in-up">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 animate-fade-in-up animation-delay-200">
                  Welcome to Aroma
                </h2>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600 animate-fade-in-up animation-delay-400">
                  Enter your tenant ID to continue
                </p>
              </div>

              <form
                onSubmit={handleSubmit}
                className="space-y-6 sm:space-y-7 lg:space-y-8 animate-fade-in-up animation-delay-600"
              >
                <div className="space-y-2 sm:space-y-3">
                  <Label
                    htmlFor="tenantId"
                    className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                  >
                    Tenant ID
                  </Label>
                  <Input
                    id="tenantId"
                    type="text"
                    placeholder="Enter your tenant ID"
                    value={tenantId}
                    onChange={(e) => {
                      setTenantId(e.target.value);
                      setTenantIdError("");
                    }}
                    className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 lg:px-6 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                    disabled={loading}
                  />
                  {tenantIdError && (
                    <p className="text-xs sm:text-sm text-red-600 animate-fade-in-left">
                      {tenantIdError}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 sm:h-14 lg:h-16 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-bold text-sm sm:text-base lg:text-lg py-2 sm:py-3 lg:py-4 shadow-xl rounded-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] active:scale-[0.98]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2 sm:gap-3">
                      <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm sm:text-base lg:text-lg">
                        Checking...
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-1 sm:gap-2">
                      <span className="text-sm sm:text-base lg:text-lg">
                        Continue
                      </span>
                      <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                    </div>
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  const token = req.cookies.access_token;
  const tenantSlug = req.cookies.tenant_slug;

  if (token) {
    try {
      const auth = await VerifyTokenService(token);
      if (auth && auth.success) {
        return {
          redirect: {
            destination: "/dashboard",
            permanent: false,
          },
        };
      }
    } catch (error) {
      console.error("Error verifying token:", error);
    }
  }

  if (
    tenantSlug &&
    typeof tenantSlug === "string" &&
    tenantSlug.trim() !== ""
  ) {
    // return {
    //   redirect: {
    //     destination: `/${tenantSlug}/login`,
    //     permanent: false,
    //   },
    // };

    // verify the tenant slug
    const tenant = await GetTenantService(tenantSlug);
    if (tenant && tenant.success) {
      return {
        redirect: {
          destination: `/${tenantSlug}/login`,
          permanent: false,
        },
      };
    }
  }

  return {
    props: {},
  };
}
