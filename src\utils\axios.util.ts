import { LogoutService } from "@/services/auth.service";
import axios from "axios";
import { getCookie } from "cookies-next";

console.log("🔧 API Base URL:", process.env.NEXT_PUBLIC_API_BASE_URL);

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use(
  (config) => {
    const token = getCookie("access_token");
    
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    if (config.headers.customHeaders) {
      config.headers = {
        ...config.headers,
        ...config.headers.customHeaders,
      };
      delete config.headers.customHeaders;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      LogoutService();
    }
    return Promise.reject(error);
  }
);

export default api;