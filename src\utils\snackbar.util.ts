import { showSnackbar } from "@/redux/slices/snackbarSlice";
import { store as reduxStore } from "@/redux/store";

export const snackbar = {
  showSuccessMessage: (message: string) => {
    reduxStore.dispatch(showSnackbar({ type: "success", message }));
  },

  showErrorMessage: (message: string) => {
    reduxStore.dispatch(showSnackbar({ type: "error", message }));
  },

  showInfoMessage: (message: string) => {
    reduxStore.dispatch(showSnackbar({ type: "info", message }));
  },

  showWarningMessage: (message: string) => {
    reduxStore.dispatch(showSnackbar({ type: "warning", message }));
  },
};