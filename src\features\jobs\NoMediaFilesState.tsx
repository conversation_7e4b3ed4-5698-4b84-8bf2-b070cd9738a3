"use client";

import { motion } from "framer-motion";
import { FileText, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

interface NoMediaFilesStateProps {
  onAddMediaClick?: () => void;
  className?: string;
  title?: string;
  description?: string;
  buttonText?: string;
  icon?: React.ReactNode;
}

export default function NoMediaFilesState({
  onAddMediaClick,
  className = "",
  title = "No Media Files",
  description = "No media files have been uploaded for this job yet. Upload some files to get started with analysis.",
  buttonText = "Add Media Files",
  icon,
}: NoMediaFilesStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={`flex flex-col items-center justify-center p-20 text-center ${className}`}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
        className="p-4 bg-gray-100 rounded-full mb-6"
      >
        {icon || <FileText className="w-12 h-12 text-gray-600" />}
      </motion.div>
      <motion.h3
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.4 }}
        className="text-2xl font-bold text-gray-700 mb-3"
      >
        {title}
      </motion.h3>
      <motion.p
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.4 }}
        className="text-gray-600 text-lg leading-relaxed max-w-md mb-6"
      >
        {description}
      </motion.p>
      {onAddMediaClick && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.4 }}
        >
          <Button
            onClick={onAddMediaClick}
            className="bg-black hover:bg-gray-800 text-white font-semibold px-6 py-3 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl"
          >
            <Plus className="w-5 h-5 mr-2" />
            {buttonText}
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}
