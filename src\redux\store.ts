import { configureStore } from "@reduxjs/toolkit";
import snackbarReducer from "@/redux/slices/snackbarSlice";
import roleReducer from "@/redux/slices/rolesSlice";
import processesReducer from "@/redux/slices/processesSlice";
import breadcrumbReducer from "@/redux/slices/breadcrumbSlice";

export const store = configureStore({
  reducer: {
    snackbar: snackbarReducer,
    roles: roleReducer,
    processes: processesReducer,
    breadcrumb: breadcrumbReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
