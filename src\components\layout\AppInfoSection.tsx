import React from "react";
import { FolderOpen, Briefcase, PlayCircle, Users } from "lucide-react";
import Image from "next/image";

interface AppInfoSectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  features?: Array<{
    icon: React.ComponentType<{ className?: string }>;
    title: string;
  }>;
}

const defaultFeatures = [
  {
    icon: FolderOpen,
    title: "Projects",
  },
  {
    icon: Briefcase,
    title: "Jobs",
  },
  {
    icon: PlayCircle,
    title: "Playground",
  },
  {
    icon: Users,
    title: "User Management",
  },
];

const AppInfoSection: React.FC<AppInfoSectionProps> = ({
  title = "Aroma",
  subtitle = "Task & Process Management",
  description = "Streamline your workflow with our comprehensive platform for managing tasks, processing files with AI, and collaborating with your team.",
  features = defaultFeatures,
}) => {
  return (
    <div className="w-[45%] lg:flex hidden flex-col justify-center p-6 lg:p-8 xl:p-12 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating orbs with smooth infinite animation */}
        <div className="absolute -top-16 -left-16 w-48 h-48 lg:w-64 lg:h-64 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full animate-float-slow"></div>
        <div className="absolute -bottom-24 -right-16 w-60 h-60 lg:w-80 lg:h-80 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full animate-float-reverse"></div>
        <div className="absolute top-1/2 left-1/4 w-32 h-32 lg:w-40 lg:h-40 bg-gradient-to-r from-cyan-500/8 to-blue-500/8 rounded-full animate-float-medium"></div>

        {/* Animated grid pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 animate-grid-move"></div>

        {/* Subtle animated lines */}
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
        <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-l from-transparent via-white/20 to-transparent animate-shimmer-reverse"></div>
      </div>

      <div className="max-w-sm lg:max-w-md z-10 relative">
        {/* Logo and Title */}
        <div className="mb-6 lg:mb-8 animate-fade-in-up">
          <div className="w-12 h-12 lg:w-14 lg:h-14 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center mb-4 lg:mb-5 shadow-2xl border border-white/20 animate-pulse-glow">
            <Image
              src="/logo.png"
              alt="Aroma Logo"
              width={48}
              height={48}
              className="w-7 h-7 lg:w-9 lg:h-9 animate-gentle-bounce"
            />
          </div>
          <h1 className="text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-white mb-2 lg:mb-3 animate-fade-in-up animation-delay-200">
            {title}
          </h1>
          <p className="text-base lg:text-lg xl:text-xl 2xl:text-2xl text-slate-300 animate-fade-in-up animation-delay-400">
            {subtitle}
          </p>
        </div>

        {/* Description */}
        <div className="mb-8 lg:mb-10 animate-fade-in-up animation-delay-600">
          <p className="text-sm lg:text-base xl:text-lg 2xl:text-xl text-slate-400 leading-relaxed">
            {description}
          </p>
        </div>

        {/* Features */}
        <div className="space-y-4 lg:space-y-5">
          {features.map((feature, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 lg:space-x-4 animate-fade-in-left group hover:translate-x-2 transition-transform duration-300"
              style={{ animationDelay: `${800 + index * 150}ms` }}
            >
              <div className="w-8 h-8 lg:w-10 lg:h-10 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center flex-shrink-0 border border-white/20 group-hover:bg-white/20 group-hover:scale-110 transition-all duration-300">
                <feature.icon className="w-4 h-4 lg:w-5 lg:h-5 text-white group-hover:text-blue-200 transition-colors duration-300" />
              </div>
              <h4 className="font-medium text-sm lg:text-base xl:text-lg 2xl:text-xl text-slate-200 group-hover:text-white transition-colors duration-300">
                {feature.title}
              </h4>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AppInfoSection;
