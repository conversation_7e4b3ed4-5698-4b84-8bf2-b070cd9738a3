import api from "@/utils/axios.util";

export const GetProcessesService = async () => {
  try {
    const response = await api.get(`/processes/`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting processes",
    };
  }
};

export const GenerateSchemaService = async (
  userPrompt: string,
  file: File,
  url?: string,
  temperature: number = 1,
  topP: number = 0.95,
  topK: number = 40,
  maxOutputTokens: number = 2048
) => {
  try {
    const formData = new FormData();
    formData.append("user_prompt", userPrompt);
    formData.append("url", url || "");
    formData.append("temperature", temperature.toString());
    formData.append("top_p", topP.toString());
    formData.append("top_k", topK.toString());
    formData.append("max_output_tokens", maxOutputTokens.toString());
    formData.append("file", file);

    const response = await api.post("/processes/generate-schema", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occurred while generating schema",
    };
  }
};

export const RunGenericEntityExtractionProcessService = async (
  mediaUris: string[],
  mediaFiles: File[]
) => {
  try {
    const formData = new FormData();
    mediaFiles.forEach((file) => {
      formData.append("files", file);
    });
    mediaUris.forEach((uri) => {
      formData.append("urls", uri);
    });
    const response = await api.post(
      "/processes/generic-entity-extraction/run",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occurred while running the generic entity extraction process",
    };
  }
};
