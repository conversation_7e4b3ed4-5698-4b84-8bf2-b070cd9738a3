import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  FileText,
  Image,
  Mic,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  Briefcase,
} from "lucide-react";

interface Job {
  id: string;
  title: string;
  type: "text" | "image" | "audio";
  status: "pending" | "processing" | "completed" | "failed" | "reviewing";
  assignee?: string;
  createdAt: string;
  completedAt?: string;
  priority: "low" | "medium" | "high";
  project: string;
}

const mockJobs: Job[] = [
  {
    id: "job-001",
    title: "Extract text from legal documents",
    type: "image",
    status: "completed",
    assignee: "<PERSON>",
    createdAt: "2024-01-15T10:30:00Z",
    completedAt: "2024-01-15T14:22:00Z",
    priority: "high",
    project: "Legal Document Processing",
  },
  {
    id: "job-002",
    title: "Transcribe customer interview",
    type: "audio",
    status: "reviewing",
    assignee: "<PERSON>",
    createdAt: "2024-01-15T09:15:00Z",
    priority: "medium",
    project: "Customer Research",
  },
  {
    id: "job-003",
    title: "Process insurance claim forms",
    type: "text",
    status: "processing",
    assignee: "Lisa <PERSON>",
    createdAt: "2024-01-15T08:45:00Z",
    priority: "high",
    project: "Insurance Claims",
  },
  {
    id: "job-004",
    title: "Extract data from medical reports",
    type: "image",
    status: "pending",
    createdAt: "2024-01-15T11:20:00Z",
    priority: "medium",
    project: "Healthcare Analytics",
  },
  {
    id: "job-005",
    title: "Analyze meeting recordings",
    type: "audio",
    status: "failed",
    assignee: "David Kumar",
    createdAt: "2024-01-15T07:30:00Z",
    priority: "low",
    project: "Meeting Intelligence",
  },
];

const getStatusIcon = (status: Job["status"]) => {
  switch (status) {
    case "pending":
      return <Clock className="w-4 h-4 text-[#FFC107]" />;
    case "processing":
      return (
        <div className="w-4 h-4 border-2 border-[#007B80] border-t-transparent rounded-full animate-spin" />
      );
    case "completed":
      return <CheckCircle className="w-4 h-4 text-[#28A745]" />;
    case "failed":
      return <AlertCircle className="w-4 h-4 text-[#DC3545]" />;
    case "reviewing":
      return <User className="w-4 h-4 text-[#17A2B8]" />;
    default:
      return null;
  }
};

const getTypeIcon = (type: Job["type"]) => {
  switch (type) {
    case "text":
      return <FileText className="w-4 h-4 text-[#007B80]" />;
    case "image":
      return <Image className="w-4 h-4 text-[#28A745]" />;
    case "audio":
      return <Mic className="w-4 h-4 text-[#FFC107]" />;
    default:
      return null;
  }
};

const getStatusColor = (status: Job["status"]) => {
  switch (status) {
    case "pending":
      return "bg-[#FFC107] text-white";
    case "processing":
      return "bg-[#17A2B8] text-white";
    case "completed":
      return "bg-[#28A745] text-white";
    case "failed":
      return "bg-[#DC3545] text-white";
    case "reviewing":
      return "bg-[#007B80] text-white";
    default:
      return "bg-[#AAAAAA] text-white";
  }
};

const getPriorityColor = (priority: Job["priority"]) => {
  switch (priority) {
    case "high":
      return "bg-[#DC3545] text-white";
    case "medium":
      return "bg-[#FFC107] text-white";
    case "low":
      return "bg-[#28A745] text-white";
    default:
      return "bg-[#AAAAAA] text-white";
  }
};

export const JobsList: React.FC = () => {
  const navigate = useNavigate();
  const [jobs] = useState<Job[]>(mockJobs);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.project.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || job.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleViewDetails = (jobId: string) => {
    navigate(`/jobs/${jobId}`);
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#666666] w-4 h-4" />
          <Input
            placeholder="Search jobs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 border-[#CCCCCC] focus:border-[#8F4E06] focus:ring-[#8F4E06]"
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-[#CCCCCC] rounded-md focus:outline-none focus:ring-2 focus:ring-[#8F4E06] focus:border-[#8F4E06] bg-white"
        >
          <option value="all">All Status</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="reviewing">Reviewing</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
        </select>
      </div>

      {/* Jobs Grid */}
      <div className="grid grid-cols-1 gap-4">
        {filteredJobs.map((job) => (
          <Card
            key={job.id}
            className="border border-[#CCCCCC] bg-white hover:shadow-md transition-shadow duration-200"
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-3">
                    {getTypeIcon(job.type)}
                    <h3 className="text-lg font-semibold text-[#202020]">
                      {job.title}
                    </h3>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-[#666666]">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(job.createdAt)}
                    </span>
                    <span className="text-[#AAAAAA]">•</span>
                    <span>{job.project}</span>
                    {job.assignee && (
                      <>
                        <span className="text-[#AAAAAA]">•</span>
                        <span className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          {job.assignee}
                        </span>
                      </>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Badge
                    className={`${getPriorityColor(
                      job.priority
                    )} text-xs font-medium`}
                  >
                    {job.priority.toUpperCase()}
                  </Badge>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(job.status)}
                    <Badge
                      className={`${getStatusColor(
                        job.status
                      )} text-xs font-medium`}
                    >
                      {job.status.toUpperCase()}
                    </Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(job.id)}
                    className="border-[#8F4E06] text-[#8F4E06] hover:bg-[#8F4E06] hover:text-white"
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredJobs.length === 0 && (
        <div className="text-center py-12">
          <Briefcase className="w-12 h-12 text-[#AAAAAA] mx-auto mb-4" />
          <h3 className="text-lg font-medium text-[#202020] mb-2">
            No jobs found
          </h3>
          <p className="text-[#666666]">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  );
};
