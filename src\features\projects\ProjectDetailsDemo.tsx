import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Edit,
  Plus,
  Users,
  Calendar,
  Briefcase,
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  Image,
  Mic,
} from "lucide-react";

const mockProject = {
  id: "proj-001",
  name: "Legal Document Processing",
  description:
    "Automated extraction and analysis of legal documents including contracts, agreements, and regulatory filings.",
  status: "active" as const,
  progress: 75,
  totalJobs: 156,
  completedJobs: 117,
  pendingJobs: 28,
  failedJobs: 11,
  assignedUsers: 8,
  createdAt: "2024-01-01T00:00:00Z",
  deadline: "2024-02-15T00:00:00Z",
  priority: "high" as const,
  teamMembers: [
    {
      id: 1,
      name: "<PERSON>",
      role: "Lead Analyst",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b5af?w=100&h=100&fit=crop&crop=face",
    },
    {
      id: 2,
      name: "<PERSON> <PERSON>",
      role: "Data Processor",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    },
    {
      id: 3,
      name: "Lisa Wang",
      role: "Quality Reviewer",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    },
  ],
  recentJobs: [
    {
      id: "job-001",
      title: "Extract contract terms",
      type: "text",
      status: "completed",
      assignee: "Sarah Chen",
      completedAt: "2024-01-15T14:22:00Z",
    },
    {
      id: "job-002",
      title: "Process legal images",
      type: "image",
      status: "processing",
      assignee: "Mike Johnson",
    },
    {
      id: "job-003",
      title: "Review compliance docs",
      type: "text",
      status: "reviewing",
      assignee: "Lisa Wang",
    },
  ],
};

export const ProjectDetails: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project] = useState(mockProject);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-[#28A745] text-white";
      case "completed":
        return "bg-[#8F4E06] text-white";
      case "on-hold":
        return "bg-[#FFC107] text-white";
      case "planning":
        return "bg-[#17A2B8] text-white";
      default:
        return "bg-[#AAAAAA] text-white";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "text":
        return <FileText className="w-4 h-4 text-[#8F4E06]" />;
      case "image":
        return <Image className="w-4 h-4 text-[#28A745]" />;
      case "audio":
        return <Mic className="w-4 h-4 text-[#FFC107]" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F9FAFB] via-white to-[#F3F4F6] page-transition">
      <div className="max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12 py-12 space-y-16">
        {/* Header */}
        <div className="flex items-center gap-6 animate-fade-in">
          <Button
            variant="ghost"
            onClick={() => navigate("/projects")}
            className="text-[#6B7280] hover:text-[#8F4E06] hover:bg-[#8F4E06]/10 smooth-transition px-4 py-3 rounded-xl"
          >
            <ArrowLeft className="w-5 h-5 mr-3" />
            Back to Projects
          </Button>
        </div>

        <div
          className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-8 animate-slide-up"
          style={{ animationDelay: "100ms" }}
        >
          <div className="space-y-6 max-w-4xl">
            <div className="flex flex-wrap items-center gap-4 mb-4">
              <h1 className="text-4xl lg:text-5xl font-bold text-[#111827] leading-tight">
                {project.name}
              </h1>
              <Badge
                className={`${getStatusColor(
                  project.status
                )} text-sm font-semibold px-4 py-2 rounded-xl smooth-transition`}
              >
                {project.status.replace("-", " ").toUpperCase()}
              </Badge>
            </div>
            <p className="text-xl text-[#6B7280] leading-relaxed">
              {project.description}
            </p>
          </div>
          <div className="flex flex-wrap gap-4 shrink-0">
            <Button
              variant="outline"
              className="border-2 border-[#8F4E06] text-[#8F4E06] hover:bg-[#8F4E06] hover:text-white smooth-transition px-6 py-3 rounded-xl font-semibold"
            >
              <Edit className="w-5 h-5 mr-3" />
              Edit Project
            </Button>
            <Button className="glass-effect bg-gradient-to-r from-[#8F4E06] to-[#B8610A] hover:from-[#7A4205] hover:to-[#A05609] text-white smooth-transition hover-lift px-6 py-3 rounded-xl font-semibold shadow-xl border-0">
              <Plus className="w-5 h-5 mr-3" />
              Add Job
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 animate-scale-in"
          style={{ animationDelay: "200ms" }}
        >
          <Card className="glass-effect border-0 shadow-xl hover-lift smooth-transition">
            <CardContent className="p-8">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-gradient-to-br from-[#28A745]/20 to-[#28A745]/10 rounded-2xl">
                  <CheckCircle className="w-8 h-8 text-[#28A745]" />
                </div>
                <div>
                  <p className="text-3xl font-bold text-[#111827] mb-1">
                    {project.completedJobs}
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Completed Jobs
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-0 shadow-xl hover-lift smooth-transition">
            <CardContent className="p-8">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-gradient-to-br from-[#FFC107]/20 to-[#FFC107]/10 rounded-2xl">
                  <Clock className="w-8 h-8 text-[#FFC107]" />
                </div>
                <div>
                  <p className="text-3xl font-bold text-[#111827] mb-1">
                    {project.pendingJobs}
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Pending Jobs
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-0 shadow-xl hover-lift smooth-transition">
            <CardContent className="p-8">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-gradient-to-br from-[#DC3545]/20 to-[#DC3545]/10 rounded-2xl">
                  <AlertCircle className="w-8 h-8 text-[#DC3545]" />
                </div>
                <div>
                  <p className="text-3xl font-bold text-[#111827] mb-1">
                    {project.failedJobs}
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Failed Jobs
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-0 shadow-xl hover-lift smooth-transition">
            <CardContent className="p-8">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-gradient-to-br from-[#8F4E06]/20 to-[#8F4E06]/10 rounded-2xl">
                  <Users className="w-8 h-8 text-[#8F4E06]" />
                </div>
                <div>
                  <p className="text-3xl font-bold text-[#111827] mb-1">
                    {project.assignedUsers}
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Team Members
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progress & Timeline */}
        <div
          className="grid grid-cols-1 xl:grid-cols-2 gap-10 animate-fade-in"
          style={{ animationDelay: "300ms" }}
        >
          <Card className="glass-effect border-0 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-[#111827]">
                Project Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-4">
                <div className="flex justify-between text-lg">
                  <span className="font-semibold text-[#111827]">
                    Overall Progress
                  </span>
                  <span className="text-[#6B7280] font-medium">
                    {project.progress}%
                  </span>
                </div>
                <Progress
                  value={project.progress}
                  className="h-4 rounded-full"
                />
              </div>
              <div className="grid grid-cols-2 gap-8 pt-6">
                <div className="text-center p-6 glass-effect rounded-2xl">
                  <p className="text-3xl font-bold text-[#8F4E06] mb-2">
                    {project.totalJobs}
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Total Jobs
                  </p>
                </div>
                <div className="text-center p-6 glass-effect rounded-2xl">
                  <p className="text-3xl font-bold text-[#28A745] mb-2">
                    {Math.round(
                      (project.completedJobs / project.totalJobs) * 100
                    )}
                    %
                  </p>
                  <p className="text-sm text-[#6B7280] font-medium">
                    Success Rate
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-0 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-[#111827]">
                Project Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="flex items-center gap-4 text-lg p-4 glass-effect rounded-xl">
                <Calendar className="w-6 h-6 text-[#6B7280]" />
                <span className="text-[#6B7280] font-medium">Started:</span>
                <span className="font-semibold text-[#111827]">
                  {formatDate(project.createdAt)}
                </span>
              </div>
              {project.deadline && (
                <div className="flex items-center gap-4 text-lg p-4 glass-effect rounded-xl">
                  <Calendar className="w-6 h-6 text-[#6B7280]" />
                  <span className="text-[#6B7280] font-medium">Deadline:</span>
                  <span className="font-semibold text-[#111827]">
                    {formatDate(project.deadline)}
                  </span>
                </div>
              )}
              <div className="pt-6">
                <div className="flex items-center gap-4 mb-4 p-4 glass-effect rounded-xl">
                  <div className="w-4 h-4 bg-gradient-to-r from-[#28A745] to-[#20C997] rounded-full"></div>
                  <span className="text-lg text-[#28A745] font-semibold">
                    On Track
                  </span>
                </div>
                <p className="text-lg text-[#6B7280] leading-relaxed">
                  Project is progressing as planned with no major blockers.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Members & Recent Jobs */}
        <div
          className="grid grid-cols-1 xl:grid-cols-2 gap-10 animate-slide-up"
          style={{ animationDelay: "400ms" }}
        >
          <Card className="glass-effect border-0 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-[#111827]">
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {project.teamMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center gap-6 p-6 glass-effect rounded-2xl hover-lift smooth-transition"
                  >
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-16 h-16 rounded-2xl ring-4 ring-[#8F4E06]/20"
                    />
                    <div className="flex-1">
                      <p className="font-semibold text-[#111827] text-lg mb-1">
                        {member.name}
                      </p>
                      <p className="text-base text-[#6B7280] font-medium">
                        {member.role}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-0 shadow-xl">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-[#111827]">
                Recent Jobs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {project.recentJobs.map((job) => (
                  <div
                    key={job.id}
                    className="flex items-center justify-between p-6 glass-effect rounded-2xl hover-lift smooth-transition"
                  >
                    <div className="flex items-center gap-6">
                      <div className="p-3 glass-effect rounded-xl">
                        {getTypeIcon(job.type)}
                      </div>
                      <div>
                        <p className="font-semibold text-[#111827] text-lg mb-1">
                          {job.title}
                        </p>
                        <p className="text-base text-[#6B7280] font-medium">
                          {job.assignee}
                        </p>
                      </div>
                    </div>
                    <Badge
                      className={`${getStatusColor(
                        job.status
                      )} text-sm font-semibold px-4 py-2 rounded-xl`}
                    >
                      {job.status.toUpperCase()}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
