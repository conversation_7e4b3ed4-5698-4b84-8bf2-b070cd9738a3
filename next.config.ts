import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  devIndicators: {
    position: "bottom-right",
  },
  // Enable standalone output for Docker
  output: "standalone",
  // Optimize for production
  poweredByHeader: false,
  // Optimize images
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.**",
        port: "",
        pathname: "/**",
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  // Optimize bundle
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  // Disable source maps in production for smaller build
  productionBrowserSourceMaps: false,
};

export default nextConfig;
