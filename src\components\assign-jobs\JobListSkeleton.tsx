import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface JobListSkeletonProps {
  count?: number;
}

export const JobListSkeleton: React.FC<JobListSkeletonProps> = ({
  count = 6,
}) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.4,
            delay: index * 0.05,
            ease: "easeOut",
          }}
        >
          <Card className="border border-[#E5E7EB] bg-white shadow-sm hover:shadow-md transition-all duration-200 group relative">
            <CardContent className="p-2 px-4">
              <div className="flex items-start gap-4">
                {/* Checkbox skeleton - hidden initially like the real component */}
                <div className="flex-shrink-0">
                  <Skeleton className="w-5 h-5 mt-1 rounded opacity-30" />
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      {/* Job name skeleton with link icon */}
                      <div className="flex items-center gap-2 mb-1">
                        <Skeleton className="h-6 w-64" />
                        <Skeleton className="w-4 h-4" />
                      </div>
                      {/* Project name skeleton with link icon */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-48" />
                        <Skeleton className="w-3 h-3" />
                      </div>
                    </div>
                    {/* Status badge skeleton */}
                    <Skeleton className="h-6 w-20 rounded-full" />
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-4">
                      {/* Assignee skeleton */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="w-4 h-4" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      {/* Date skeleton */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="w-4 h-4" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                    {/* 3-dots menu skeleton */}
                    <Skeleton className="w-8 h-8 rounded" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};
