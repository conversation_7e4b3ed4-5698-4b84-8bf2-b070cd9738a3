import { VerifyTokenService } from "@/services/auth.service";
import type { GetServerSidePropsContext } from "next";

// Reusable wrapper for authentication check
export function withAuth() {
  return async (context: GetServerSidePropsContext) => {
    const { req } = context;
    const token = req.cookies.access_token;
    const tenantSlug = req.cookies.tenant_slug;
    const loginPath = tenantSlug && typeof tenantSlug === "string" && tenantSlug.trim() !== "" ? `/${tenantSlug}/login` : "/login";

    // If no token, redirect to login
    if (!token) {
      return {
        redirect: {
          destination: loginPath,
          permanent: false,
        },
      };
    }

    console.log("Test api url:", process.env.NEXT_PUBLIC_API_BASE_URL);

    try {
      // Verify token
      const auth = await VerifyTokenService(token);
      if (!auth || !auth.success) {
        return {
          redirect: {
            destination: loginPath,
            permanent: false,
          },
        };
      }

      // Token is valid, render protected page
      return {
        props: {}
      };
    }
    catch (error) {
      console.error("Error verifying token:", error);
      return {
        redirect: {
          destination: loginPath,
          permanent: false,
        },
      };
    }
  };
}