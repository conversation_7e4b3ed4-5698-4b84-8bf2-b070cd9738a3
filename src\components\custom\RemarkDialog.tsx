"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import Button from "@/components/custom/Button";
import { Check, X, MessageSquare } from "lucide-react";

interface RemarkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  placeholder?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm: (remark: string) => void;
  loading?: boolean;
  initialRemark?: string;
  required?: boolean;
}

export default function RemarkDialog({
  open,
  onOpenChange,
  title,
  description,
  placeholder = "Enter your remark...",
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  onConfirm,
  loading = false,
  initialRemark = "",
  required = false,
}: RemarkDialogProps) {
  const [remark, setRemark] = useState(initialRemark);

  const handleConfirm = () => {
    if (required && !remark.trim()) {
      return;
    }
    onConfirm(remark.trim());
  };

  const handleCancel = () => {
    setRemark(initialRemark);
    onOpenChange(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setRemark(initialRemark);
    }
    onOpenChange(newOpen);
  };

  const isConfirmDisabled = loading || (required && !remark.trim());

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            {title}
          </DialogTitle>
          {description && (
            <p className="text-sm text-gray-600 mt-2">{description}</p>
          )}
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Remark {required && <span className="text-red-500">*</span>}
            </label>
            <Textarea
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
              placeholder={placeholder}
              className="min-h-[120px] resize-none"
              disabled={loading}
              autoFocus
            />
            {required && !remark.trim() && (
              <p className="text-xs text-red-500">Remark is required</p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-1" />
            {cancelLabel}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
          >
            {loading ? (
              <div className="w-4 h-4 animate-spin border-2 border-current border-t-transparent rounded-full mr-1" />
            ) : (
              <Check className="w-4 h-4 mr-1" />
            )}
            {loading ? "Processing..." : confirmLabel}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
