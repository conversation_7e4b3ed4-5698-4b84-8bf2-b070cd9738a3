import { useState } from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
import InvitationUrlDialog from "@/features/manage-users/InvitationUrlDialog";
import UsersList from "@/features/manage-users/UsersList";
import { withAuth } from "@/utils/withAuth";
import useFetchRoles from "@/hooks/useRoles";
const InviteUserDialog = dynamic(
  () => import("@/features/manage-users/InviteUserDialog"),
  {
    ssr: false,
  }
);

export default function ManageUsers() {
  const [inviteUserDialogOpen, setInviteUserDialogOpen] = useState(false);
  const [invitationUrlDialogOpen, setInvitationUrlDialogOpen] = useState(false);
  const [invitationToken, setInvitationToken] = useState("");

  useFetchRoles();

  return (
    <>
      <Head>
        <title>Manage Users</title>
      </Head>
      <InviteUserDialog
        open={inviteUserDialogOpen}
        setOpen={setInviteUserDialogOpen}
        setInvitationUrlDialogOpen={setInvitationUrlDialogOpen}
        setInvitationToken={setInvitationToken}
      />

      <InvitationUrlDialog
        open={invitationUrlDialogOpen}
        setOpen={setInvitationUrlDialogOpen}
        setInviteUserDialogOpen={setInviteUserDialogOpen}
        invitationToken={invitationToken}
      />

      <UsersList setInviteUserDialogOpen={setInviteUserDialogOpen} />
    </>
  );
}

export const getServerSideProps = withAuth();
