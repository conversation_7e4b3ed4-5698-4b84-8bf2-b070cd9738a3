import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, X, Loader2, Users } from "lucide-react";
import { DateRange } from "react-day-picker";
import Button from "@/components/custom/Button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DateRangePicker } from "@/components/ui/date-range-picker";

interface ProjectDetailFloatingHeaderProps {
  isVisible: boolean;
  project: Project;
  searchTerm: string;
  statusFilter: string;
  dateRange: DateRange | undefined;
  isSearching: boolean;
  hasActiveFilters: boolean;
  jobsLength: number;
  selectedJobsSize: number;
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onDateRangeChange: (range: DateRange | undefined) => void;
  onClearFilters: () => void;
  onSelectAll: () => void;
}

export const ProjectDetailFloatingHeader: React.FC<
  ProjectDetailFloatingHeaderProps
> = ({
  isVisible,
  project,
  searchTerm,
  statusFilter,
  dateRange,
  isSearching,
  hasActiveFilters,
  jobsLength,
  selectedJobsSize,
  onSearchChange,
  onStatusFilterChange,
  onDateRangeChange,
  onClearFilters,
  onSelectAll,
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
          className="fixed top-0 left-[257px] h-[92px] right-0 z-50 bg-white border-b border-[#E5E7EB] flex items-center justify-center"
        >
          <div className="container">
            <div className="max-w-7xl mx-auto px-6 py-4">
              <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
                {/* Left side - Project name */}
                <div className="flex-shrink-0 min-w-0 max-w-xs">
                  <h2
                    className="text-2xl font-bold text-gray-900 truncate"
                    title={project.name}
                  >
                    {project.name}
                  </h2>
                </div>

                {/* Right side - Filters */}
                <div className="flex flex-col sm:flex-row gap-3 flex-1 lg:max-w-4xl">
                  {/* Search Input */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#9CA3AF] w-4 h-4" />
                    <Input
                      placeholder="Search jobs..."
                      value={searchTerm}
                      onChange={(e) => onSearchChange(e.target.value)}
                      className="pl-10 pr-10 h-10 border-[#E5E7EB] focus:border-sky-600 focus:ring-sky-600 w-full"
                    />
                    {/* Right side icons */}
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                      {isSearching && (
                        <Loader2 className="w-4 h-4 text-sky-600 animate-spin" />
                      )}
                      {searchTerm && !isSearching && (
                        <button
                          onClick={() => onSearchChange("")}
                          className="text-[#9CA3AF] hover:text-[#6B7280] transition-colors"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Status Filter */}
                  <div className="w-48">
                    <Select
                      value={statusFilter}
                      onValueChange={onStatusFilterChange}
                    >
                      <SelectTrigger className="w-full h-10">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="failed">Failed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Date Range Filter */}
                  <div className="w-64">
                    <DateRangePicker
                      date={dateRange}
                      onDateChange={onDateRangeChange}
                      placeholder="Filter by date range"
                      className="h-10"
                      disableFuture={true}
                    />
                  </div>

                  {/* Clear Filters */}
                  {hasActiveFilters && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onClearFilters}
                      className="h-10 px-3 border-[#E5E7EB] text-[#6B7280] hover:bg-[#F9FAFB] whitespace-nowrap"
                    >
                      Clear Filters
                    </Button>
                  )}

                  {/* Select All Button */}
                  {jobsLength > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onSelectAll}
                      className="h-10 px-3 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white whitespace-nowrap"
                    >
                      <Users className="w-4 h-4 mr-2" />
                      {selectedJobsSize === jobsLength
                        ? "Deselect All"
                        : "Select All"}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
