import { FolderOpen, Plus } from "lucide-react";
import Button from "./Button";

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
}

export default function EmptyState({
  icon,
  title,
  description,
  actionLabel,
  onAction,
  className = "",
}: EmptyStateProps) {
  return (
    <div
      className={`flex flex-col items-center justify-center py-32 px-6 text-center ${className}`}
    >
      {/* Icon Container with gradient background */}
      <div className="w-24 h-24 bg-gradient-to-br from-sky-100 to-sky-200 rounded-full flex items-center justify-center mb-8 shadow-sm">
        {icon || <FolderOpen className="h-12 w-12 text-sky-600" />}
      </div>

      {/* Title */}
      <h3 className="text-2xl font-bold text-gray-900 mb-3">{title}</h3>

      {/* Description */}
      <p className="text-gray-600 mb-10 max-w-lg leading-relaxed text-lg">
        {description}
      </p>

      {/* Action Button */}
      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          startIcon={<Plus className="w-5 h-5" />}
          className="px-8 py-3 text-base font-medium shadow-sm hover:shadow-md transition-shadow"
          size="lg"
        >
          {actionLabel}
        </Button>
      )}
    </div>
  );
}
