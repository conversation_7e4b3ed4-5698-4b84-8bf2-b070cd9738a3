import { useEffect, useState, useCallback, useRef } from "react";
import { useRouter } from "next/router";
import { motion } from "framer-motion";
import {
  GetJobsService,
  DeleteJobService,
  AssignJobToAgentService,
  UnassignJobsService,
} from "@/services/jobs.service";
import { GetUsersService } from "@/services/users.service";
import { JobListItem } from "@/components/assign-jobs/JobListItem";
import { FloatingActionTab } from "@/components/assign-jobs/FloatingActionTab";
import { JobListSkeleton } from "@/components/assign-jobs/JobListSkeleton";
import JobDialog from "../jobs/JobDialog";
import ConfirmationDialog from "@/components/custom/ConfirmationDialog";
import BulkAssignDialog from "../jobs/BulkAssignDialog";
import { snackbar } from "@/utils/snackbar.util";
import {
  Plus,
  Briefcase,
  Search,
  X,
  Loader2,
  <PERSON>UpDown,
  ArrowUpNarrowWide,
  ArrowDownWideNarrow,
} from "lucide-react";
import Button from "@/components/custom/Button";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Card, CardContent } from "@/components/ui/card";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";

// Types
type JobWithAssignee = Job & {
  assigned_to?: string;
  assigned_to_name?: string;
  project_name?: string;
};

type SortOption = {
  field: "created_at" | "name" | "status";
  direction: "asc" | "desc";
  clicked?: boolean;
};

interface JobsListProps {
  projectId: string;
  projectDetails: { name?: string } | null;
  onJobCountChange?: () => void;
  onJobSelectionChange?: (selectedJobs: Set<string>) => void;
  onJobsLengthChange?: (length: number) => void;
  // Filter props for sharing with floating header
  searchTerm?: string; // For display in UI
  debouncedSearchTerm?: string; // For API calls
  statusFilter?: string;
  dateRange?: DateRange | undefined;
  isSearching?: boolean;
  hasActiveFilters?: boolean;
  onSearchChange?: (value: string) => void;
  onStatusFilterChange?: (value: string) => void;
  onDateRangeChange?: (range: DateRange | undefined) => void;
  onClearFilters?: () => void;
  onSelectAll?: () => void;
}

export default function JobsList({
  projectId,
  projectDetails,
  onJobCountChange,
  onJobSelectionChange,
  onJobsLengthChange,
  // Filter props
  searchTerm: externalSearchTerm,
  debouncedSearchTerm: externalDebouncedSearchTerm,
  statusFilter: externalStatusFilter,
  dateRange: externalDateRange,
  isSearching: externalIsSearching,
  hasActiveFilters: externalHasActiveFilters,
  onSearchChange: externalOnSearchChange,
  onStatusFilterChange: externalOnStatusFilterChange,
  onDateRangeChange: externalOnDateRangeChange,
  onClearFilters: externalOnClearFilters,
  onSelectAll: externalOnSelectAll,
}: JobsListProps) {
  const router = useRouter();

  // State
  const [jobs, setJobs] = useState<JobWithAssignee[]>([]);
  const [agents, setAgents] = useState<User[]>([]);
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());
  const [initialLoading, setInitialLoading] = useState(true);
  const [jobsLoading, setJobsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [sortOption, setSortOption] = useState<SortOption>({
    field: "created_at",
    direction: "desc",
    clicked: false,
  });

  // Dialog states
  const [newJobDialogOpen, setNewJobDialogOpen] = useState(false);
  const [editJobDialogOpen, setEditJobDialogOpen] = useState(false);
  const [deleteJobDialogOpen, setDeleteJobDialogOpen] = useState(false);
  const [bulkAssignDialogOpen, setBulkAssignDialogOpen] = useState(false);

  // Selected items
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [jobToEdit, setJobToEdit] = useState<Job | null>(null);
  const [jobToDelete, setJobToDelete] = useState<JobWithAssignee | null>(null);
  const [sortByCreatedAt, setSortByCreatedAt] = useState(false);
  const [sortAscending, setSortAscending] = useState<boolean | null>(false);

  // Infinite scroll state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalJobs, setTotalJobs] = useState(0);
  const limit = 10; // Fixed limit for infinite scroll

  // Filter states - use external props if provided, otherwise internal state
  const [internalSearchTerm, setInternalSearchTerm] = useState("");
  const [internalDebouncedSearchTerm, setInternalDebouncedSearchTerm] =
    useState("");
  const [internalStatusFilter, setInternalStatusFilter] =
    useState<string>("all");
  const [internalDateRange, setInternalDateRange] = useState<
    DateRange | undefined
  >(undefined);

  // Use external props if provided, otherwise use internal state
  const searchTerm =
    externalSearchTerm !== undefined ? externalSearchTerm : internalSearchTerm;
  const debouncedSearchTerm =
    externalDebouncedSearchTerm !== undefined
      ? externalDebouncedSearchTerm
      : internalDebouncedSearchTerm;
  const statusFilter =
    externalStatusFilter !== undefined
      ? externalStatusFilter
      : internalStatusFilter;
  const dateRange =
    externalDateRange !== undefined ? externalDateRange : internalDateRange;

  // Refs for debouncing and infinite scroll
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // URL update function
  const updateUrl = (params: Record<string, any>) => {
    const query = { ...router.query };

    // Update or remove parameters
    Object.entries(params).forEach(([key, value]) => {
      if (
        value === undefined ||
        value === null ||
        value === "" ||
        value === "all"
      ) {
        delete query[key];
      } else {
        query[key] = value;
      }
    });

    router.push({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  // Fetch jobs with infinite scroll support
  const fetchJobs = async (
    page = 1,
    isInitial = false,
    isLoadMore = false,
    showLoading = true,
    silent = false
  ) => {
    // Set appropriate loading states
    if (showLoading && !silent) {
      if (isInitial || !isInitialized) {
        setInitialLoading(true);
      } else if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setJobsLoading(true);
      }
    }

    try {
      // Prepare date range parameters
      const startDate = dateRange?.from
        ? format(dateRange.from, "yyyy-MM-dd")
        : null;
      const endDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : null;

      const response = await GetJobsService(
        projectId,
        page,
        limit,
        startDate,
        endDate,
        debouncedSearchTerm || null,
        statusFilter && statusFilter !== "all" ? statusFilter : null,
        sortByCreatedAt,
        sortAscending
      );

      if (response.success) {
        const jobsWithAssignee = response.data.data.map((job: any) => {
          // Find assignee name from agents list
          const assignedAgent = agents.find(
            (agent) => agent._id === job.assigned_to
          );
          return {
            ...job,
            project_name: projectDetails?.name || "Unknown Project",
            assigned_to_name: assignedAgent?.username || null,
          };
        });

        const meta = response.data.meta;

        if (isInitial || !isLoadMore) {
          // Replace jobs for initial load or refresh
          setJobs(jobsWithAssignee);
          setCurrentPage(1);
        } else {
          // Append jobs for infinite scroll
          setJobs((prev) => [...prev, ...jobsWithAssignee]);
          setCurrentPage(page);
        }

        // Update infinite scroll state
        setTotalJobs(meta.total);
        setHasMore(page < meta.total_pages);
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
    } finally {
      setInitialLoading(false);
      setJobsLoading(false);
      setLoadingMore(false);
      setHasLoadedOnce(true);
    }
  };

  // Fetch agents
  const fetchAgents = async () => {
    try {
      const response = await GetUsersService(1, 100, "", "");
      if (response.success) {
        setAgents(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
    }
  };

  // Filter handlers - use external handlers if provided, otherwise internal
  const handleFilterChange = (type: string, value: string) => {
    if (type === "status") {
      if (externalOnStatusFilterChange) {
        externalOnStatusFilterChange(value);
      } else {
        setInternalStatusFilter(value);
        updateUrl({
          status: value === "all" ? undefined : value,
          search: debouncedSearchTerm || undefined,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
        // Reset infinite scroll state
        setCurrentPage(1);
        setHasMore(true);
      }
    }
  };

  // Toggle sort direction - this will be handled by backend API
  const toggleSortDirection = () => {
    // If not clicked yet, set to ascending
    if (!sortOption.clicked) {
      setSortByCreatedAt(true);
      setSortAscending(true);
      setSortOption({ ...sortOption, direction: "asc", clicked: true });
    }
    // If already clicked and ascending, set to descending
    else if (sortOption.direction === "asc") {
      setSortByCreatedAt(true);
      setSortAscending(false);
      setSortOption({ ...sortOption, direction: "desc" });
    }
    // If already clicked and descending, reset to default (not clicked)
    else {
      setSortByCreatedAt(false);
      setSortAscending(null);
      setSortOption({ ...sortOption, clicked: false });
    }
    // The actual sorting will be handled by the backend API
  };

  const handleSearchChange = (value: string) => {
    if (externalOnSearchChange) {
      externalOnSearchChange(value);
    } else {
      setInternalSearchTerm(value);

      // Clear existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Show searching state if there's a value and it's different from current debounced term
      if (value !== debouncedSearchTerm) {
        setIsSearching(true);
      }

      // Set new timeout for debounced search
      searchTimeoutRef.current = setTimeout(() => {
        setInternalDebouncedSearchTerm(value);
        setIsSearching(false);
        updateUrl({
          search: value || undefined,
          status: statusFilter === "all" ? undefined : statusFilter,
          start_date: dateRange?.from
            ? format(dateRange.from, "yyyy-MM-dd")
            : undefined,
          end_date: dateRange?.to
            ? format(dateRange.to, "yyyy-MM-dd")
            : undefined,
        });
        // Reset infinite scroll state
        setCurrentPage(1);
        setHasMore(true);
      }, 500); // 500ms debounce delay
    }
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (externalOnDateRangeChange) {
      externalOnDateRangeChange(range);
    } else {
      setInternalDateRange(range);

      // Only update URL and trigger API call if both dates are selected or range is cleared
      if (!range || (range.from && range.to)) {
        updateUrl({
          start_date: range?.from
            ? format(range.from, "yyyy-MM-dd")
            : undefined,
          end_date: range?.to ? format(range.to, "yyyy-MM-dd") : undefined,
          search: debouncedSearchTerm || undefined,
          status: statusFilter === "all" ? undefined : statusFilter,
        });
        // Reset infinite scroll state
        setCurrentPage(1);
        setHasMore(true);
      }
    }
  };

  const handleClearFilters = () => {
    if (externalOnClearFilters) {
      externalOnClearFilters();
    } else {
      setInternalSearchTerm("");
      setInternalDebouncedSearchTerm("");
      setInternalStatusFilter("all");
      setInternalDateRange(undefined);
      setIsSearching(false);

      // Clear any pending search timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      updateUrl({
        search: undefined,
        status: undefined,
        start_date: undefined,
        end_date: undefined,
      });
      // Reset infinite scroll state
      setCurrentPage(1);
      setHasMore(true);
    }
  };

  // Load more function for infinite scroll
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore && !initialLoading) {
      fetchJobs(currentPage + 1, false, true, true, false);
    }
  }, [loadingMore, hasMore, initialLoading, currentPage]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, [loadMore]);

  // Effects
  // Initialize state from URL parameters only once
  useEffect(() => {
    if (!isInitialized && router.isReady) {
      const urlSearchTerm = (router.query.search as string) || "";
      const urlStatusFilter = (router.query.status as string) || "all";
      const startDate = router.query.start_date as string;
      const endDate = router.query.end_date as string;

      // Only initialize internal state if external props are not provided
      if (externalSearchTerm === undefined) {
        setInternalSearchTerm(urlSearchTerm);
        setInternalDebouncedSearchTerm(urlSearchTerm);
      }
      if (externalStatusFilter === undefined) {
        setInternalStatusFilter(urlStatusFilter);
      }
      if (externalDateRange === undefined && startDate && endDate) {
        setInternalDateRange({
          from: new Date(startDate),
          to: new Date(endDate),
        });
      }

      setIsInitialized(true);
    }
  }, [
    router.isReady,
    isInitialized,
    externalSearchTerm,
    externalStatusFilter,
    externalDateRange,
  ]);

  useEffect(() => {
    if (projectId) {
      fetchAgents();
    }
  }, [projectId]);

  // Initial fetch and filter changes
  useEffect(() => {
    if (projectId && agents.length > 0 && isInitialized) {
      // Only fetch if dateRange is not partially selected (both dates or no dates)
      const isDateRangeValid = !dateRange || (dateRange.from && dateRange.to);
      if (isDateRangeValid) {
        fetchJobs(1, true, false, true, false);
      }
    }
  }, [
    projectId,
    statusFilter,
    debouncedSearchTerm,
    dateRange,
    agents,
    isInitialized,
    sortAscending,
  ]);

  useEffect(() => {
    onJobSelectionChange?.(selectedJobs);
  }, [selectedJobs, onJobSelectionChange]);

  useEffect(() => {
    onJobsLengthChange?.(jobs.length);
  }, [jobs.length, onJobsLengthChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handlers
  const handleJobSelect = useCallback(
    (jobId: string) => {
      const newSelectedJobs = new Set(selectedJobs);
      if (newSelectedJobs.has(jobId)) {
        newSelectedJobs.delete(jobId);
      } else {
        newSelectedJobs.add(jobId);
      }
      setSelectedJobs(newSelectedJobs);
    },
    [selectedJobs]
  );

  const handleJobClick = (jobId: string) => {
    router.push(`/projects/${projectId}/job/${jobId}`);
  };

  const handleProjectClick = (projectId: string) => {
    router.push(`/projects/${projectId}`);
  };

  const handleAssignJob = async (jobId: string, assigneeId: string) => {
    try {
      const response = await AssignJobToAgentService(jobId, assigneeId);
      if (response.success) {
        const assignedAgent = agents.find((agent) => agent._id === assigneeId);
        snackbar.showSuccessMessage(
          `Job assigned to ${assignedAgent?.username || "agent"}`
        );
        // Refresh current data without resetting scroll position
        await fetchJobs(1, true, false, false, true);
        onJobCountChange?.();
      } else {
        snackbar.showErrorMessage("Failed to assign job");
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to assign job");
    }
  };

  const handleUnassignJob = async (jobId: string) => {
    try {
      const response = await UnassignJobsService([jobId]);
      if (response.success) {
        snackbar.showSuccessMessage("Job unassigned successfully");
        // Refresh current data without resetting scroll position
        await fetchJobs(1, true, false, false, true);
        onJobCountChange?.();
      } else {
        snackbar.showErrorMessage("Failed to unassign job");
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to unassign job");
    }
  };

  const handleEditJob = (job: JobWithAssignee) => {
    setJobToEdit(job);
    setEditJobDialogOpen(true);
  };

  const handleDeleteJob = (job: JobWithAssignee) => {
    setJobToDelete(job);
    setDeleteJobDialogOpen(true);
  };

  const handleJobSaved = () => {
    // Reset and fetch from beginning when a job is saved
    setJobs([]);
    setCurrentPage(1);
    setHasMore(true);
    fetchJobs(1, true, false, false, true);
    setSelectedJobs(new Set());
    onJobCountChange?.();
  };

  const handleJobDeleted = async () => {
    if (!jobToDelete) return;

    try {
      const response = await DeleteJobService(jobToDelete._id);
      if (response.success) {
        snackbar.showSuccessMessage("Job deleted successfully");
        // Reset and fetch from beginning when a job is deleted
        setJobs([]);
        setCurrentPage(1);
        setHasMore(true);
        fetchJobs(1, true, false, false, true);
        onJobCountChange?.();
      } else {
        snackbar.showErrorMessage("Failed to delete job");
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to delete job");
    }
    setDeleteJobDialogOpen(false);
    setJobToDelete(null);
  };

  const handleBulkAssign = () => {
    setBulkAssignDialogOpen(true);
  };

  const handleBulkUnassign = async () => {
    try {
      const jobIds = Array.from(selectedJobs);
      const response = await UnassignJobsService(jobIds);

      if (response.success) {
        snackbar.showSuccessMessage(
          `Successfully unassigned ${jobIds.length} job${
            jobIds.length !== 1 ? "s" : ""
          }`
        );
      } else {
        snackbar.showErrorMessage("Failed to unassign jobs");
      }

      // Refresh current data
      fetchJobs(1, true, false, false, true);
      handleClearSelection();
      onJobCountChange?.();
    } catch (error) {
      snackbar.showErrorMessage("Failed to unassign jobs");
    }
  };

  const handleClearSelection = () => {
    setSelectedJobs(new Set());
  };

  const handleSelectAll = () => {
    if (externalOnSelectAll) {
      externalOnSelectAll();
    } else {
      if (selectedJobs.size === jobs.length) {
        setSelectedJobs(new Set());
      } else {
        const allJobIds = new Set(jobs.map((job) => job._id));
        setSelectedJobs(allJobIds);
      }
    }
  };

  // Check if any filters are active
  const hasActiveFilters =
    debouncedSearchTerm || statusFilter !== "all" || dateRange;

  // Only show full skeleton on very first load before we have loaded data at least once
  if (initialLoading || !hasLoadedOnce) {
    return <JobListSkeleton count={6} />;
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <motion.div
        className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        {/* Left side - Title and count */}
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-sky-50 to-sky-100 rounded-xl flex items-center justify-center">
            <Briefcase className="h-5 w-5 text-sky-600" />
          </div>
          <div>
            <h2 className="text-3xl font-semibold text-[#111827]">Jobs</h2>
            {/* <p className="text-sm text-[#6B7280]">
              {meta.total} job{meta.total !== 1 ? "s" : ""} in this project
            </p> */}
          </div>
        </div>

        {/* Right side - New Job button */}
        <Button
          onClick={() => {
            setNewJobDialogOpen(true);
            setSelectedJob(null);
          }}
          startIcon={<Plus className="h-4 w-4" />}
          className="bg-gradient-to-r from-sky-600 to-sky-700 hover:from-sky-700 hover:to-sky-800 text-white px-6 py-3 rounded-md font-semibold shadow-lg"
        >
          New Job
        </Button>
      </motion.div>

      {/* Filters Section */}
      <motion.div
        className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        {/* Left side - Search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#9CA3AF] w-4 h-4" />
            <Input
              placeholder="Search jobs..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 pr-10 h-10 border-[#E5E7EB] focus:border-sky-600 focus:ring-sky-600 w-full"
            />
            {/* Right side icons */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
              {isSearching && (
                <Loader2 className="w-4 h-4 text-sky-600 animate-spin" />
              )}
              {searchTerm && !isSearching && (
                <button
                  onClick={() => handleSearchChange("")}
                  className="text-[#9CA3AF] hover:text-[#6B7280] transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Filters */}
        <div className="flex flex-wrap gap-3 items-center">
          {/* Status Filter */}
          <div className="w-48">
            <Select
              value={statusFilter}
              onValueChange={(value) => handleFilterChange("status", value)}
            >
              <SelectTrigger className="w-full h-10">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter with Sort Button */}
          <div className="flex gap-1">
            <DateRangePicker
              date={dateRange}
              onDateChange={handleDateRangeChange}
              placeholder="Filter by date range"
              className="h-10"
              disableFuture={true}
            />
            {/* Sort Button - positioned in rightmost section */}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSortDirection}
              className="h-10 px-3 border-[#E5E7EB] hover:bg-[#F9FAFB] flex items-center gap-1"
              title={
                !sortOption.clicked
                  ? "Sort"
                  : sortOption.direction === "asc"
                  ? "Sort Ascending"
                  : "Sort Descending"
              }
            >
              {!sortOption.clicked ? (
                <ArrowUpDown className="h-4 w-4 text-sky-600" />
              ) : sortOption.direction === "asc" ? (
                <ArrowUpNarrowWide className="h-4 w-4 text-sky-600" />
              ) : (
                <ArrowDownWideNarrow className="h-4 w-4 text-sky-600" />
              )}
              Sort
            </Button>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="h-10 px-3 border-[#E5E7EB] text-[#6B7280] hover:bg-[#F9FAFB] whitespace-nowrap"
            >
              Clear Filters
            </Button>
          )}

          {/* Select All Button */}
          {jobs.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="h-10 px-3 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white whitespace-nowrap"
            >
              {/* <Users className="w-4 h-4 mr-2" /> */}
              {selectedJobs.size === jobs.length
                ? "Deselect All"
                : "Select All"}
            </Button>
          )}
        </div>
      </motion.div>

      {/* Jobs List */}
      {jobsLoading || (!hasLoadedOnce && jobs.length === 0) ? (
        <JobListSkeleton count={6} />
      ) : jobs.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="border border-[#E5E7EB] bg-white shadow-sm">
            <CardContent className="text-center py-16">
              <Briefcase className="w-16 h-16 text-[#9CA3AF] mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-[#111827] mb-3">
                No jobs found
              </h3>
              <p className="text-[#6B7280] text-lg mb-6">
                {hasActiveFilters
                  ? "Try adjusting your search or filter criteria."
                  : "Get started by creating your first job for this project."}
              </p>
              <Button
                onClick={() => {
                  setNewJobDialogOpen(true);
                  setSelectedJob(null);
                }}
                startIcon={<Plus className="h-4 w-4" />}
              >
                Create Job
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      ) : (
        <>
          <motion.div
            key={`jobs-${statusFilter}`}
            className="space-y-4"
            role="list"
            aria-label="Jobs list"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {jobs.map((job, index) => (
              <motion.div
                key={job._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.03,
                  ease: "easeOut",
                }}
              >
                <JobListItem
                  job={job}
                  isSelected={selectedJobs.has(job._id)}
                  onSelect={handleJobSelect}
                  onJobClick={handleJobClick}
                  onProjectClick={handleProjectClick}
                  onAssignJob={handleAssignJob}
                  onUnassignJob={handleUnassignJob}
                  agents={agents}
                  showEditDelete={true}
                  onEditJob={handleEditJob}
                  onDeleteJob={handleDeleteJob}
                />
              </motion.div>
            ))}
          </motion.div>

          {/* Infinite scroll trigger */}
          {hasMore && (
            <div ref={observerRef} className="flex justify-center py-8">
              {loadingMore ? (
                <div className="flex items-center gap-2 text-gray-500">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Loading more jobs...</span>
                </div>
              ) : (
                <div className="h-4" /> // Invisible trigger element
              )}
            </div>
          )}

          {/* End of list indicator */}
          {!hasMore && jobs.length > 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>You've reached the end.</p>
              <p className="text-sm mt-1">{totalJobs} total jobs</p>
            </div>
          )}
        </>
      )}

      {/* Job Dialogs */}
      <JobDialog
        open={newJobDialogOpen}
        setOpen={setNewJobDialogOpen}
        selectedProject={selectedJob as any}
        setSelectedProject={setSelectedJob as any}
        onProjectCreated={handleJobSaved}
        projectId={projectId}
      />

      <JobDialog
        open={editJobDialogOpen}
        setOpen={setEditJobDialogOpen}
        selectedProject={jobToEdit as any}
        setSelectedProject={setJobToEdit as any}
        onProjectCreated={handleJobSaved}
        projectId={projectId}
      />

      <ConfirmationDialog
        open={deleteJobDialogOpen}
        onOpenChange={setDeleteJobDialogOpen}
        title="Delete Job"
        description={`Are you sure you want to delete "${jobToDelete?.name}"? This action cannot be undone.`}
        onConfirm={handleJobDeleted}
        confirmLabel="Delete Job"
        cancelLabel="Cancel"
        variant="destructive"
      />

      <BulkAssignDialog
        open={bulkAssignDialogOpen}
        setOpen={setBulkAssignDialogOpen}
        selectedJobIds={Array.from(selectedJobs)}
        agents={agents}
        onAssignmentComplete={() => {
          fetchJobs(1, true, false, false, true);
          handleClearSelection();
          onJobCountChange?.();
        }}
        projectId={projectId}
        onJobRemoved={(jobId) => {
          const newSelectedJobs = new Set(selectedJobs);
          newSelectedJobs.delete(jobId);
          setSelectedJobs(newSelectedJobs);
        }}
      />

      {/* Floating Action Tab */}
      <FloatingActionTab
        selectedCount={selectedJobs.size}
        isVisible={selectedJobs.size > 0}
        onClearSelection={handleClearSelection}
        onBulkAssign={handleBulkAssign}
        onBulkUnassign={handleBulkUnassign}
      />
    </div>
  );
}
