// pages/_app.tsx
import { useEffect } from "react";
import { useRouter } from "next/router";
import PublicLayout from "@/components/layout/PublicLayout";
import ProtectedLayout from "@/components/layout/ProtectedLayout";
import { font } from "@/constants/font";
import "@/styles/globals.css";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "@/redux/store";
import Snackbar from "@/components/custom/Snackbar";
import { Toaster } from "@/components/ui/sonner";
import type { AppProps } from "next/app";
import CustomTopLoader from "@/components/custom/CustomTopLoader";

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter();

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const preloader = document.getElementById("preloader");
      if (preloader && router.isReady) {
        preloader.style.display = "none";
      }
    }
  }, [router.isReady]);

  const publicRoutes = [
    "/login",
    "/[tenant]/login",
    "/invite/[token]",
    "/404",
    "/500",
  ];

  // Safe check for router.pathname during SSR/SSG
  const isPublicRoute = router.pathname ? publicRoutes.includes(router.pathname) : false;

  return (
    <ReduxProvider store={store}>
      <Snackbar />
      <Toaster position="bottom-right" />
      <CustomTopLoader />
      <div className={font.className}>
        {isPublicRoute ? (
          <PublicLayout>
            <Component {...pageProps} />
          </PublicLayout>
        ) : (
          <ProtectedLayout>
            <Component {...pageProps} />
          </ProtectedLayout>
        )}
      </div>
    </ReduxProvider>
  );
}
