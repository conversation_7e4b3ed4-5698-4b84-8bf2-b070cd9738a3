import * as React from "react";
import { X, Search, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";

export interface TagOption {
  value: string;
  label: string;
  category?: string; // Optional category for grouping
}

export interface Tag {
  value: string;
  label: string;
}

interface TagInputProps {
  value: Tag[];
  onChange: (tags: Tag[]) => void;
  options: TagOption[];
  placeholder?: string;
  searchPlaceholder?: string;
  className?: string;
  disabled?: boolean;
  maxTags?: number;
  allowCustomTags?: boolean;
}

export function TagInput({
  value = [],
  onChange,
  options,
  placeholder = "Search and select options...",
  searchPlaceholder = "Type to search...",
  className,
  disabled = false,
  maxTags,
  allowCustomTags = false,
}: TagInputProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [inputValue, setInputValue] = React.useState("");
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Filter options based on search query and exclude already selected
  const filteredOptions = React.useMemo(() => {
    const selectedValues = value.map(tag => tag.value);
    const availableOptions = options.filter(option => !selectedValues.includes(option.value));
    
    if (!searchQuery) return availableOptions;
    
    const query = searchQuery.toLowerCase();
    return availableOptions.filter(option => 
      option.label.toLowerCase().includes(query) ||
      option.category?.toLowerCase().includes(query)
    );
  }, [options, searchQuery, value]);

  // Handle tag addition
  const addTag = (option: TagOption) => {
    if (maxTags && value.length >= maxTags) return;
    
    const newTag: Tag = {
      value: option.value,
      label: option.label,
    };
    
    onChange([...value, newTag]);
    setSearchQuery("");
    setInputValue("");
  };

  // Handle tag removal
  const removeTag = (tagValue: string) => {
    onChange(value.filter(tag => tag.value !== tagValue));
  };

  // Handle custom tag creation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && allowCustomTags && inputValue.trim()) {
      e.preventDefault();
      if (maxTags && value.length >= maxTags) return;
      
      const customTag: Tag = {
        value: inputValue.trim(),
        label: inputValue.trim(),
      };
      
      // Check if tag already exists
      if (!value.some(tag => tag.value === customTag.value)) {
        onChange([...value, customTag]);
      }
      
      setInputValue("");
      setSearchQuery("");
    } else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      // Remove last tag on backspace when input is empty
      removeTag(value[value.length - 1].value);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setSearchQuery(newValue);
    setOpen(true);
  };

  // Clear search when popover closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
      setInputValue("");
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            "flex min-h-10 w-full flex-wrap items-center gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
            "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
            disabled && "cursor-not-allowed opacity-50",
            className
          )}
          onClick={() => {
            if (!disabled) {
              setOpen(true);
              inputRef.current?.focus();
            }
          }}
        >
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          
          {/* Render selected tags */}
          {value.map((tag) => (
            <div
              key={tag.value}
              className="inline-flex items-center gap-1 rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground"
            >
              <span>{tag.label}</span>
              {!disabled && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-secondary-foreground/20"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeTag(tag.value);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          ))}
          
          {/* Input field */}
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={value.length === 0 ? placeholder : ""}
            className="flex-1 border-0 bg-transparent p-0 text-sm outline-none placeholder:text-muted-foreground focus-visible:ring-0"
            disabled={disabled || (maxTags ? value.length >= maxTags : false)}
          />
          
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </div>
      </PopoverTrigger>
      
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
        <div className="max-h-60 overflow-auto p-1">
          {filteredOptions.length === 0 ? (
            <div className="py-6 text-center text-sm text-muted-foreground">
              {searchQuery ? "No options found." : "All options selected."}
            </div>
          ) : (
            filteredOptions.map((option) => (
              <div
                key={option.value}
                className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                onClick={() => addTag(option)}
              >
                <span className="truncate">{option.label}</span>
                {option.category && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    {option.category}
                  </span>
                )}
              </div>
            ))
          )}
          
          {allowCustomTags && inputValue.trim() && !filteredOptions.some(opt => opt.label.toLowerCase() === inputValue.toLowerCase()) && (
            <div
              className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground border-t"
              onClick={() => {
                const customTag: Tag = {
                  value: inputValue.trim(),
                  label: inputValue.trim(),
                };
                if (!value.some(tag => tag.value === customTag.value)) {
                  onChange([...value, customTag]);
                }
                setInputValue("");
                setSearchQuery("");
                setOpen(false);
              }}
            >
              <span className="truncate">Create "{inputValue.trim()}"</span>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
