import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Briefcase,
  CheckCircle,
  Clock,
  TrendingUp,
  AlertCircle,
  PlayCircle,
  XCircle,
} from "lucide-react";
import {
  JobCountByStatusService,
  JobCountByProcessService,
} from "@/services/metrics.service";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  bgColor: string;
  textColor: string;
  borderColor: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  bgColor,
  textColor,
  borderColor,
}) => (
  <motion.div
    className={`${bgColor} rounded-md p-8 border ${borderColor} shadow-sm transition-all duration-300 hover:shadow-lg group relative overflow-hidden min-h-[140px]`}
    whileHover={{
      scale: 1.02,
      y: -4,
      transition: { duration: 0.2 },
    }}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    {/* Background decoration */}
    <div className="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full -translate-y-12 translate-x-12"></div>
    <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>

    <div className="relative h-full flex flex-col justify-between">
      {/* Header with title and icon */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex-1">
          <div
            className={`text-sm font-medium ${textColor} opacity-75 leading-tight`}
          >
            {title}
          </div>
        </div>
        <motion.div
          className={`${textColor} opacity-60 group-hover:opacity-90`}
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ duration: 0.2 }}
        >
          {icon}
        </motion.div>
      </div>

      {/* Value */}
      <motion.div
        className={`text-4xl font-bold ${textColor} leading-none`}
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {value}
      </motion.div>
    </div>
  </motion.div>
);

export const DashboardStats: React.FC = () => {
  const [statusMetrics, setStatusMetrics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJobStats = async () => {
      try {
        const response = await JobCountByStatusService();
        if (response.success) {
          // Handle the new data format - array of objects with title and value
          setStatusMetrics(Array.isArray(response.data) ? response.data : []);
        }
      } catch (error) {
        console.error("Failed to fetch job stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobStats();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div
            key={index}
            className=" rounded-md p-8 border border-gray-200 relative overflow-hidden min-h-[140px]"
          >
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full -translate-y-12 translate-x-12"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>

            <div className="relative h-full flex flex-col justify-between">
              {/* Header with title and icon */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="h-4 w-20 bg-slate-200 rounded animate-pulse"></div>
                </div>
                <div className="h-5 w-5 bg-slate-200 rounded animate-pulse"></div>
              </div>

              {/* Value */}
              <div className="h-10 w-20 bg-slate-200 rounded animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case "total_jobs":
        return {
          icon: <Briefcase className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "Total Jobs",
        };
      case "completed":
        return {
          icon: <CheckCircle className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "Jobs Completed ",
        };
      case "in-progress":
        return {
          icon: <PlayCircle className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "In Progress",
        };
      case "pending":
        return {
          icon: <Clock className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "Pending",
        };
      case "failed":
        return {
          icon: <XCircle className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "Failed",
        };
      case "success_rate":
        return {
          icon: <TrendingUp className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label: "Success Rate",
        };
      default:
        return {
          icon: <AlertCircle className="w-5 h-5 text-slate-600" />,
          bgColor: "",
          textColor: "text-slate-700",
          borderColor: "border-gray-200",
          label:
            status.charAt(0).toUpperCase() + status.slice(1).replace(/-/g, " "),
        };
    }
  };

  const formatValue = (title: string, value: number) => {
    if (title === "success_rate") {
      return `${(value * 100).toFixed(1)}%`;
    }
    return value.toLocaleString();
  };

  // Show only the most important metrics in 2 rows max (6 cards)
  const displayOrder = [
    "total_jobs",
    "completed",
    "in-progress",
    "pending",
    "failed",
    "success_rate",
  ];

  const sortedMetrics = statusMetrics
    .filter((metric) => displayOrder.includes(metric.title))
    .sort((a, b) => {
      const aIndex = displayOrder.indexOf(a.title);
      const bIndex = displayOrder.indexOf(b.title);
      return aIndex - bIndex;
    });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {sortedMetrics.map((metric) => {
        const config = getStatusConfig(metric.title);
        return (
          <StatCard
            key={metric.title}
            title={config.label}
            value={formatValue(metric.title, metric.value)}
            icon={config.icon}
            bgColor={config.bgColor}
            textColor={config.textColor}
            borderColor={config.borderColor}
          />
        );
      })}
    </div>
  );
};

export const WorkloadProgress: React.FC = () => {
  const [processMetrics, setProcessMetrics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJobsByProcess = async () => {
      try {
        const response = await JobCountByProcessService();
        if (response.success) {
          // Handle the new data format - array of objects with title and value
          setProcessMetrics(Array.isArray(response.data) ? response.data : []);
        }
      } catch (error) {
        console.error("Failed to fetch jobs by process:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsByProcess();
  }, []);

  const getProcessDisplayName = (processName: string) => {
    const displayNames: { [key: string]: string } = {
      "audio-transcribe-analysis": "Audio Transcription",
      "extract-image-data": "Image Data Extraction",
      "legal-document-processing": "Legal Document Processing",
      "customer-research": "Customer Research",
      "generic-entity-extraction": "Entity Extraction",
    };
    return (
      displayNames[processName] ||
      processName.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
    );
  };

  const getProcessColor = (index: number) => {
    const colors = [
      "#007B80",
      "#28A745",
      "#FFC107",
      "#DC3545",
      "#6F42C1",
      "#FD7E14",
    ];
    return colors[index % colors.length];
  };

  if (loading) {
    return (
      <Card className="border border-[#CCCCCC] bg-white">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-[#202020]">
            Jobs by Process
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between text-sm">
                <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
              </div>
              <div className="h-2 bg-gray-200 rounded animate-pulse"></div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  // Calculate total jobs for percentage calculation
  const totalJobs = processMetrics.reduce(
    (sum, metric) => sum + metric.value,
    0
  );

  return (
    <Card className="border border-[#CCCCCC] bg-white">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-[#202020]">
          Jobs by Process
        </CardTitle>
        <p className="text-sm text-[#666666] mt-1">
          Job count distribution across different process types
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {processMetrics.length > 0 ? (
          processMetrics.map((metric, index) => {
            const percentage =
              totalJobs > 0 ? (metric.value / totalJobs) * 100 : 0;
            return (
              <div key={metric.title} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-[#202020]">
                    {getProcessDisplayName(metric.title)}
                  </span>
                  <span className="text-[#666666]">
                    {metric.value.toLocaleString()} jobs (
                    {percentage.toFixed(1)}% of total)
                  </span>
                </div>
                <Progress
                  value={percentage}
                  className="h-2"
                  style={
                    {
                      "--progress-background": getProcessColor(index),
                    } as React.CSSProperties
                  }
                />
              </div>
            );
          })
        ) : (
          <div className="text-center text-gray-500 py-8">
            No process data available
          </div>
        )}
      </CardContent>
    </Card>
  );
};
