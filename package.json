{"name": "aroma", "version": "0.1.0", "private": true, "scripts": {"dev:turbo": "next dev --turbopack", "dev": "next dev", "build": "next build", "start": "PORT=${PORT:-3030} next start -p $PORT", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.7", "@uiw/react-json-view": "^1.9.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "framer-motion": "^12.16.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "postcss": "^8.5.3", "react": "^18.0.0", "react-day-picker": "^8.10.1", "react-dom": "^18.0.0", "react-redux": "^9.2.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.2", "tw-animate-css": "^1.3.0", "typescript": "^5"}}