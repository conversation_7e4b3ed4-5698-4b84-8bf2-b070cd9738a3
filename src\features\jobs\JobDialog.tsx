import { Fragment, useEffect, useState, useRef } from "react";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Description,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Plus, SaveIcon, UploadCloud, X, RefreshCw } from "lucide-react";
import TextField from "@/components/custom/TextField";
import Button from "@/components/custom/Button";
import FieldTitle from "@/components/custom/FieldTitle";
import Select from "@/components/custom/Select";
import {
  CreateJobService,
  UpdateJobService,
  AddMediaToJobService,
} from "@/services/jobs.service";
import { useAppSelector } from "@/redux";
import { snackbar } from "@/utils/snackbar.util";

// Random job name generator based on process type
const generateRandomJobName = (processName?: string): string => {
  // Process-specific job types and actions
  const processJobTypes: Record<
    string,
    { actions: string[]; subjects: string[] }
  > = {
    "audio-transcribe-analysis": {
      actions: [
        "Transcribe",
        "Analyze",
        "Process",
        "Convert",
        "Extract",
        "Decode",
      ],
      subjects: [
        "Audio",
        "Speech",
        "Recording",
        "Interview",
        "Podcast",
        "Meeting",
        "Call",
      ],
    },
    "extract-image-data": {
      actions: [
        "Extract",
        "Analyze",
        "Process",
        "Scan",
        "Parse",
        "Detect",
        "Identify",
      ],
      subjects: [
        "Image",
        "Photo",
        "Document",
        "Text",
        "Data",
        "Content",
        "Visual",
      ],
    },
    "text-analysis": {
      actions: [
        "Analyze",
        "Process",
        "Parse",
        "Review",
        "Examine",
        "Study",
        "Evaluate",
      ],
      subjects: [
        "Text",
        "Document",
        "Content",
        "Data",
        "Report",
        "Article",
        "Manuscript",
      ],
    },
    "data-processing": {
      actions: [
        "Process",
        "Transform",
        "Clean",
        "Validate",
        "Organize",
        "Structure",
        "Format",
      ],
      subjects: [
        "Data",
        "Dataset",
        "Records",
        "Information",
        "Files",
        "Content",
        "Database",
      ],
    },
  };

  // Generic job types for unknown processes
  const genericJobTypes = {
    actions: [
      "Process",
      "Analyze",
      "Handle",
      "Manage",
      "Execute",
      "Complete",
      "Perform",
    ],
    subjects: [
      "Task",
      "Job",
      "Work",
      "Assignment",
      "Operation",
      "Activity",
      "Request",
    ],
  };

  // Get job types based on process or use generic
  const jobTypes = processJobTypes[processName || ""] || genericJobTypes;

  // Additional descriptive words
  const descriptors = [
    "Quick",
    "Smart",
    "Auto",
    "Batch",
    "Daily",
    "Weekly",
    "Priority",
    "Urgent",
    "Standard",
    "Custom",
    "Advanced",
    "Basic",
    "Enhanced",
    "Optimized",
    "Efficient",
  ];

  const timeframes = [
    "Today",
    "Morning",
    "Evening",
    "Weekend",
    "Q1",
    "Q2",
    "Q3",
    "Q4",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Generate different name patterns
  const patterns = [
    () => {
      const action =
        jobTypes.actions[Math.floor(Math.random() * jobTypes.actions.length)];
      const subject =
        jobTypes.subjects[Math.floor(Math.random() * jobTypes.subjects.length)];
      const number = Math.floor(Math.random() * 999) + 1;
      return `${action} ${subject} ${number}`;
    },
    () => {
      const descriptor =
        descriptors[Math.floor(Math.random() * descriptors.length)];
      const action =
        jobTypes.actions[Math.floor(Math.random() * jobTypes.actions.length)];
      const subject =
        jobTypes.subjects[Math.floor(Math.random() * jobTypes.subjects.length)];
      return `${descriptor} ${action} ${subject}`;
    },
    () => {
      const action =
        jobTypes.actions[Math.floor(Math.random() * jobTypes.actions.length)];
      const subject =
        jobTypes.subjects[Math.floor(Math.random() * jobTypes.subjects.length)];
      const timeframe =
        timeframes[Math.floor(Math.random() * timeframes.length)];
      return `${action} ${subject} - ${timeframe}`;
    },
    () => {
      const descriptor =
        descriptors[Math.floor(Math.random() * descriptors.length)];
      const subject =
        jobTypes.subjects[Math.floor(Math.random() * jobTypes.subjects.length)];
      const action =
        jobTypes.actions[Math.floor(Math.random() * jobTypes.actions.length)];
      return `${descriptor} ${subject} ${action}`;
    },
  ];

  // Select random pattern and generate name
  const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];
  return selectedPattern();
};

type JobDialogProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  selectedProject?: Job | null;
  setSelectedProject?: (val: Job | null) => void;
  onProjectCreated?: () => void;
  projectId: string;
};

export default function JobDialog({
  open,
  setOpen,
  selectedProject,
  setSelectedProject,
  onProjectCreated,
  projectId,
}: JobDialogProps) {
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [process, setProcess] = useState("");
  const [nameHelperText, setNameHelperText] = useState("");
  const [processHelperText, setProcessHelperText] = useState("");

  // Media-related state
  const [urlInput, setUrlInput] = useState("");
  const [mediaUrls, setMediaUrls] = useState<string[]>([]);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [isUploadingMedia, setIsUploadingMedia] = useState(false);
  const [jobCreationStep, setJobCreationStep] = useState<
    "form" | "uploading" | "complete"
  >("form");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const processes = useAppSelector((state) => state.processes);

  // Function to regenerate random job name
  const handleRegenerateRandomName = () => {
    if (!selectedProject) {
      // Only allow regeneration for new jobs
      setName(generateRandomJobName(process));
      setNameHelperText("");
    }
  };

  useEffect(() => {
    if (open && selectedProject) {
      // Editing existing job - populate with existing data
      setName(selectedProject.name);
      setDescription(selectedProject.description || "");
      setProcess(selectedProject.process_name || "");
      setNameHelperText("");
      setProcessHelperText("");
    } else if (open && !selectedProject) {
      // Creating new job - auto-fill with random name
      setName(generateRandomJobName());
      setDescription("");
      setProcess("");
      setNameHelperText("");
      setProcessHelperText("");
      // Reset media state for new jobs
      setUrlInput("");
      setMediaUrls([]);
      setMediaFiles([]);
      setJobCreationStep("form");
      setIsUploadingMedia(false);
    }
  }, [selectedProject, open]);

  // Update job name when process changes (for new jobs only)
  useEffect(() => {
    if (!selectedProject && process && open) {
      // Only regenerate if the current name looks like a generated one or is empty
      const isGeneratedName =
        name === "" ||
        /^(Transcribe|Analyze|Process|Convert|Extract|Decode|Scan|Parse|Detect|Identify|Review|Examine|Study|Evaluate|Transform|Clean|Validate|Organize|Structure|Format|Handle|Manage|Execute|Complete|Perform|Quick|Smart|Auto|Batch|Daily|Weekly|Priority|Urgent|Standard|Custom|Advanced|Basic|Enhanced|Optimized|Efficient)/.test(
          name
        );

      if (isGeneratedName) {
        setName(generateRandomJobName(process));
      }
    }
  }, [process, selectedProject, open]);

  const handleClose = () => {
    setOpen(false);
    setTimeout(() => {
      setLoading(false);
      setName("");
      setDescription("");
      setProcess("");
      setNameHelperText("");
      setProcessHelperText("");
      // Reset media state
      setUrlInput("");
      setMediaUrls([]);
      setMediaFiles([]);
      setJobCreationStep("form");
      setIsUploadingMedia(false);
      if (setSelectedProject) {
        setSelectedProject(null);
      }
    }, 300);
  };

  // Get valid extensions for the selected process
  const validExtensions = process
    ? processes[process as ProcessName]?.valid_extensions || []
    : [];

  // Media handling functions (adapted from AddMediaDialog)
  const handleAddUrl = () => {
    if (urlInput.trim()) {
      setMediaUrls([...mediaUrls, urlInput.trim()]);
      setUrlInput("");
    }
  };

  const handleRemoveUrl = (index: number) => {
    setMediaUrls(mediaUrls.filter((_, i) => i !== index));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    // Just add files without validation during selection
    // Validation will happen when Create button is clicked
    setMediaFiles([...mediaFiles, ...files]);
  };

  const handleRemoveFile = (index: number) => {
    setMediaFiles(mediaFiles.filter((_, i) => i !== index));
  };

  // Validate file types when Create button is clicked
  const validateMediaFiles = () => {
    if (!process || validExtensions.length === 0 || mediaFiles.length === 0) {
      return true; // No validation needed
    }

    const invalidFiles = mediaFiles.filter((file) => {
      const extension = file.name.split(".").pop()?.toLowerCase();
      if (!extension) return true;

      // Check if the extension (without dot) is in valid extensions
      // Handle both formats: with dot (.jpg) and without dot (jpg)
      const extensionWithDot = `.${extension}`;
      return (
        !validExtensions.includes(extension) &&
        !validExtensions.includes(extensionWithDot)
      );
    });

    if (invalidFiles.length > 0) {
      snackbar.showErrorMessage(
        `Invalid file types: ${invalidFiles
          .map((f) => f.name)
          .join(", ")}. Allowed: ${validExtensions.join(", ")}`
      );
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name) {
      setNameHelperText("Enter job name");
      return;
    }
    if (!selectedProject && !process) {
      setProcessHelperText("Select a process");
      return;
    }

    // Validate media files for new jobs
    if (!selectedProject && !validateMediaFiles()) {
      return;
    }

    setLoading(true);

    // For editing existing jobs, just update the job
    if (selectedProject) {
      const response = await UpdateJobService(
        selectedProject._id,
        name,
        description
      );
      if (response.success) {
        snackbar.showSuccessMessage("Job updated successfully");
        handleClose();
        onProjectCreated?.();
      } else {
        setLoading(false);
        setNameHelperText(response.data?.message || "Something went wrong");
        snackbar.showErrorMessage(response.data);
      }
      return;
    }

    // For new jobs, handle the complete workflow
    try {
      // Step 1: Create the job
      setJobCreationStep("uploading");
      const jobResponse = await CreateJobService(
        projectId,
        name,
        description,
        process
      );

      if (!jobResponse.success) {
        setLoading(false);
        setJobCreationStep("form");
        setNameHelperText(jobResponse.data?.message || "Something went wrong");
        snackbar.showErrorMessage(jobResponse.data);
        return;
      }

      const createdJob = jobResponse.data;

      // Step 2: Upload media if any files or URLs are provided
      if (mediaUrls.length > 0 || mediaFiles.length > 0) {
        setIsUploadingMedia(true);
        const uploadResponse = await AddMediaToJobService(
          createdJob._id,
          mediaUrls,
          mediaFiles
        );

        if (!uploadResponse.success) {
          setLoading(false);
          setIsUploadingMedia(false);
          setJobCreationStep("form");
          snackbar.showErrorMessage(
            uploadResponse.data ||
              "Media upload failed. Job created but media not uploaded."
          );
          // Still call onProjectCreated since job was created successfully
          onProjectCreated?.();
          handleClose();
          return;
        }
      }

      // Step 3: Success
      setJobCreationStep("complete");
      snackbar.showSuccessMessage("Job created successfully!");
      handleClose();
      onProjectCreated?.();
    } catch (error) {
      setLoading(false);
      setIsUploadingMedia(false);
      setJobCreationStep("form");
      snackbar.showErrorMessage("An unexpected error occurred");
    }
  };

  return (
    <Transition appear show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-150"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/70" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto p-4">
          <div className="flex min-h-full items-center justify-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-200"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-150"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel
                className={`w-full transform overflow-hidden rounded-xl bg-white dark:bg-zinc-900 p-6 shadow-xl transition-all ${
                  selectedProject ? "max-w-lg" : "max-w-4xl"
                }`}
              >
                <DialogTitle className="text-lg font-semibold mb-1">
                  {selectedProject ? "Update Job" : "New Job"}
                </DialogTitle>
                <Description className="text-sm text-muted-foreground mb-4">
                  Fill in the details below to{" "}
                  {selectedProject ? "update" : "create"} your job.
                </Description>

                <form onSubmit={handleSubmit} className="space-y-5">
                  <div
                    className={`grid gap-6 ${
                      !selectedProject
                        ? "grid-cols-1 lg:grid-cols-2"
                        : "grid-cols-1"
                    }`}
                  >
                    {/* Job Details Column */}
                    <div className="space-y-5">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <FieldTitle title="Name" required />
                          {!selectedProject && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={handleRegenerateRandomName}
                              className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                              startIcon={<RefreshCw className="w-3 h-3" />}
                            >
                              Generate
                            </Button>
                          )}
                        </div>
                        <TextField
                          autoFocus
                          value={name}
                          onChange={(e) => {
                            setName(e.target.value);
                            setNameHelperText("");
                          }}
                          placeholder="Enter job name"
                          error={!!nameHelperText}
                          helperText={nameHelperText}
                        />
                      </div>

                      {!selectedProject && (
                        <div>
                          <FieldTitle title="Process" required />
                          <Select
                            value={process}
                            onValueChange={(value) => {
                              setProcess(value);
                              setProcessHelperText("");
                            }}
                            placeholder="Select a process"
                            options={Object.keys(processes).map((key) => {
                              const proc = processes[key] as any;
                              return {
                                value: proc.name,
                                label: proc.label,
                              };
                            })}
                            error={!!processHelperText}
                            helperText={processHelperText}
                          />
                        </div>
                      )}

                      <div>
                        <FieldTitle title="Description" />
                        <TextField
                          multiline
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Enter job description"
                          rows={4}
                        />
                      </div>
                    </div>

                    {/* Right Column - Media Upload (Always visible for new jobs) */}
                    {!selectedProject && (
                      <div className="space-y-5">
                        <div className="space-y-3">
                          <FieldTitle title="Media URLs (Optional)" />
                          <div className="grid grid-cols-[1fr_auto] gap-2">
                            <TextField
                              type="url"
                              value={urlInput}
                              onChange={(e) => setUrlInput(e.target.value)}
                              placeholder={
                                process === "audio-transcribe-analysis"
                                  ? "https://example.com/media.mp3"
                                  : process === "extract-image-data"
                                  ? "https://example.com/image.jpg"
                                  : "https://example.com/media"
                              }
                              className="w-full"
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  e.preventDefault();
                                  handleAddUrl();
                                }
                              }}
                            />
                            <Button
                              type="button"
                              onClick={handleAddUrl}
                              className="px-3"
                              disabled={!urlInput.trim()}
                            >
                              Add
                            </Button>
                          </div>

                          {/* Display added URLs - Scrollable */}
                          {mediaUrls.length > 0 && (
                            <div className="max-h-32 overflow-y-auto space-y-1 border rounded-md p-2 bg-gray-50 dark:bg-zinc-800">
                              {mediaUrls.map((url, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between bg-white dark:bg-zinc-700 p-2 rounded text-sm"
                                >
                                  <span className="truncate flex-1 text-xs">
                                    {url}
                                  </span>
                                  <Button
                                    type="button"
                                    onClick={() => handleRemoveUrl(index)}
                                    variant="ghost"
                                    size="sm"
                                    className="ml-2 p-1 h-5 w-5"
                                  >
                                    <X className="w-3 h-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="space-y-3">
                          <FieldTitle title="Upload Files (Optional)" />
                          <div>
                            <input
                              type="file"
                              accept={
                                validExtensions.length > 0
                                  ? validExtensions.join(", ")
                                  : "audio/*,image/*,application/*"
                              }
                              multiple
                              ref={fileInputRef}
                              onChange={handleFileChange}
                              className="hidden"
                            />
                            <div
                              onClick={() => fileInputRef.current?.click()}
                              className="w-full h-16 flex flex-col items-center justify-center border-2 border-dashed border-gray-400 dark:border-zinc-600 rounded-md cursor-pointer bg-gray-100 dark:bg-zinc-800 hover:bg-gray-200 dark:hover:bg-zinc-700 transition-colors text-center text-xs text-gray-700 dark:text-gray-300"
                            >
                              <UploadCloud className="w-4 h-4 mb-1" />
                              <span>Click to select files</span>
                              {process && validExtensions.length > 0 ? (
                                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  Allowed: {validExtensions.join(", ")}
                                </span>
                              ) : !process ? (
                                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  Select a process to see allowed file types
                                </span>
                              ) : null}
                            </div>
                          </div>

                          {/* Display selected files - Scrollable */}
                          {mediaFiles.length > 0 && (
                            <div className="max-h-40 overflow-y-auto space-y-1 border rounded-md p-2 bg-gray-50 dark:bg-zinc-800">
                              {mediaFiles.map((file, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between bg-white dark:bg-zinc-700 p-2 rounded text-sm"
                                >
                                  <span className="truncate flex-1 text-xs">
                                    {file.name}
                                  </span>
                                  <Button
                                    type="button"
                                    onClick={() => handleRemoveFile(index)}
                                    variant="ghost"
                                    size="sm"
                                    className="ml-2 p-1 h-5 w-5"
                                  >
                                    <X className="w-3 h-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2 -mt-3">
                    <Button
                      type="button"
                      onClick={handleClose}
                      variant="ghost"
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading}
                      loading={loading}
                      className="btn-primary"
                      startIcon={
                        selectedProject ? (
                          <SaveIcon className="w-4 h-4" />
                        ) : (
                          <Plus className="w-4 h-4" />
                        )
                      }
                    >
                      {selectedProject
                        ? "Update"
                        : jobCreationStep === "uploading" && isUploadingMedia
                        ? "Uploading Media..."
                        : jobCreationStep === "uploading"
                        ? "Creating Job..."
                        : "Create"}
                    </Button>
                  </div>
                </form>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

type Job = {
  _id: string;
  name: string;
  description: string;
  process_name?: string;
};
