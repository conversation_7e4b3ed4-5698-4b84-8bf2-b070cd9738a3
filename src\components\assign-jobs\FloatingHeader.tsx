import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { Search, Users } from "lucide-react";

interface FloatingHeaderProps {
  isVisible: boolean;
  searchTerm: string;
  statusFilter: string;
  assigneeFilter: string;
  unassignedOnly: boolean;
  agents: User[];
  paginatedJobsLength: number;
  selectedJobsSize: number;
  onSearchChange: (value: string) => void;
  onFilterChange: (type: string, value: string) => void;
  onUnassignedToggle: (value: string) => void;
  onSelectAll: () => void;
}

export const FloatingHeader: React.FC<FloatingHeaderProps> = ({
  isVisible,
  searchTerm,
  statusFilter,
  assigneeFilter,
  unassignedOnly,
  agents,
  paginatedJobsLength,
  selectedJobsSize,
  onSearchChange,
  onFilterChange,
  onUnassignedToggle,
  onSelectAll,
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
          className="fixed top-0 left-[257px] h-[92px] right-0 z-50 bg-white border-b border-[#E5E7EB] flex items-center justify-center"
        >
          <div className="container">
            <div className="max-w-screen-xl mx-auto px-6 py-4">
              <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
                {/* Left side - Page title */}
                <div className="flex-shrink-0">
                  <h2 className="text-3xl font-semibold text-[#111827]">
                    Assign Jobs
                  </h2>
                </div>

                {/* Right side - Filters */}
                <div className="flex flex-col sm:flex-row gap-4 flex-1 lg:max-w-4xl">
                  {/* Search Input */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6B7280]" />
                    <Input
                      placeholder="Search jobs or projects..."
                      value={searchTerm}
                      onChange={(e) => onSearchChange(e.target.value)}
                      className="pl-10 h-10 border-[#E5E7EB] focus:border-sky-600 focus:ring-sky-600 w-full"
                    />
                  </div>

                  {/* Status Filter with Search */}
                  <div className="w-48">
                    <SearchableSelect
                      value={statusFilter}
                      onValueChange={(value) => onFilterChange("status", value)}
                      placeholder="Filter by status"
                      searchPlaceholder="Search statuses..."
                      options={[
                        { value: "all", label: "All Statuses" },
                        { value: "pending", label: "Pending" },
                        { value: "in-progress", label: "In Progress" },
                        { value: "completed", label: "Completed" },
                        { value: "failed", label: "Failed" },
                      ]}
                      className="h-10"
                    />
                  </div>

                  {/* Assignee Filter with Search */}
                  <div className="w-48">
                    <SearchableSelect
                      value={unassignedOnly ? "unassigned" : assigneeFilter}
                      onValueChange={onUnassignedToggle}
                      placeholder="Filter by assignee"
                      searchPlaceholder="Search assignees..."
                      options={[
                        { value: "all", label: "All Assignees" },
                        { value: "unassigned", label: "Unassigned Only" },
                        ...agents.map((agent) => ({
                          value: agent._id,
                          label: `${agent.username} (${agent.role})`,
                          searchText: `${agent.username} ${agent.role}`,
                        })),
                      ]}
                      className="h-10"
                    />
                  </div>

                  {/* Select All Button */}
                  {paginatedJobsLength > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onSelectAll}
                      className="h-10 px-3 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white whitespace-nowrap"
                    >
                      <Users className="w-4 h-4 mr-2" />
                      {selectedJobsSize === paginatedJobsLength
                        ? "Deselect All"
                        : "Select All"}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
