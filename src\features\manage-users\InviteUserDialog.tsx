import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import TextField from "@/components/custom/TextField";
import Select from "@/components/custom/Select";
import Button from "@/components/custom/Button";
import { UserPlus, Mail } from "lucide-react";
import { InviteUserService } from "@/services/users.service";
import { useAppSelector } from "@/redux";
import { snackbar } from "@/utils/snackbar.util";

export default function InviteUserDialog({
  open,
  setOpen,
  setInvitationUrlDialogOpen,
  setInvitationToken,
}: InviteUserDialogProps) {
  const [loading, setLoading] = useState(false);
  const [userNameOrEmail, setUserNameOrEmail] = useState("");
  const [role, setRole] = useState("");
  const [userNameOrEmailHelperText, setUserNameOrEmailHelperText] =
    useState("");
  const [roleHelperText, setRoleHelperText] = useState("");

  const roles = useAppSelector((state) => state.roles);

  const handleCloseDialog = (event: React.SyntheticEvent) => {
    setOpen(false);

    setTimeout(() => {
      setLoading(false);
      setUserNameOrEmail("");
      setRole("");
      setUserNameOrEmailHelperText("");
      setRoleHelperText("");
    }, 500);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    let error = false;
    if (!userNameOrEmail) {
      setUserNameOrEmailHelperText("Email or username is required");
      error = true;
    }

    if (!role) {
      setRoleHelperText("Role is required");
      error = true;
    }

    if (error) {
      return;
    }

    setLoading(true);
    const inviteUserResponse = await InviteUserService(userNameOrEmail, role);
    if (inviteUserResponse.success) {
      setInvitationToken(inviteUserResponse.data.registration_token);
      setInvitationUrlDialogOpen(true);
      handleCloseDialog(event);
    } else {
      setLoading(false);
      if (inviteUserResponse.data.includes("Username")) {
        setUserNameOrEmailHelperText(inviteUserResponse.data);
      } else {
        snackbar.showErrorMessage(inviteUserResponse.data);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {/* <div className="w-10 h-10 bg-gradient-to-br from-sky-100 to-sky-200 rounded-lg flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-sky-600" />
            </div> */}
            <div>
              <h3 className="text-lg font-semibold">Invite User</h3>
              <p className="text-sm text-gray-500 font-normal">
                Send an invitation to join your organization
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="space-y-6 py-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Email or Username <span className="text-red-500">*</span>
              </label>
              <TextField
                type="text"
                value={userNameOrEmail}
                placeholder="Enter email or username"
                onChange={(event) => {
                  setUserNameOrEmail(event.target.value);
                  setUserNameOrEmailHelperText("");
                }}
                error={!!userNameOrEmailHelperText}
                helperText={userNameOrEmailHelperText}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Role <span className="text-red-500">*</span>
              </label>
              <Select
                value={role}
                onValueChange={(value) => {
                  setRole(value);
                  setRoleHelperText("");
                }}
                placeholder="Select a role"
                options={roles.map((role) => ({
                  value: role.name,
                  label: role.name,
                }))}
                error={!!roleHelperText}
                helperText={roleHelperText}
              />
            </div>
          </div>
        </form>
        <DialogFooter className="gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            onClick={handleSubmit}
            startIcon={<UserPlus className="h-4 w-4" />}
          >
            Send Invitation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type InviteUserDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setInvitationUrlDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setInvitationToken: React.Dispatch<React.SetStateAction<string>>;
};
