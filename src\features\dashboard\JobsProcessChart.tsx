import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { JobCountByProcessService } from "@/services/metrics.service";
import { useAppSelector } from "@/redux";
import * as echarts from "echarts";

export const JobsProcessChart: React.FC = () => {
  const [processMetrics, setProcessMetrics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const processes = useAppSelector((state) => state.processes);

  useEffect(() => {
    const fetchJobsByProcess = async () => {
      try {
        const response = await JobCountByProcessService();
        if (response.success) {
          setProcessMetrics(Array.isArray(response.data) ? response.data : []);
        }
      } catch (error) {
        console.error("Failed to fetch jobs by process:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsByProcess();
  }, []);

  const getProcessDisplayName = (processName: string) => {
    // Find the process key that matches the process name
    const processKey = Object.keys(processes).find(
      (key) => (processes as any)[key].name === processName
    );
    return processKey
      ? (processes as any)[processKey].label
      : processName.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const getProcessColors = () => [
    "#64748b", // slate
    "#059669", // emerald
    "#0284c7", // sky
    "#7c3aed", // violet
    "#dc2626", // red
    "#ea580c", // orange
    "#16a34a", // green
    "#0891b2", // cyan
  ];

  useEffect(() => {
    if (!loading && processMetrics.length > 0 && chartRef.current) {
      // Initialize chart
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      chartInstance.current = echarts.init(chartRef.current);

      const sortedMetrics = [...processMetrics].sort(
        (a, b) => b.value - a.value
      );
      const colors = getProcessColors();

      const chartData = processMetrics
        .map((metric, index) => ({
          name: getProcessDisplayName(metric.title),
          value: metric.value,
          rawIndex: index,
        }))
        .sort((a, b) => b.value - a.value) // Sort by value descending
        .map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length],
          },
          label: {
            show: index < 10, // Show labels for top 10
            formatter: `{b}\n${item.value} jobs`,
            fontSize: index < 5 ? 14 : 12,
            fontWeight: index < 5 ? "bold" : "normal",
            color: "#374151",
            lineHeight: 18,
          },
          labelLine: {
            show: index < 10,
            length: index < 5 ? 20 : 12,
            length2: index < 5 ? 10 : 6,
            lineStyle: {
              color: "#d1d5db", // gray-300
            },
          },
          emphasis: {
            label: {
              show: true,
              formatter: `{b}\n${item.value} jobs`,
              fontSize: 14,
              fontWeight: "bold",
              color: "#111827",
            },
          },
        }));

      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} jobs ({d}%)",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#e5e7eb",
          borderWidth: 1,
          textStyle: {
            color: "#374151",
            fontSize: 12,
          },
        },
        legend: {
          orient: "vertical",
          left: "left",
          top: "middle",
          textStyle: {
            fontSize: 12,
            color: "#6b7280",
          },
          itemGap: 12,
          itemWidth: 14,
          itemHeight: 14,
        },
        series: [
          {
            name: "Jobs by Process",
            type: "pie",
            radius: ["45%", "75%"],
            center: ["65%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 6,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false, // Base fallback
            },
            labelLine: {
              show: false, // Base fallback
            },
            emphasis: {
              scale: true,
              scaleSize: 5,
              itemStyle: {
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.3)",
              },
            },
            data: chartData,
          },
        ],
      };

      chartInstance.current.setOption(option);

      // Handle resize
      const handleResize = () => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      };

      window.addEventListener("resize", handleResize);

      return () => {
        window.removeEventListener("resize", handleResize);
        if (chartInstance.current) {
          chartInstance.current.dispose();
        }
      };
    }
  }, [loading, processMetrics]);

  if (loading) {
    return (
      <Card className="border border-gray-200 bg-white shadow-sm rounded-md h-[600px]">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-slate-800">
            Jobs by Process
          </CardTitle>
          <p className="text-sm text-slate-600 mt-2">
            Visual breakdown of job distribution across process types
          </p>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-600"></div>
        </CardContent>
      </Card>
    );
  }

  if (processMetrics.length === 0) {
    return (
      <Card className="border border-gray-200 bg-white shadow-sm rounded-md h-[600px]">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-slate-800">
            Jobs by Process Distribution
          </CardTitle>
          <p className="text-sm text-slate-600 mt-2">
            Visual breakdown of job distribution across process types
          </p>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-slate-500">
            <div className="w-16 h-16 mx-auto mb-4 rounded-md bg-slate-100 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-slate-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <p className="font-medium">No process data available</p>
            <p className="text-sm mt-1">
              Check back later for process distribution
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border border-gray-200 bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md h-[600px] flex flex-col">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold text-slate-800">
          Jobs by Process Distribution
        </CardTitle>
        <p className="text-sm text-slate-600 mt-2">
          Visual breakdown of job distribution across process types
        </p>
      </CardHeader>
      <CardContent className="flex-1 p-6">
        <div ref={chartRef} className="h-full w-full" />
      </CardContent>
    </Card>
  );
};
