import { Fragment, useEffect, useState, useRef } from "react";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Description,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Plus, SaveIcon, UploadCloud, X, RefreshCw } from "lucide-react";
import TextField from "@/components/custom/TextField";
import Button from "@/components/custom/Button";
import FieldTitle from "@/components/custom/FieldTitle";
import Select from "@/components/custom/Select";
import {
  CreateJobService,
  AddMediaToJobService,
} from "@/services/jobs.service";
import { CreateProjectService } from "@/services/projects.service";
import { useAppSelector } from "@/redux";
import { snackbar } from "@/utils/snackbar.util";
import {
  generateRandomProjectName,
  generateRandomJobName,
} from "@/utils/generateRandomName.util";
import { generateRandomDescription } from "@/utils/generatedescription.util";

type DashboardJobDialogProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  onJobCreated?: (projectData?: any, jobData?: any) => void;
};

export default function DashboardJobDialog({
  open,
  setOpen,
  onJobCreated,
}: DashboardJobDialogProps) {
  const [loading, setLoading] = useState(false);

  // Project fields
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [projectNameHelperText, setProjectNameHelperText] = useState("");
  const [projectDescriptionHelperText, setProjectDescriptionHelperText] =
    useState("");

  // Job fields
  const [jobName, setJobName] = useState("");
  const [jobDescription, setJobDescription] = useState("");
  const [process, setProcess] = useState("");
  const [jobNameHelperText, setJobNameHelperText] = useState("");
  const [processHelperText, setProcessHelperText] = useState("");

  // Media handling
  const [urlInput, setUrlInput] = useState("");
  const [mediaUrls, setMediaUrls] = useState<string[]>([]);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [jobCreationStep, setJobCreationStep] = useState<
    "form" | "uploading" | "complete"
  >("form");
  const [isUploadingMedia, setIsUploadingMedia] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const processes = useAppSelector((state) => state.processes);

  // Generator functions
  const handleGenerateProjectName = () => {
    setProjectName(generateRandomProjectName());
    setProjectNameHelperText("");
  };

  const handleGenerateProjectDescription = () => {
    setProjectDescription(generateRandomDescription());
    setProjectDescriptionHelperText("");
  };

  const handleGenerateJobName = () => {
    setJobName(generateRandomJobName());
    setJobNameHelperText("");
  };

  const handleGenerateJobDescription = () => {
    setJobDescription(generateRandomDescription());
  };

  useEffect(() => {
    if (open) {
      // Reset all fields when dialog opens
      setProjectName("");
      setProjectDescription("");
      setJobName("");
      setJobDescription("");
      setProcess("");
      setProjectNameHelperText("");
      setProjectDescriptionHelperText("");
      setJobNameHelperText("");
      setProcessHelperText("");
      setUrlInput("");
      setMediaUrls([]);
      setMediaFiles([]);
      setJobCreationStep("form");
      setIsUploadingMedia(false);
    }
  }, [open]);

  const handleClose = () => {
    setOpen(false);
    setTimeout(() => {
      setLoading(false);
      setProjectName("");
      setProjectDescription("");
      setJobName("");
      setJobDescription("");
      setProcess("");
      setProjectNameHelperText("");
      setProjectDescriptionHelperText("");
      setJobNameHelperText("");
      setProcessHelperText("");
      setUrlInput("");
      setMediaUrls([]);
      setMediaFiles([]);
      setJobCreationStep("form");
      setIsUploadingMedia(false);
    }, 300);
  };

  // Get valid extensions for the selected process
  const validExtensions = process
    ? processes[process as ProcessName]?.valid_extensions || []
    : [];

  // Media handling functions
  const handleAddUrl = () => {
    if (urlInput.trim()) {
      setMediaUrls([...mediaUrls, urlInput.trim()]);
      setUrlInput("");
    }
  };

  const handleRemoveUrl = (index: number) => {
    setMediaUrls(mediaUrls.filter((_, i) => i !== index));
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setMediaFiles([...mediaFiles, ...files]);
  };

  const handleRemoveFile = (index: number) => {
    setMediaFiles(mediaFiles.filter((_, i) => i !== index));
  };

  const validateMediaFiles = () => {
    if (mediaFiles.length === 0 && mediaUrls.length === 0) {
      snackbar.showErrorMessage("Please add at least one media file or URL");
      return false;
    }

    // Validate file extensions
    for (const file of mediaFiles) {
      const extension = file.name.split(".").pop()?.toLowerCase();
      if (extension) {
        // Check if the extension matches any of the valid extensions (with or without dot)
        const isValid = validExtensions.some((validExt) => {
          const cleanValidExt = validExt.startsWith(".")
            ? validExt.slice(1)
            : validExt;
          return cleanValidExt.toLowerCase() === extension;
        });

        if (!isValid) {
          const cleanExtensions = validExtensions.map((ext) =>
            ext.startsWith(".") ? ext.slice(1) : ext
          );
          snackbar.showErrorMessage(
            `Invalid file type: ${
              file.name
            }. Allowed types: ${cleanExtensions.join(", ")}`
          );
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset error messages
    setProjectNameHelperText("");
    setProjectDescriptionHelperText("");
    setJobNameHelperText("");
    setProcessHelperText("");

    // Validate required fields
    let hasErrors = false;

    if (!projectName.trim()) {
      setProjectNameHelperText("Enter project name");
      hasErrors = true;
    }

    if (!projectDescription.trim()) {
      setProjectDescriptionHelperText("Enter project description");
      hasErrors = true;
    }

    if (!jobName.trim()) {
      setJobNameHelperText("Enter job name");
      hasErrors = true;
    }

    if (!process) {
      setProcessHelperText("Select a process");
      hasErrors = true;
    }

    if (hasErrors) {
      return;
    }

    // Validate media files
    if (!validateMediaFiles()) {
      return;
    }

    setLoading(true);

    try {
      // Step 1: Create the project
      setJobCreationStep("uploading");
      const projectResponse = await CreateProjectService(
        projectName,
        projectDescription
      );

      if (!projectResponse.success) {
        setLoading(false);
        setJobCreationStep("form");
        setProjectNameHelperText(
          projectResponse.data?.message || "Something went wrong"
        );
        snackbar.showErrorMessage(projectResponse.data);
        return;
      }

      const createdProject = projectResponse.data;

      // Step 2: Create the job
      const jobResponse = await CreateJobService(
        createdProject._id,
        jobName,
        jobDescription,
        process
      );

      if (!jobResponse.success) {
        setLoading(false);
        setJobCreationStep("form");
        setJobNameHelperText(
          jobResponse.data?.message || "Something went wrong"
        );
        snackbar.showErrorMessage(jobResponse.data);
        return;
      }

      const createdJob = jobResponse.data;

      // Step 3: Upload media if any files or URLs are provided
      if (mediaUrls.length > 0 || mediaFiles.length > 0) {
        setIsUploadingMedia(true);
        const uploadResponse = await AddMediaToJobService(
          createdJob._id,
          mediaUrls,
          mediaFiles
        );

        if (!uploadResponse.success) {
          setLoading(false);
          setIsUploadingMedia(false);
          setJobCreationStep("form");
          snackbar.showErrorMessage(
            uploadResponse.data ||
              "Media upload failed. Job created but media not uploaded."
          );
          // Still call onJobCreated since project and job were created successfully
          onJobCreated?.(createdProject, createdJob);
          handleClose();
          return;
        }
      }

      // Step 4: Success
      setJobCreationStep("complete");
      snackbar.showSuccessMessage("Job created successfully!");
      handleClose();
      onJobCreated?.(createdProject, createdJob);
    } catch (error) {
      setLoading(false);
      setIsUploadingMedia(false);
      setJobCreationStep("form");
      snackbar.showErrorMessage("An unexpected error occurred");
    }
  };

  return (
    <Transition appear show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-150"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/70" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-200"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-150"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <DialogTitle
                      as="h3"
                      className="text-xl font-semibold leading-6 text-gray-900"
                    >
                      Create New Job
                    </DialogTitle>
                    <Description className="text-sm text-gray-600 mt-1">
                      Create a new job with media upload
                    </Description>
                  </div>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    disabled={loading}
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Left Column - Project & Job Details */}
                    <div className="space-y-4">
                      {/* Project Section */}
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-lg font-medium text-gray-900 mb-3">
                          Project Details
                        </h4>
                        <div className="space-y-3">
                          <div>
                            <FieldTitle title="Project Name" required />
                            <TextField
                              value={projectName}
                              onChange={(e) => {
                                setProjectName(e.target.value);
                                setProjectNameHelperText("");
                              }}
                              placeholder="Enter project name"
                              error={!!projectNameHelperText}
                              helperText={projectNameHelperText}
                              rightButton={
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={handleGenerateProjectName}
                                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                                  startIcon={<RefreshCw className="w-3 h-3" />}
                                  title="Random Name Generator"
                                ></Button>
                              }
                            />
                          </div>

                          <div>
                            <FieldTitle title="Project Description" required />
                            <TextField
                              multiline
                              value={projectDescription}
                              onChange={(e) => {
                                setProjectDescription(e.target.value);
                                setProjectDescriptionHelperText("");
                              }}
                              placeholder="Enter project description"
                              rows={3}
                              error={!!projectDescriptionHelperText}
                              helperText={projectDescriptionHelperText}
                              rightButton={
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={handleGenerateProjectDescription}
                                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                                  startIcon={<RefreshCw className="w-3 h-3" />}
                                  title="Random Description Generator"
                                ></Button>
                              }
                            />
                          </div>
                        </div>
                      </div>

                      {/* Job Section */}
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h4 className="text-lg font-medium text-gray-900 mb-3">
                          Job Details
                        </h4>
                        <div className="space-y-3">
                          <div>
                            <FieldTitle title="Job Name" required />
                            <TextField
                              value={jobName}
                              onChange={(e) => {
                                setJobName(e.target.value);
                                setJobNameHelperText("");
                              }}
                              placeholder="Enter job name"
                              error={!!jobNameHelperText}
                              helperText={jobNameHelperText}
                              rightButton={
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={handleGenerateJobName}
                                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                                  startIcon={<RefreshCw className="w-3 h-3" />}
                                  title="Random Name Generator"
                                ></Button>
                              }
                            />
                          </div>

                          <div>
                            <FieldTitle title="Process" required />
                            <Select
                              value={process}
                              onValueChange={(value) => {
                                setProcess(value);
                                setProcessHelperText("");
                              }}
                              placeholder="Select a process"
                              options={Object.keys(processes).map((key) => {
                                const proc = processes[key] as any;
                                return {
                                  value: proc.name,
                                  label: proc.label,
                                };
                              })}
                              error={!!processHelperText}
                              helperText={processHelperText}
                            />
                          </div>

                          <div>
                            <FieldTitle title="Job Description" />
                            <TextField
                              multiline
                              value={jobDescription}
                              onChange={(e) =>
                                setJobDescription(e.target.value)
                              }
                              placeholder="Enter job description"
                              rows={3}
                              rightButton={
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={handleGenerateJobDescription}
                                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                                  startIcon={<RefreshCw className="w-3 h-3" />}
                                  title="Random Description Generator"
                                ></Button>
                              }
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right Column - Media Upload */}
                    <div className="space-y-4">
                      <div className="border border-gray-200 rounded-lg p-4 h-fit">
                        <h4 className="text-lg font-medium text-gray-900 mb-3">
                          Media Upload
                        </h4>
                        <div className="space-y-4">
                          {/* URL Input */}
                          <div>
                            <FieldTitle title="Media URLs" />
                            <div className="flex gap-2">
                              <TextField
                                value={urlInput}
                                onChange={(e) => setUrlInput(e.target.value)}
                                placeholder="Enter media URL"
                                className="flex-1"
                              />
                              <Button
                                type="button"
                                onClick={handleAddUrl}
                                disabled={!urlInput.trim()}
                                variant="outline"
                                className="shrink-0"
                              >
                                Add URL
                              </Button>
                            </div>
                            {mediaUrls.length > 0 && (
                              <div className="mt-3 space-y-2">
                                <p className="text-sm font-medium text-gray-700">
                                  Added URLs:
                                </p>
                                {mediaUrls.map((url, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border"
                                  >
                                    <span className="text-sm text-gray-700 truncate flex-1 mr-2">
                                      {url}
                                    </span>
                                    <button
                                      type="button"
                                      onClick={() => handleRemoveUrl(index)}
                                      className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50"
                                    >
                                      <X className="w-4 h-4" />
                                    </button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* File Upload */}
                          <div>
                            <FieldTitle title="Media Files" />
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-gray-400 transition-colors">
                              <div className="text-center">
                                <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                                <div className="mt-3">
                                  <button
                                    type="button"
                                    onClick={() =>
                                      fileInputRef.current?.click()
                                    }
                                    className="text-blue-600 hover:text-blue-500 font-medium text-sm"
                                  >
                                    Click to upload files
                                  </button>
                                  <p className="text-xs text-gray-500 mt-2">
                                    {validExtensions.length > 0
                                      ? `Supported formats: ${validExtensions.join(
                                          ", "
                                        )}`
                                      : "Select a process to see supported formats"}
                                  </p>
                                </div>
                              </div>
                              <input
                                ref={fileInputRef}
                                type="file"
                                multiple
                                accept={validExtensions.join(",")}
                                onChange={handleFileSelect}
                                className="hidden"
                              />
                            </div>
                            {mediaFiles.length > 0 && (
                              <div className="mt-3 space-y-2">
                                <p className="text-sm font-medium text-gray-700">
                                  Selected Files:
                                </p>
                                {mediaFiles.map((file, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border"
                                  >
                                    <div className="flex-1 mr-2">
                                      <span className="text-sm text-gray-700 truncate block">
                                        {file.name}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {(file.size / 1024 / 1024).toFixed(2)}{" "}
                                        MB
                                      </span>
                                    </div>
                                    <button
                                      type="button"
                                      onClick={() => handleRemoveFile(index)}
                                      className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50"
                                    >
                                      <X className="w-4 h-4" />
                                    </button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 -mt-3">
                    <Button
                      type="button"
                      onClick={handleClose}
                      variant="ghost"
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading}
                      loading={loading}
                      className="btn-primary"
                      startIcon={<Plus className="w-4 h-4" />}
                    >
                      {jobCreationStep === "uploading" && isUploadingMedia
                        ? "Uploading Media..."
                        : jobCreationStep === "uploading"
                        ? "Creating Job..."
                        : "Create Job"}
                    </Button>
                  </div>
                </form>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
