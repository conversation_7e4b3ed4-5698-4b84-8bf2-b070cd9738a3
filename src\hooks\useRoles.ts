import { setRoles } from "@/redux/slices/rolesSlice";
import { GetRolesService } from "@/services/users.service";
import { snackbar } from "@/utils/snackbar.util";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

const useFetchRoles = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    (async () => {
      try {
        const rolesResponse: RolesResponse = await GetRolesService();
        if (rolesResponse.success) {
          dispatch(setRoles(rolesResponse.data.data as Role[]));
        }
        else {
          console.error("useRoles() => Error fetching roles: ", rolesResponse.data);
          snackbar.showErrorMessage(rolesResponse.data as unknown as string);
        }
      }
      catch (error: any) {
        console.error("useRoles() => Error fetching roles: ", error);
        snackbar.showErrorMessage(error?.message || "An error occurred while fetching roles");
      }
    })();
  }, [dispatch]);
};

export default useFetchRoles;

type Role = {
  _id: string;
  name: string;
};

type RolesResponse = {
  success: boolean;
  data: {
    data: Role[] | string;
  };
};