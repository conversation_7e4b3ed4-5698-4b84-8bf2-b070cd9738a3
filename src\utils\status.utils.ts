export function getStatusBadgeVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  const normalizedStatus = status.toLowerCase();
  
  switch (normalizedStatus) {
    case "completed":
      return "default"; // Green
    case "in-progress":
    case "processing":
      return "secondary"; // Blue
    case "pending":
      return "outline"; // Yellow/amber
    case "failed":
    case "error":
      return "destructive"; // Red
    default:
      return "outline";
  }
}

export function getStatusColor(status: string): string {
  const normalizedStatus = status.toLowerCase();
  
  switch (normalizedStatus) {
    case "completed":
      return "text-green-700 bg-green-50 border-green-200";
    case "in-progress":
    case "processing":
      return "text-blue-700 bg-blue-50 border-blue-200";
    case "pending":
      return "text-amber-700 bg-amber-50 border-amber-200";
    case "failed":
    case "error":
      return "text-red-700 bg-red-50 border-red-200";
    default:
      return "text-gray-700 bg-gray-50 border-gray-200";
  }
}
