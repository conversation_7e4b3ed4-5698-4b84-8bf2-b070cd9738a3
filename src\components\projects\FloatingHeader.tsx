import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import Button from "@/components/custom/Button";
import TextField from "@/components/custom/TextField";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Search, PlusIcon } from "lucide-react";
import { DateRange } from "react-day-picker";

interface FloatingHeaderProps {
  isVisible: boolean;
  searchQuery: string;
  dateRange: DateRange | undefined;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onDateRangeChange: (range: DateRange | undefined) => void;
  onClearFilters: () => void;
  onNewProject: () => void;
  hasActiveFilters: boolean;
}

export const FloatingHeader: React.FC<FloatingHeaderProps> = ({
  isVisible,
  searchQuery,
  dateRange,
  onSearchChange,
  onDateRangeChange,
  onClearFilters,
  onNewProject,
  hasActiveFilters,
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
          className="fixed top-0 left-[257px] h-[92px] right-0 z-50 bg-white border-b border-[#E5E7EB] flex items-center justify-center"
        >
          <div className="container">
            <div className="max-w-7xl mx-auto px-6 py-4">
              <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
                {/* Left side - Page title */}
                <div className="flex-shrink-0">
                  <h2 className="text-3xl font-semibold text-[#111827]">
                    Projects
                  </h2>
                </div>

                {/* Right side - Filters and Actions */}
                <div className="flex flex-col sm:flex-row gap-4 flex-1 lg:max-w-4xl">
                  {/* Search Input */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <TextField
                      placeholder="Search projects..."
                      value={searchQuery}
                      onChange={onSearchChange}
                      className="pl-10 h-10 w-full"
                    />
                  </div>

                  {/* Date Range Filter */}
                  <div className="w-64">
                    <DateRangePicker
                      className="w-full"
                      date={dateRange}
                      onDateChange={onDateRangeChange}
                      placeholder="Filter by date"
                      numberOfMonths={2}
                      align="start"
                      disableFuture={true}
                    />
                  </div>

                  {/* Clear Filters Button */}
                  {hasActiveFilters && (
                    <Button
                      onClick={onClearFilters}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 px-3 py-2 h-10 whitespace-nowrap"
                    >
                      Clear filters
                    </Button>
                  )}

                  {/* New Project Button */}
                  <Button
                    onClick={onNewProject}
                    startIcon={<PlusIcon className="w-4 h-4" />}
                    className="px-4 py-2 h-10 whitespace-nowrap"
                  >
                    New Project
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
