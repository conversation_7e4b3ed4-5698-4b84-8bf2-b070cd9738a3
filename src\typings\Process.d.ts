type Process = {
  "audio-transcribe-analysis": {
    name: "audio-transcribe-analysis";
    label: "Audio Analysis";
    media_type: "audio";
    valid_extensions: string[];
    description: string;
    schema: {
      urls: string[];
      project_name?: string;
      job_name?: string;
    };
  };

  "generic-entity-extraction": {
    name: "generic-entity-extraction";
    label: "Entity Extraction";
    media_type: "document";
    valid_extensions: string[];
    description: string;
    schema: {
      input_type: "text" | "url" | "audio";
      inputs: string[];
      project_name?: string;
      job_name?: string;
    };
  };

  "extract-image-data": {
    name: "extract-image-data";
    label: "Image Data Extraction";
    media_type: "image";
    valid_extensions: string[];
    description: string;
    schema: {
      urls: string[];
      project_name?: string;
      job_name?: string;
    };
  };
};

// process name type s
type ProcessName = Process[keyof Process]["name"];
