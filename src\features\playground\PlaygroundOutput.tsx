import React, { useState } from "react";
import Button from "@/components/custom/Button";
import { JobGenericFile } from "@/features/jobs/JobGenericFile";
import <PERSON>sonView from "@uiw/react-json-view";
import { motion, AnimatePresence } from "framer-motion";
import {
  CheckCircle,
  Download,
  Sparkles,
  AlertCircle,
  Eye,
  EyeOff,
  Bug,
  FileText,
} from "lucide-react";

// Updated interfaces to match new response format
interface OutputResult {
  status: string;
  data?: Record<string, any>; // Dynamic data structure - optional for error cases
  message?: string; // Error message when status is "error"
  raw_response?: string; // Raw response when there's a parsing error
  price_nep?: number;
  usage_metadata?: {
    prompt_token_count: number;
    candidates_token_count: number;
  };
}

interface JobOutput {
  output_id: string;
  source_media_id: string;
  object_name: string;
  process_type: string;
  result: OutputResult;
  presigned_url: string;
}

interface PlaygroundOutputProps {
  response: {
    status: string;
    project_id: string;
    job_id: string;
    results: {
      job_id: string;
      status: string;
      outputs: JobOutput[];
    };
  } | null;
  isProcessing: boolean;
  error?: string | null;
  processingStep?: string;
  onViewInput?: () => void;
  showInputMediaDialog?: boolean;
  elapsedTime?: number;
  formatElapsedTime?: (seconds: number) => string;
}

// Simplified data renderer
const RenderDataItem: React.FC<{ keyName: string; value: any }> = ({
  keyName,
  value,
}) => {
  if (Array.isArray(value)) {
    return (
      <div className="mb-2">
        <div className="font-medium text-sky-600">{keyName}:</div>
        <div className="ml-4 border-l border-[#EAEAEA] pl-2">
          {value.map((item, index) => (
            <div key={index} className="flex items-start py-1">
              <span className="text-[#666] mr-2">•</span>
              <div>
                {typeof item === "object" ? (
                  <div className="space-y-2">
                    {Object.entries(item).map(([subKey, subValue]) => (
                      <RenderDataItem
                        key={subKey}
                        keyName={subKey}
                        value={subValue}
                      />
                    ))}
                  </div>
                ) : (
                  <span className="text-[#202020]">{item}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (value && typeof value === "object") {
    return (
      <div className="mb-2">
        <div className="font-medium text-sky-600">{keyName}:</div>
        <div className="ml-4 border-l border-[#EAEAEA] pl-2">
          {Object.entries(value).map(([subKey, subValue]) => (
            <RenderDataItem key={subKey} keyName={subKey} value={subValue} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap mb-2">
      <div className="font-medium text-sky-600 w-full md:w-1/3">{keyName}:</div>
      <div className="w-full md:w-2/3 text-[#202020] break-words">
        {value?.toString() || "N/A"}
      </div>
    </div>
  );
};

export const PlaygroundOutput: React.FC<PlaygroundOutputProps> = ({
  response,
  isProcessing,
  error,
  onViewInput,
  showInputMediaDialog,
  elapsedTime = 0,
  formatElapsedTime = (s) => `in ${Math.round(s)}s`,
}) => {
  const [showOutputJson, setShowOutputJson] = useState(false);
  console.log("PlaygroundOutput response:", response);

  // Extract first output's result with enhanced error handling
  let firstOutput: any = null;
  let parseError: string | null = null;
  let processingError: string | null = null;

  try {
    if (response?.results?.outputs?.[0]?.result) {
      firstOutput = response.results.outputs[0].result;

      // Check if the result itself indicates an error
      if (firstOutput.status === "error") {
        processingError =
          firstOutput.message || "Processing failed with an unknown error";
      }
    }
  } catch (err) {
    console.error("Error parsing response:", err);
    parseError = "Failed to parse response data";
  }

  // Error state with enhanced formatting
  if (error) {
    // Try to format the error message better
    let displayError = error;
    let errorDetails = null;

    try {
      // Check if error is a JSON string that can be parsed
      if (typeof error === "string" && error.includes("{")) {
        const parsed = JSON.parse(error);
        if (parsed.detail) {
          displayError = parsed.detail;
        }
      }
    } catch {
      // If parsing fails, use the original error
    }

    // Extract more user-friendly error messages
    if (displayError.includes("Schema generation failed:")) {
      const parts = displayError.split("Schema generation failed:");
      if (parts.length > 1) {
        displayError = parts[parts.length - 1].trim();
        errorDetails =
          "This error occurred during schema generation from your uploaded file.";
      }
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="h-full flex flex-col border border-red-200 bg-red-50 rounded-md shadow-sm"
      >
        <div className="p-6 flex flex-col items-center justify-center flex-grow text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
            className="w-12 h-12 md:w-16 md:h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 shadow"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ delay: 0.6, duration: 0.6, ease: "easeInOut" }}
            >
              <AlertCircle className="w-6 h-6 md:w-8 md:h-8 text-red-600" />
            </motion.div>
          </motion.div>
          <motion.h3
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="text-base md:text-lg font-medium text-red-900 mb-2"
          >
            Processing Error
          </motion.h3>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.4 }}
            className="text-red-700 text-sm max-w-xs md:max-w-sm leading-relaxed mb-3"
          >
            {displayError}
          </motion.p>
          {errorDetails && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.4 }}
              className="text-red-600 text-xs max-w-xs md:max-w-sm leading-relaxed"
            >
              {errorDetails}
            </motion.p>
          )}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.4 }}
            className="mt-4 text-xs text-red-500"
          >
            <details>
              <summary className="cursor-pointer hover:text-red-700">
                Show technical details
              </summary>
              <pre className="mt-2 p-2 bg-red-100 rounded text-left overflow-auto max-h-32 max-w-xs md:max-w-sm">
                {error}
              </pre>
            </details>
          </motion.div>
        </div>
      </motion.div>
    );
  }

  // Processing error state (when result.status is "error")
  if (processingError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="h-full flex flex-col border border-orange-200 bg-orange-50 rounded-md shadow-sm"
      >
        <div className="p-6 py-12 flex flex-col items-center justify-center flex-grow text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
            className="w-12 h-12 md:w-16 md:h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4 shadow"
          >
            <motion.div
              animate={{ rotate: [0, -10, 10, 0] }}
              transition={{ delay: 0.6, duration: 0.8, ease: "easeInOut" }}
            >
              <Bug className="w-6 h-6 md:w-8 md:h-8 text-orange-600" />
            </motion.div>
          </motion.div>
          <motion.h3
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="text-base md:text-lg font-medium text-orange-900 mb-2"
          >
            Error Occurred
          </motion.h3>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.4 }}
            className="text-orange-700 text-sm max-w-xs md:max-w-sm leading-relaxed mb-3"
          >
            Something went wrong while processing your request. Please try
            again.
          </motion.p>

          {/* {firstOutput?.raw_response && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.4 }}
              className="mt-4 text-xs text-orange-500 w-full max-w-md"
            >
              <details>
                <summary className="cursor-pointer hover:text-orange-700 font-medium">
                  Show AI response
                </summary>
                <div className="mt-2 p-3 bg-orange-100 rounded text-left overflow-auto max-h-40 text-orange-800">
                  <pre className="whitespace-pre-wrap text-xs">
                    {firstOutput.raw_response.length > 500
                      ? firstOutput.raw_response.substring(0, 500) + "..."
                      : firstOutput.raw_response}
                  </pre>
                </div>
              </details>
            </motion.div>
          )} */}
        </div>
      </motion.div>
    );
  }

  // Parse error state
  if (parseError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="h-full flex flex-col border border-red-200 bg-red-50 rounded-md shadow-sm"
      >
        <div className="p-6 flex flex-col items-center justify-center flex-grow text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
            className="w-12 h-12 md:w-16 md:h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 shadow"
          >
            <AlertCircle className="w-6 h-6 md:w-8 md:h-8 text-red-600" />
          </motion.div>
          <motion.h3
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="text-base md:text-lg font-medium text-red-900 mb-2"
          >
            Error Occurred
          </motion.h3>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.4 }}
            className="text-red-700 text-sm max-w-xs md:max-w-sm leading-relaxed"
          >
            Something went wrong while processing your request. Please try
            again.
          </motion.p>
        </div>
      </motion.div>
    );
  }

  // Processing state is now handled in the main playground page
  if (isProcessing) {
    return null;
  }

  if (!response) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="h-full flex flex-col border border-gray-200 bg-white rounded-md shadow-sm"
      >
        <div className="p-12 flex flex-col items-center justify-center flex-grow text-center">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.8, ease: "easeOut" }}
            className="w-24 h-24 bg-gradient-to-br from-sky-600 to-sky-700 rounded-2xl flex items-center justify-center mb-8 shadow-lg"
          >
            <motion.div
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1],
              }}
              transition={{
                delay: 1,
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut",
              }}
            >
              <Sparkles className="w-10 h-10 text-white" />
            </motion.div>
          </motion.div>
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="text-2xl font-semibold text-gray-900 mb-4"
          >
            Ready to Process
          </motion.h3>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="text-gray-600 text-lg max-w-md leading-relaxed"
          >
            Add a file and enter your prompt to see AI-powered extraction
            results appear here.
          </motion.p>
        </div>
      </motion.div>
    );
  }

  const handleDownloadOutput = () => {
    if (!response) return;
    const jsonData = JSON.stringify(response, null, 2);
    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "ai_output.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="h-full flex flex-col border border-gray-200 bg-white rounded-md"
    >
      <div className="flex flex-col flex-grow overflow-hidden">
        {/* Output Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="flex items-center justify-between mb-8 border-b p-6"
        >
          <div className="flex items-center gap-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, duration: 0.4, ease: "easeOut" }}
              className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-md flex items-center justify-center shadow-lg"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.6, duration: 0.3 }}
              >
                <CheckCircle className="w-6 h-6 text-white" />
              </motion.div>
            </motion.div>
            <div className="flex items-center gap-3">
              <motion.h3
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.4 }}
                className="text-xl font-semibold text-gray-900"
              >
                Processing Complete
              </motion.h3>
              {elapsedTime > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7, duration: 0.3 }}
                  className="px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full font-mono tabular-nums"
                >
                  in {Math.round(elapsedTime)}s
                </motion.div>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="ghost"
                // size="sm"
                onClick={() => setShowOutputJson(!showOutputJson)}
                startIcon={
                  <motion.div
                    animate={{ rotate: showOutputJson ? 180 : 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    {showOutputJson ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </motion.div>
                }
              >
                {showOutputJson ? "Rendered Output" : "Raw Output"}
              </Button>
            </motion.div>
            {onViewInput && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  variant="outline"
                  onClick={onViewInput}
                  startIcon={<FileText className="w-4 h-4" />}
                >
                  {showInputMediaDialog ? "Hide Input" : "View Input"}
                </Button>
              </motion.div>
            )}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="outline"
                // size="sm"
                onClick={handleDownloadOutput}
                startIcon={<Download className="w-4 h-4" />}
              >
                Download
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* Main Output Content */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="flex-1 h-full space-y-6 overflow-y-auto pr-2"
        >
          <AnimatePresence mode="wait">
            {/* Output JSON Viewer with Error Protection */}
            {showOutputJson && response && (
              <motion.div
                key="json-view"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="h-full overflow-auto"
              >
                {(() => {
                  try {
                    const outputData = response?.results?.outputs?.[0];
                    if (!outputData) {
                      return (
                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-center gap-2 text-yellow-700">
                            <AlertCircle className="w-4 h-4" />
                            <span className="text-sm font-medium">
                              No Output Data
                            </span>
                          </div>
                          <p className="text-yellow-600 text-sm mt-1">
                            The response does not contain any output data to
                            display.
                          </p>
                        </div>
                      );
                    }

                    return (
                      <JsonView
                        value={outputData}
                        style={{
                          backgroundColor: "#ffffff",
                          fontSize: "14px",
                        }}
                        displayDataTypes={false}
                        displayObjectSize={false}
                        enableClipboard={false}
                      />
                    );
                  } catch (error) {
                    console.error("Error rendering JSON view:", error);
                    return (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                        <div className="flex items-center gap-2 text-red-700">
                          <AlertCircle className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            JSON Display Error
                          </span>
                        </div>
                        <p className="text-red-600 text-sm mt-1">
                          Unable to display the JSON data. The response may be
                          corrupted.
                        </p>
                        <details className="mt-2">
                          <summary className="text-xs text-red-500 cursor-pointer">
                            Show raw response
                          </summary>
                          <pre className="text-xs text-red-600 mt-1 p-2 bg-red-100 rounded overflow-auto max-h-32">
                            {JSON.stringify(response, null, 2)}
                          </pre>
                        </details>
                      </div>
                    );
                  }
                })()}
              </motion.div>
            )}

            {/* AI Response using JobGenericFile with Error Protection */}
            {firstOutput?.data && !showOutputJson && (
              <motion.div
                key="rendered-view"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                {(() => {
                  try {
                    // Validate data before passing to JobGenericFile
                    if (
                      !firstOutput.data ||
                      typeof firstOutput.data !== "object"
                    ) {
                      return (
                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-center gap-2 text-yellow-700">
                            <AlertCircle className="w-4 h-4" />
                            <span className="text-sm font-medium">
                              Invalid Output Data
                            </span>
                          </div>
                          <p className="text-yellow-600 text-sm mt-1">
                            The output data is not in a valid format for
                            display.
                          </p>
                        </div>
                      );
                    }

                    return (
                      <JobGenericFile
                        data={firstOutput.data}
                        filename="extracted_data.json"
                        showDownload={false}
                        showVerification={false}
                        showFilename={false}
                        showRawToggle={false}
                        className="mb-4"
                      />
                    );
                  } catch (error) {
                    console.error("Error rendering output data:", error);
                    return (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                        <div className="flex items-center gap-2 text-red-700">
                          <AlertCircle className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            Output Display Error
                          </span>
                        </div>
                        <p className="text-red-600 text-sm mt-1">
                          Unable to display the output data. There may be an
                          issue with the data format.
                        </p>
                        <details className="mt-2">
                          <summary className="text-xs text-red-500 cursor-pointer">
                            Show raw data
                          </summary>
                          <pre className="text-xs text-red-600 mt-1 p-2 bg-red-100 rounded overflow-auto max-h-32">
                            {JSON.stringify(firstOutput, null, 2)}
                          </pre>
                        </details>
                      </div>
                    );
                  }
                })()}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </motion.div>
  );
};
