import React, { useState, useEffect } from "react";
import {
  Home,
  FolderOpen,
  Users,
  Settings,
  LogOut,
  Zap,
  User,
  ChevronDown,
  FileText,
  Briefcase,
  UserCog2 as UserCog,
  Folder,
  FolderPlus,
  Layers,
  Package,
  Target,
  Workflow,
  Image as ImageIcon,
  AudioLines,
  FileSearch,
  Cpu,
} from "lucide-react";
import { useRouter } from "next/router";
import { cn } from "@/lib/utils";
import { LogoutService } from "@/services/auth.service";
import Link from "next/link";
import Image from "next/image";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { BreadcrumbItem } from "@/redux/slices/breadcrumbSlice";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { getCookie } from "cookies-next/client";
import ChangePasswordDialog from "@/features/change-password/ChangePasswordDialog";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: Home },
  { name: "Projects", href: "/projects", icon: FolderOpen },
  { name: "Assign Jobs", href: "/assign-jobs", icon: UserCog },
  { name: "Playground", href: "/playground", icon: Zap },
  { name: "Manage Users", href: "/manage-users", icon: Users },
  { name: "Settings", href: "/settings", icon: Settings },
];

export const Sidebar: React.FC = () => {
  const router = useRouter();
  const [collapsed] = useState(false);
  const [username, setUsername] = useState<string | undefined>(undefined);
  const [tenant, setTenant] = useState<string | undefined>(undefined);
  const [isMounted, setIsMounted] = useState(false);
  const [changePasswordDialogOpen, setChangePasswordDialogOpen] =
    useState(false);

  const breadcrumbItems = useSelector(
    (state: RootState) => (state.breadcrumb as any)?.currentPath || []
  );

  useEffect(() => {
    setUsername(getCookie("username") as string);
    setTenant(getCookie("tenant_label") as string);
    setIsMounted(true);
  }, []);

  const getIconForItem = (href: string, item?: BreadcrumbItem) => {
    // Use metadata for more accurate icon selection
    const metadata = item?.metadata;
    const itemType = metadata?.type;
    const processName = metadata?.processName;

    // Dashboard
    if (href.includes("/dashboard")) return Home;

    // Projects - use different icons based on context and type
    if (href === "/projects" || itemType === "page") {
      if (href === "/projects") return FolderOpen;
      if (href.includes("/playground")) return Zap;
      if (href.includes("/assign-jobs")) return UserCog;
      if (href.includes("/manage-users")) return Users;
      if (href.includes("/settings")) return Settings;
    }

    // Individual project pages
    if (itemType === "project") {
      return Folder;
    }

    // Jobs - use process-specific icons when available
    if (itemType === "job" || href.includes("/job")) {
      // Use process name from metadata for accurate icon selection
      if (processName) {
        switch (processName) {
          case "audio-transcribe-analysis":
            return AudioLines;
          case "extract-image-data":
            return ImageIcon;
          case "generic-entity-extraction":
            return FileSearch;
          default:
            return Briefcase;
        }
      }

      // Fallback to label-based detection
      const label = item?.label?.toLowerCase() || "";
      if (label.includes("audio") || href.includes("audio")) return AudioLines;
      if (label.includes("image") || href.includes("extract-image"))
        return ImageIcon;
      if (label.includes("entity") || label.includes("extraction"))
        return FileSearch;
      if (label.includes("analysis") || label.includes("transcribe"))
        return Cpu;

      // Default job icon
      return Briefcase;
    }

    // Default fallback
    return FileText;
  };

  const renderBreadcrumbItem = (item: BreadcrumbItem) => {
    const Icon = getIconForItem(item.href, item);
    const indentLevel = item.level;

    return (
      <div
        key={item.id}
        className={cn("", {
          "ml-4": indentLevel === 1,
          "ml-8": indentLevel === 2,
          "ml-12": indentLevel === 3,
        })}
      >
        <Link
          href={item.href}
          className="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out text-gray-600 hover:bg-gray-50 hover:text-gray-800"
        >
          <Icon
            className={cn(
              "h-5 w-5 flex-shrink-0 transition-colors duration-200 text-gray-400 group-hover:text-gray-600",
              collapsed ? "mx-auto" : "mr-3"
            )}
          />
          {!collapsed && (
            <span className="truncate">
              {item.isLoading ? (
                <span className="flex items-center">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                  <span className="ml-2 text-gray-500"></span>
                </span>
              ) : (
                item.label
              )}
            </span>
          )}
        </Link>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <div
        className={cn(
          "h-full bg-white flex flex-col transition-all duration-300",
          collapsed ? "w-16" : "w-64"
        )}
      >
        {/* Header */}
        <div className="p-3 border-b border-gray-100 h-[92px] flex items-center justify-between">
          {!collapsed ? (
            <div className="flex items-center space-x-2">
              <Image src="/logo.png" alt="Logo" width={40} height={40} />
              <div>
                <h1 className="text-2xl font-bold">Aroma</h1>
                <span className="text-sm text-gray-500">
                  Digitization Platform
                </span>
              </div>
            </div>
          ) : (
            <Image src="/logo.png" alt="Logo" width={32} height={32} />
          )}
          {/* <Button
            variant="ghost"
            size="icon"
            className="ml-auto"
            onClick={() => setCollapsed(!collapsed)}
          >
            {collapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
          </Button> */}
        </div>

        {/* Navigation */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="px-2 mt-4 mb-4">
            <h3 className="px-2 text-xs font-semibold text-gray-700 uppercase tracking-wide mb-3">
              Navigation
            </h3>
            <nav className="space-y-2">
              {navigation.map((item) => {
                const isActive =
                  router.pathname === item.href ||
                  (item.href !== "/dashboard" &&
                    router.pathname.startsWith(item.href)) ||
                  (router.pathname.includes("/job") &&
                    item.href === "/projects");

                const navLink = (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out",
                      isActive
                        ? "bg-[#191825] text-white"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    )}
                  >
                    <item.icon
                      className={cn(
                        "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                        isActive
                          ? "text-white"
                          : "text-gray-400 group-hover:text-gray-600",
                        collapsed ? "mx-auto" : "mr-3"
                      )}
                    />
                    {!collapsed && item.name}
                  </Link>
                );

                return collapsed ? (
                  <Tooltip key={item.name}>
                    <TooltipTrigger asChild>{navLink}</TooltipTrigger>
                    <TooltipContent side="right">{item.name}</TooltipContent>
                  </Tooltip>
                ) : (
                  navLink
                );
              })}
            </nav>
          </div>

          {/* Current Path / Breadcrumb Tree */}
          {!collapsed && breadcrumbItems.length > 0 && (
            <>
              <div className="mx-4 border-t border-gray-100"></div>
              <div className="px-2 mt-4 flex-1 overflow-y-auto">
                <h3 className="px-2 text-xs font-semibold text-gray-700 uppercase tracking-wide mb-3">
                  Current Path
                </h3>
                <nav className="space-y-2">
                  {breadcrumbItems.map((item: BreadcrumbItem) =>
                    renderBreadcrumbItem(item)
                  )}
                </nav>
              </div>
            </>
          )}
        </div>

        {/* Footer - Profile Dropdown */}
        <div className="p-4 border-t border-gray-100 bg-white">
          <DropdownMenu>
            {collapsed ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="w-full">
                      <div className="h-8 w-8 bg-black text-white flex items-center justify-center rounded-full text-sm font-medium">
                        {isMounted ? username?.charAt(0).toUpperCase() : "L"}
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent side="right">Profile</TooltipContent>
              </Tooltip>
            ) : (
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full flex items-center justify-between px-2 py-2"
                >
                  <div className="flex items-center space-x-2">
                    <div className="h-8 w-8 bg-[#191825] text-white flex items-center justify-center rounded-full text-sm font-medium">
                      {isMounted ? username?.charAt(0).toUpperCase() : "L"}
                    </div>
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-800">
                        {isMounted ? username || "Loading..." : "Loading..."}
                      </p>
                      <p className="text-xs text-gray-500">
                        {isMounted ? tenant || "Loading..." : "Loading..."}
                      </p>
                    </div>
                  </div>
                  <ChevronDown className="h-4 w-4 ml-auto" />
                </Button>
              </DropdownMenuTrigger>
            )}

            <DropdownMenuPortal>
              <DropdownMenuContent
                align="end"
                side="right"
                sideOffset={10}
                className="w-56"
              >
                <DropdownMenuItem
                  onClick={() => setChangePasswordDialogOpen(true)}
                  className="flex items-center cursor-pointer"
                >
                  <User className="mr-2 h-4 w-4" />
                  <span>Change Password</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={LogoutService}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenuPortal>
          </DropdownMenu>
        </div>
      </div>

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={changePasswordDialogOpen}
        setOpen={setChangePasswordDialogOpen}
      />
    </TooltipProvider>
  );
};
