import { Calendar, Edit, Trash2, CheckCircle } from "lucide-react";
import { format } from "date-fns";
import { useState, useImperativeHandle, forwardRef } from "react";
import { useRouter } from "next/router";
import { motion } from "framer-motion";
import Button from "@/components/custom/Button";
import ProjectDialog from "./ProjectDialog";
import DeleteProjectDialog from "./DeleteProjectDialog";
import { BaseProject } from "@/types/project.types";

interface ProjectDetailsSectionProps {
  project: Project;
  onProjectUpdated?: () => void;
  onStatusCardClick?: (status: string) => void;
  selectedJobs?: Set<string>;
  onAddMediaClick?: () => void;
}

export interface ProjectDetailsSectionRef {
  refreshStatusCounts: () => void;
}

const ProjectDetailsSection = forwardRef<
  ProjectDetailsSectionRef,
  ProjectDetailsSectionProps
>(
  (
    {
      project,
      onProjectUpdated,
      onStatusCardClick,
      selectedJobs,
      onAddMediaClick,
    },
    ref
  ) => {
    const router = useRouter();
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [selectedProject, setSelectedProject] = useState<Project | null>(
      null
    );

    // Get job statistics from project data
    const jobStats = project.job_statistics || {
      by_status: {},
      by_process_type: {},
      total_jobs: 0,
    };

    const totalJobs = jobStats.total_jobs || 0;
    const completedJobs = jobStats.by_status?.completed || 0;
    const completionPercentage =
      totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

    const handleProjectUpdated = () => {
      onProjectUpdated?.();
    };

    const handleProjectDeleted = () => {
      router.push("/projects");
    };

    // Expose refresh function to parent component
    useImperativeHandle(ref, () => ({
      refreshStatusCounts: () => {
        // No longer needed since we use project data directly
        // But keeping for compatibility
      },
    }));

    return (
      <div className="">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header Section - Based on ProjectDetailsDemo */}
          <motion.div
            className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="space-y-4 max-w-4xl">
              <div className="flex flex-wrap items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                  {project.name}
                </h1>
              </div>
              {project.description && (
                <p className="text-lg text-gray-600 leading-relaxed">
                  {project.description}
                </p>
              )}
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <span className="font-medium">
                    Created{" "}
                    {format(new Date(project.created_at), "MMM dd, yyyy")}
                  </span>
                </div>
                <div className="flex items-center gap-2 bg-green-50 px-3 py-2 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-700">
                    {Math.round(completionPercentage)}% ({completedJobs}/
                    {totalJobs} jobs completed)
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 shrink-0">
              {/* Delete button - icon only, on the left */}
              <Button
                variant="outline"
                // size="icon"
                onClick={() => {
                  setSelectedProject(project);
                  setDeleteDialogOpen(true);
                }}
                className="border-2 border-red-200 text-red-600 hover:border-red-300 hover:bg-red-100 transition-all duration-200 p-3 rounded-md"
                title="Delete Project"
                // startIcon={<Trash2 className="h-4 w-4" />}
              >
                <Trash2 className="h-4 w-4" />
              </Button>

              {/* Edit button */}
              <Button
                variant="outline"
                startIcon={<Edit className="h-4 w-4" />}
                onClick={() => {
                  setSelectedProject(project);
                  setEditDialogOpen(true);
                }}
                className="border-2 border-sky-600 text-sky-600 hover:bg-sky-100 hover:text-sky-600 transition-all duration-200 px-6 py-3 rounded-md font-semibold"
              >
                Edit Project
              </Button>
            </div>
          </motion.div>
        </div>

        {/* Dialogs */}
        <ProjectDialog
          open={editDialogOpen}
          setOpen={setEditDialogOpen}
          selectedProject={selectedProject as BaseProject}
          setSelectedProject={(project) =>
            setSelectedProject(project as Project)
          }
          onProjectCreated={handleProjectUpdated}
        />

        <DeleteProjectDialog
          open={deleteDialogOpen}
          setOpen={setDeleteDialogOpen}
          selectedProject={selectedProject}
          setSelectedProject={setSelectedProject}
          onProjectDeleted={handleProjectDeleted}
        />
      </div>
    );
  }
);

ProjectDetailsSection.displayName = "ProjectDetailsSection";

export default ProjectDetailsSection;
