import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  XCircle,
  User,
  Calendar,
  ChevronLeft,
  ChevronRight,
  TrendingUp,
} from "lucide-react";
import { GetRecentActivityService } from "@/services/metrics.service";
import { useAppSelector } from "@/redux";
import { formatDistanceToNow } from "date-fns";

export const RecentActivitySection: React.FC = () => {
  const [activities, setActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("");
  const itemsPerPage = 5;
  const processes = useAppSelector((state) => state.processes);

  const fetchActivities = async (
    page: number,
    status?: string,
    preventLoading = false
  ) => {
    if (!preventLoading) {
      setLoading(true);
    }
    try {
      const response = await GetRecentActivityService(
        page,
        itemsPerPage,
        status || undefined
      );
      if (response.success) {
        setActivities(response.data.data || []);
        setTotalPages(response.data.meta?.total_pages || 1);
      }
    } catch (error) {
      console.error("Failed to fetch recent activity:", error);
    } finally {
      if (!preventLoading) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchActivities(currentPage, statusFilter);
  }, [currentPage, statusFilter]);

  const getActivityIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-slate-600" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-slate-600" />;
      case "in-progress":
        return <PlayCircle className="w-4 h-4 text-slate-600" />;
      case "pending":
        return <Clock className="w-4 h-4 text-slate-600" />;
      default:
        return <Activity className="w-4 h-4 text-slate-600" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    return "bg-slate-50 text-slate-700 border-slate-200";
  };

  const getProcessSlug = (processName: string) => {
    // Find the process key that matches the process name
    const processKey = Object.keys(processes).find(
      (key) => (processes as any)[key].name === processName
    );
    return processKey
      ? (processes as any)[processKey].label
      : processName || "N/A";
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return "Unknown time";
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      // Fetch without loading state to prevent scroll jump
      fetchActivities(newPage, statusFilter, true);
    }
  };

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "completed", label: "Completed" },
    { value: "failed", label: "Failed" },
    { value: "in-progress", label: "In Progress" },
    { value: "pending", label: "Pending" },
  ];

  return (
    <Card className="bg-white shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200 rounded-md">
      <CardHeader className="pb-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold text-slate-800">
              Recent Activity Highlights
            </CardTitle>
          </div>
          <select
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value);
              setCurrentPage(1);
            }}
            className="text-xs border border-slate-200 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-slate-500 bg-white"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <p className="text-sm text-slate-600 mt-2">
          Latest job activities and status updates
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 min-h-[500px]">
          {loading ? (
            <>
              {[...Array(itemsPerPage)].map((_, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 p-3 bg-slate-50/50 rounded-md animate-pulse h-[92px]"
                >
                  <div className="w-8 h-8 bg-slate-200 rounded-md mt-1 flex-shrink-0"></div>
                  <div className="flex-1 flex flex-col justify-between h-full py-1">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between gap-3">
                        <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                        <div className="h-5 w-16 bg-slate-200 rounded-md flex-shrink-0"></div>
                      </div>
                      <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                    </div>
                    <div className="h-3 bg-slate-200 rounded w-2/3 flex-shrink-0"></div>
                  </div>
                </div>
              ))}

              {/* Pagination Skeleton */}
              <div className="flex items-center justify-between pt-4 border-t border-slate-100 mt-4">
                <div className="h-4 w-20 bg-slate-200 rounded animate-pulse"></div>
                <div className="flex items-center gap-1">
                  <div className="h-8 w-8 bg-slate-200 rounded-md animate-pulse"></div>
                  <div className="h-8 w-8 bg-slate-200 rounded-md animate-pulse"></div>
                </div>
              </div>
            </>
          ) : activities.length > 0 ? (
            <>
              {activities.map((activity, index) => (
                <div
                  key={activity.id || index}
                  className="group flex items-start gap-4 p-3 bg-slate-50/50 rounded-md hover:bg-white transition-all duration-300 hover:shadow-sm cursor-pointer border border-slate-100 hover:border-slate-200 h-[92px]"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="mt-1 p-2 rounded-md bg-white shadow-sm group-hover:shadow-md transition-shadow duration-200 border border-gray-100 flex-shrink-0">
                    {getActivityIcon(activity.status)}
                  </div>
                  <div className="flex-1 min-w-0 flex flex-col justify-between h-full py-1 overflow-hidden">
                    <div className="flex-1 min-h-0">
                      <div className="flex items-start justify-between gap-3 mb-1">
                        <p className="text-sm font-medium text-gray-800 group-hover:text-gray-900 transition-colors duration-200 truncate">
                          {activity.job_name}
                        </p>
                        <span
                          className={`px-2 py-0.5 text-xs font-medium rounded-md border ${getStatusBadgeColor(
                            activity.status
                          )} flex-shrink-0`}
                        >
                          {activity.status}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mb-1 truncate">
                        {activity.event} •{" "}
                        {getProcessSlug(activity.process_name)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500 flex-shrink-0">
                      <User className="w-3 h-3" />
                      <span className="truncate">
                        {activity.created_by_name || "System"}
                      </span>
                      <span>•</span>
                      <Calendar className="w-3 h-3" />
                      <span>{formatTimeAgo(activity.created_at)}</span>
                    </div>
                  </div>
                </div>
              ))}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-4">
                  <div className="text-xs text-gray-500">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="h-8 w-8 p-0 border-gray-200 rounded-md"
                    >
                      <ChevronLeft className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="h-8 w-8 p-0 border-gray-200 rounded-md"
                    >
                      <ChevronRight className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center text-gray-500 flex items-center justify-center min-h-[500px]">
              <div>
                <div className="w-16 h-16 mx-auto mb-4 rounded-md bg-gray-100 flex items-center justify-center">
                  <Activity className="w-8 h-8 text-gray-400" />
                </div>
                <p className="font-medium">No recent activity found</p>
                {statusFilter && (
                  <p className="text-sm mt-1">
                    Try changing the status filter or check back later
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
