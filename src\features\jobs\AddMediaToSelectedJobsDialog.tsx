import { Fragment, useState, useEffect } from "react";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Description,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { X } from "lucide-react";
import Button from "@/components/custom/Button";
import { GetJobDetailsService } from "@/services/jobs.service";
import AddMediaDialog from "./AddMediaDialog";

type AddMediaToSelectedJobsDialogProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  selectedJobIds: string[];
  onMediaUploadSuccess?: () => void;
};

type JobInfo = {
  _id: string;
  name: string;
  process_name: ProcessName;
};

export default function AddMediaToSelectedJobsDialog({
  open,
  setOpen,
  selectedJobIds,
  onMediaUploadSuccess,
}: AddMediaToSelectedJobsDialogProps) {
  const [jobs, setJobs] = useState<JobInfo[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>("");
  const [addMediaDialogOpen, setAddMediaDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Fetch job details when dialog opens
  useEffect(() => {
    if (open && selectedJobIds.length > 0) {
      fetchJobDetails();
    }
  }, [open, selectedJobIds]);

  const fetchJobDetails = async () => {
    setLoading(true);
    try {
      const jobPromises = selectedJobIds.map((jobId) =>
        GetJobDetailsService(jobId)
      );
      const responses = await Promise.all(jobPromises);

      const jobsData = responses
        .filter((response) => response.success)
        .map((response) => ({
          _id: response.data._id,
          name: response.data.name,
          process_name: response.data.process_name,
        }));

      setJobs(jobsData);
      if (jobsData.length > 0) {
        setSelectedJobId(jobsData[0]._id);
      }
    } catch (error) {
      console.error("Error fetching job details:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectJob = (jobId: string) => {
    setSelectedJobId(jobId);
    setAddMediaDialogOpen(true);
  };

  const handleMediaUploadSuccess = () => {
    setAddMediaDialogOpen(false);
    setOpen(false);
    onMediaUploadSuccess?.();
  };

  const selectedJob = jobs.find((job) => job._id === selectedJobId);

  return (
    <>
      <Transition appear show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={setOpen}>
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-200"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-150"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/70" />
          </TransitionChild>

          <div className="fixed inset-0 overflow-y-auto p-4">
            <div className="flex min-h-full items-center justify-center">
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-200"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-150"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel className="w-full max-w-md transform rounded-xl bg-white dark:bg-zinc-900 p-6 shadow-xl transition-all">
                  <div className="flex items-center justify-between mb-4">
                    <DialogTitle className="text-xl font-semibold">
                      Add Media to Job
                    </DialogTitle>
                    <button
                      onClick={() => setOpen(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>

                  <Description className="text-sm text-muted-foreground mb-6">
                    Select a job to add media files to:
                  </Description>

                  {loading ? (
                    <div className="space-y-3">
                      {Array.from({ length: selectedJobIds.length }).map(
                        (_, index) => (
                          <div
                            key={index}
                            className="h-12 bg-gray-100 rounded-md animate-pulse"
                          />
                        )
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {jobs.map((job) => (
                        <div
                          key={job._id}
                          className="flex items-center justify-between p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                          onClick={() => handleSelectJob(job._id)}
                        >
                          <div>
                            <div className="font-medium text-sm">
                              {job.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {job.process_name}
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            Add Media
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {jobs.length === 0 && !loading && (
                    <div className="text-center py-8 text-gray-500">
                      No jobs available for media upload.
                    </div>
                  )}

                  <div className="flex justify-end mt-6">
                    <Button variant="outline" onClick={() => setOpen(false)}>
                      Cancel
                    </Button>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Add Media Dialog */}
      {selectedJob && (
        <AddMediaDialog
          open={addMediaDialogOpen}
          setOpen={setAddMediaDialogOpen}
          jobId={selectedJob._id}
          processName={selectedJob.process_name}
          onMediaUploadSuccess={handleMediaUploadSuccess}
        />
      )}
    </>
  );
}
