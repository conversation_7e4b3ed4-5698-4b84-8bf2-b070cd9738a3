import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose, // Import if you want a manual close button
} from "@/components/ui/dialog"; // Assuming from shadcn/ui
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Assuming from shadcn/ui
import { Save } from "lucide-react";

interface ProcessSaveDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  processInput: string | null; // More specific type if input is structured
  processOutput: any | null; // More specific type for output
}

export const ProcessSaveDialog: React.FC<ProcessSaveDialogProps> = ({
  open,
  onOpenChange,
  processInput,
  processOutput,
}) => {
  const [processName, setProcessName] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (open) {
      // Reset form when dialog opens or prefill if needed
      setProcessName("");
      setDescription("");
      setCategory("");
      // Example prefill based on output
      if (processOutput?.type) {
        if (processOutput.type.includes("document"))
          setCategory("document-processing");
        else if (processOutput.type.includes("image"))
          setCategory("image-analysis");
      }
    }
  }, [open, processOutput]);

  const handleSave = async () => {
    if (!processName.trim()) {
      // Add validation feedback, e.g., using a toast or inline error message
      alert("Process Name is required."); // Replace with better UX
      return;
    }
    setIsSaving(true);
    // Simulate save operation
    await new Promise((resolve) => setTimeout(resolve, 1500));
    console.log("Saving process:", {
      processName,
      description,
      category,
      processInput,
      processOutput,
    });
    setIsSaving(false);
    onOpenChange(false); // Close dialog on successful save
    // Optionally, show a success notification
  };

  const inputSample =
    typeof processInput === "string"
      ? processInput
      : processInput // Handle structured input if necessary, e.g. JSON.stringify(processInput, null, 2)
      ? JSON.stringify(processInput, null, 2)
      : "No text input provided.";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg bg-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-[#111827]">
            Save Process Workflow
          </DialogTitle>
          <DialogDescription className="text-[#6B7280]">
            Save this input and output configuration as a reusable process
            workflow.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 md:gap-6 py-4">
          <div className="space-y-2">
            <Label
              htmlFor="processName"
              className="text-sm font-medium text-[#374151]"
            >
              Process Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="processName"
              value={processName}
              onChange={(e) => setProcessName(e.target.value)}
              placeholder="e.g., Document Analysis Workflow"
              className="mt-1 border-[#D1D5DB] focus:border-sky-600 focus:ring-sky-600"
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="description"
              className="text-sm font-medium text-[#374151]"
            >
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what this process does and when to use it..."
              className="mt-1 border-[#D1D5DB] focus:border-sky-600 focus:ring-sky-600"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="category"
              className="text-sm font-medium text-[#374151]"
            >
              Category
            </Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="mt-1 border-[#D1D5DB] focus:border-sky-600 focus:ring-sky-600">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="document-processing">
                  Document Processing
                </SelectItem>
                <SelectItem value="image-analysis">Image Analysis</SelectItem>
                <SelectItem value="audio-transcription">
                  Audio Transcription
                </SelectItem>
                <SelectItem value="data-extraction">Data Extraction</SelectItem>
                <SelectItem value="content-generation">
                  Content Generation
                </SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Preview Section */}
          {(processInput || processOutput) && (
            <div className="border border-[#E0E0E0] rounded-lg p-3 md:p-4 bg-[#F7F7F7] space-y-3">
              <h4 className="text-sm font-medium text-[#202020]">
                Process Preview
              </h4>
              {processInput && (
                <div>
                  <span className="text-xs text-[#666666] font-medium">
                    Input Sample:
                  </span>
                  <div className="mt-1 p-2 bg-white rounded border text-xs max-h-20 overflow-y-auto simple-scrollbar">
                    <pre className="whitespace-pre-wrap">
                      {inputSample.slice(0, 250)}
                      {inputSample.length > 250 ? "..." : ""}
                    </pre>
                  </div>
                </div>
              )}
              {processOutput && (
                <>
                  <div>
                    <span className="text-xs text-[#666666] font-medium">
                      Expected Output Type:
                    </span>
                    <span className="ml-2 px-2 py-0.5 bg-sky-600/10 text-sky-600 text-xs rounded-full font-medium">
                      {processOutput?.type || "Multimodal Analysis"}
                    </span>
                  </div>
                  {typeof processOutput?.confidence === "number" && (
                    <div>
                      <span className="text-xs text-[#666666] font-medium">
                        Confidence Level:
                      </span>
                      <span className="ml-2 text-[#202020] font-semibold text-xs">
                        {Math.round(processOutput.confidence * 100)}%
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="gap-2 sm:justify-end">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-[#D1D5DB] text-[#374151] hover:bg-gray-100"
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !processName.trim()}
            className="bg-sky-600 hover:bg-sky-700 text-white disabled:opacity-50"
          >
            {isSaving ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Saving...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="w-4 h-4" />
                Save Process
              </div>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
