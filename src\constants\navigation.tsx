import { Navigation } from "@toolpad/core/AppProvider";
import DashboardIcon from "@mui/icons-material/Dashboard";
import ProjectsIcon from "@mui/icons-material/ListAltRounded";
import SubordiantesIcon from "@mui/icons-material/SupervisorAccountRounded";
import Image from "next/image";

export const NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Main items",
  },
  {
    title: "Dashboard",
    icon: <DashboardIcon />,
    segment: "dashboard",
    pattern: "dashboard",
  },
  {
    segment: "projects",
    title: "Projects",
    icon: <ProjectsIcon />,
    pattern: "projects",
  },
  {
    segment: "manage-users",
    title: "Manage Users",
    icon: <SubordiantesIcon />,
    pattern: "manage-users",
  }
];

export const BRANDING = {
  title: "Aroma",
  logo: <Image src="/logo.png" alt="Logo" width={40} height={40} />,
};