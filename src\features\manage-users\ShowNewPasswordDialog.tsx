import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import Button from "@/components/custom/Button";
import { Co<PERSON>, Check, X, <PERSON>, Eye, EyeOff } from "lucide-react";

export default function ShowNewPasswordDialog({
  open,
  setOpen,
  newPasswordFields,
}: ShowNewPasswordDialogProps) {
  const [copyButtonChecked, setCopyButtonChecked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleCloseDialog = (event: React.SyntheticEvent) => {
    setOpen(false);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    setCopyButtonChecked(true);
    navigator.clipboard
      .writeText(newPasswordFields.newPassword)
      .then(() => {
        console.log("New password copied to clipboard");
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
    setTimeout(() => {
      setCopyButtonChecked(false);
    }, 2000);
  };

  if (!newPasswordFields.newPassword || !newPasswordFields.username) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {/* <div className="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
              <Key className="h-5 w-5 text-green-600" />
            </div> */}
            <div>
              <h3 className="text-lg font-semibold">Password Generated</h3>
              <p className="text-sm text-gray-500 font-normal">
                New password for {newPasswordFields.username}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="py-6 space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-sm text-green-800 mb-3">
              <strong>Password successfully generated!</strong> Please share
              this password securely with the user.
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              New Password
            </label>
            <div className="relative">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 pr-12">
                <p className="font-mono text-lg font-semibold text-gray-900 break-all">
                  {showPassword
                    ? newPasswordFields.newPassword
                    : "••••••••••••••••"}
                </p>
              </div>
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <p className="text-sm text-amber-800">
              <strong>Important:</strong> Make sure to share this password
              securely with the user. They should change it after their first
              login.
            </p>
          </div>
        </div>
        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
          >
            Close
          </Button>
          <Button
            onClick={handleSubmit}
            loading={copyButtonChecked}
            startIcon={
              copyButtonChecked ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )
            }
          >
            {copyButtonChecked ? "Copied!" : "Copy Password"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type ShowNewPasswordDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  newPasswordFields: {
    username: string;
    newPassword: string;
  };
};
