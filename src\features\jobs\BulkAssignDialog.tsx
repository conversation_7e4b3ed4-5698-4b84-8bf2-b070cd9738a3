import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Button from "@/components/custom/Button";
import { Label } from "@/components/ui/label";
import { Users, Loader2 } from "lucide-react";
import { AssignJobsToAgentService } from "@/services/jobs.service";
import { snackbar } from "@/utils/snackbar.util";

interface BulkAssignDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedJobIds: string[];
  agents: User[];
  onAssignmentComplete: () => void;
  projectId: string;
  onJobRemoved?: (jobId: string) => void;
}

export default function BulkAssignDialog({
  open,
  setOpen,
  selectedJobIds,
  agents,
  onAssignmentComplete,
  projectId,
  onJobRemoved,
}: BulkAssignD<PERSON>ogProps) {
  const [selectedAgentId, setSelectedAgentId] = useState("");
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    if (!loading) {
      setOpen(false);
      setSelectedAgentId("");
    }
  };

  const handleAssign = async () => {
    if (!selectedAgentId) {
      snackbar.showErrorMessage("Please select an agent");
      return;
    }

    if (selectedJobIds.length === 0) {
      snackbar.showErrorMessage("No jobs selected for assignment");
      return;
    }

    setLoading(true);

    try {
      const response = await AssignJobsToAgentService(
        selectedJobIds,
        selectedAgentId
      );

      const selectedAgent = agents.find(
        (agent) => agent._id === selectedAgentId
      );

      if (response.success) {
        snackbar.showSuccessMessage(
          `Successfully assigned ${selectedJobIds.length} job(s) to ${
            selectedAgent?.username || "agent"
          }`
        );
        onAssignmentComplete();
        handleClose();
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to assign jobs");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-sky-600" />
            Assign Jobs
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Job Count Display */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {selectedJobIds.length}
            </div>
            <div className="text-sm text-gray-600">
              {selectedJobIds.length === 1 ? "job" : "jobs"} to assign
            </div>
          </div>

          {/* Agent Selection */}
          <div className="space-y-2">
            <Label htmlFor="agent-select">Assign to</Label>
            <Select
              value={selectedAgentId}
              onValueChange={setSelectedAgentId}
              disabled={loading}
            >
              <SelectTrigger id="agent-select" className="w-full">
                <SelectValue placeholder="Select an agent..." />
              </SelectTrigger>
              <SelectContent>
                {agents.map((agent) => (
                  <SelectItem key={agent._id} value={agent._id}>
                    <div className="flex items-center gap-2">
                      <span>{agent.username}</span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {agent.role}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={loading || !selectedAgentId}
            className="btn-primary"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Assigning...
              </>
            ) : (
              "Assign"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
