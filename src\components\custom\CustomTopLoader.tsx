import { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import NProgress from "nprogress";

// Don't import "nprogress.css". We'll inject all styles below.

export default function CustomTopLoader() {
  const router = useRouter();
  const prevPathRef = useRef(router.pathname);

  // Inject custom styles compatible with Tailwind
  useEffect(() => {
    const existing = document.getElementById("nprogress-style");
    if (!existing) {
      const style = document.createElement("style");
      style.id = "nprogress-style";
      style.innerHTML = `
        #nprogress {
          pointer-events: none;
        }

        #nprogress .bar {
          background: #0284c7;
          position: fixed;
          z-index: 9999;
          top: 0;
          left: 0;
          width: 100%;
          height: 4px;
        }

        #nprogress .peg {
          display: block;
          position: absolute;
          right: 0px;
          width: 100px;
          height: 100%;
          box-shadow: 0 0 10px #0284c7, 0 0 5px #0284c7;
          opacity: 1.0;
          transform: rotate(3deg) translate(0px, -4px);
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Configure and control NProgress
  useEffect(() => {
    NProgress.configure({ showSpinner: false, speed: 400, minimum: 0.15 });

    const handleStart = (url: string) => {
      const newPathname = new URL(url, window.location.origin).pathname;
      if (newPathname !== prevPathRef.current) {
        NProgress.start();
      }
    };

    const handleComplete = (url: string) => {
      const newPathname = new URL(url, window.location.origin).pathname;
      prevPathRef.current = newPathname;
      NProgress.done();
    };

    const handleError = () => {
      NProgress.done();
    };

    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleComplete);
    router.events.on("routeChangeError", handleError);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleComplete);
      router.events.off("routeChangeError", handleError);
    };
  }, [router]);

  return null;
}
