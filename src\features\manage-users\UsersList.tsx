import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { motion } from "framer-motion";
import { useAppSelector } from "@/redux";
import { GetUsersService } from "@/services/users.service";
import TextField from "@/components/custom/TextField";
import Select from "@/components/custom/Select";
import Button from "@/components/custom/Button";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  User,
  UserPlus,
  Loader2,
  Edit,
  Key,
  Users as UsersIcon,
  Search,
} from "lucide-react";
import ChangeRoleDialog from "./ChangeRoleDialog";
import ResetPasswordDialog from "./ResetPasswordDialog";
import ShowNewPasswordDialog from "./ShowNewPasswordDialog";
import { Pagination } from "@/components/custom/Pagination";
import { FloatingHeader } from "@/components/users/FloatingHeader";

export default function UsersList({
  setInviteUserDialogOpen,
}: {
  setInviteUserDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const router = useRouter();
  const roles = useAppSelector((state) => state.roles);

  // Extract query params
  const getQuery = (key: string, fallback = "") =>
    typeof router.query[key] === "string"
      ? (router.query[key] as string)
      : fallback;

  const getQueryNum = (key: string, fallback = 1) =>
    typeof router.query[key] === "string"
      ? parseInt(router.query[key] as string)
      : fallback;

  // Initialize state from URL
  const [inputValue, setInputValue] = useState(getQuery("search"));
  const [searchKeyword, setSearchKeyword] = useState(getQuery("search"));
  const [filterRole, setFilterRole] = useState(getQuery("role"));
  const [initialLoading, setInitialLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);
  const [meta, setMeta] = useState<Meta>({
    page: getQueryNum("page", 1),
    limit: getQueryNum("limit", 10),
    total: 0,
    total_pages: 0,
  });

  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [changeRoleDialogOpen, setChangeRoleDialogOpen] = useState(false);
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);
  const [showNewPasswordDialogOpen, setShowNewPasswordDialogOpen] =
    useState(false);
  const [newPasswordFields, setNewPasswordFields] = useState<NewPasswordFields>(
    {
      username: "",
      newPassword: "",
    }
  );

  const updateUrl = (params: Record<string, any>) => {
    const query = {
      ...router.query,
      ...params,
    };

    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null) delete query[key];
    });

    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  // Scroll detection for floating header
  useEffect(() => {
    const handleScroll = () => {
      const mainElement = document.querySelector("main") as HTMLElement;
      if (mainElement) {
        const scrollTop = mainElement.scrollTop;
        const shouldShow = scrollTop > 300; // Show after scrolling 300px
        setShowFloatingHeader(shouldShow);
      }
    };

    const mainElement = document.querySelector("main") as HTMLElement;
    if (mainElement) {
      mainElement.addEventListener("scroll", handleScroll);
      return () => mainElement.removeEventListener("scroll", handleScroll);
    }
  }, []);

  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchKeyword(inputValue);
      setMeta((prev) => ({ ...prev, page: 1 }));
      updateUrl({ search: inputValue, page: 1 });
    }, 0);

    return () => clearTimeout(handler);
  }, [inputValue]);

  const fetchUsers = async (
    page: number,
    limit: number,
    search: string,
    role: string
  ) => {
    if (initialLoading) {
      setInitialLoading(true);
    } else {
      setLoading(true);
    }

    const res = await GetUsersService(page, limit, search, role);

    if (res.success) {
      setUsers(res.data.data);
      setMeta(res.data.meta);
    } else {
      console.error("Error fetching users:", res.data);
    }

    setInitialLoading(false);
    setLoading(false);
  };

  useEffect(() => {
    fetchUsers(meta.page, meta.limit, searchKeyword, filterRole);
  }, [meta.page, meta.limit, searchKeyword, filterRole]);

  const handleChangePage = (newPage: number) => {
    const actualPage = newPage + 1;
    setMeta((prev) => ({ ...prev, page: actualPage }));
    updateUrl({ page: actualPage });
  };

  const handleChangeRowsPerPage = (newLimit: number) => {
    setMeta({ ...meta, page: 1, limit: newLimit });
    updateUrl({ limit: newLimit, page: 1 });
  };

  // Helper functions for floating header
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleRoleChange = (role: string) => {
    setFilterRole(role);
    setMeta((prev) => ({ ...prev, page: 1 }));
    updateUrl({ role: role, page: 1 });
  };

  const handleClearFilters = () => {
    setInputValue("");
    setFilterRole("");
    setMeta((prev) => ({ ...prev, page: 1 }));
    updateUrl({ search: null, role: null, page: 1 });
  };

  const hasActiveFilters = Boolean(searchKeyword || filterRole);

  return (
    <>
      {/* Floating Header */}
      <FloatingHeader
        isVisible={showFloatingHeader && !initialLoading}
        searchQuery={inputValue}
        selectedRole={filterRole}
        roles={roles}
        onSearchChange={handleSearch}
        onRoleChange={handleRoleChange}
        onClearFilters={handleClearFilters}
        onInviteUser={() => setInviteUserDialogOpen(true)}
        hasActiveFilters={hasActiveFilters}
      />

      <ChangeRoleDialog
        open={changeRoleDialogOpen}
        setOpen={setChangeRoleDialogOpen}
        selectedUser={selectedUser}
        setSelectedUser={setSelectedUser}
      />
      <ResetPasswordDialog
        open={resetPasswordDialogOpen}
        setOpen={setResetPasswordDialogOpen}
        selectedUser={selectedUser}
        setSelectedUser={setSelectedUser}
        setShowNewPasswordDialogOpen={setShowNewPasswordDialogOpen}
        setNewPasswordFields={setNewPasswordFields}
      />
      <ShowNewPasswordDialog
        open={showNewPasswordDialogOpen}
        setOpen={setShowNewPasswordDialogOpen}
        newPasswordFields={newPasswordFields}
      />

      <motion.div
        className="min-h-screen relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Section */}
          <motion.div
            className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="space-y-2">
              <h1 className="text-4xl font-bold text-gray-900">Manage Users</h1>
              <p className="text-gray-600 text-lg">
                Manage user accounts, roles, and permissions for your
                organization.
              </p>
            </div>
            <Button
              onClick={() => setInviteUserDialogOpen(true)}
              startIcon={<UserPlus className="h-4 w-4" />}
            >
              Invite User
            </Button>
          </motion.div>

          {/* Filters Section - Full width with longer search */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Search Input - Full width, longer */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <TextField
                placeholder="Search by username..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="pl-10 w-full bg-white border-gray-100 focus:border-gray-200 h-10"
              />
            </div>

            {/* Role Filter */}
            <div className="w-full sm:w-48">
              <Select
                value={filterRole}
                onValueChange={(value) => {
                  setFilterRole(value);
                  setMeta((prev) => ({ ...prev, page: 1 }));
                  updateUrl({ role: value, page: 1 });
                }}
                placeholder="All roles"
                options={roles.map((role) => ({
                  value: role.name,
                  label: role.name,
                }))}
                clearable={true}
                clearLabel="All roles"
                className="bg-white border-gray-100 focus:border-gray-200 h-10"
              />
            </div>

            {/* Clear Filters Button */}
            {hasActiveFilters && (
              <Button
                onClick={handleClearFilters}
                variant="ghost"
                className="text-gray-600 hover:text-gray-800 px-3 py-2 h-10 whitespace-nowrap"
              >
                Clear filters
              </Button>
            )}
          </motion.div>

          {/* Users Cards */}
          <motion.div
            className="bg-white rounded-md shadow-sm overflow-hidden"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-sky-50 to-sky-100 rounded-md flex items-center justify-center">
                    <UsersIcon className="h-5 w-5 text-sky-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Users
                    </h3>
                    <p className="text-sm text-gray-500">
                      {meta.total} user{meta.total !== 1 ? "s" : ""} found
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              {initialLoading ? (
                <div className="p-6">
                  <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between p-4 bg-white rounded-md border border-gray-100"
                      >
                        <div className="flex items-center gap-4">
                          <Skeleton className="h-12 w-12 rounded-full" />
                          <Skeleton className="h-5 w-32" />
                        </div>
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <Skeleton className="h-3 w-8 mb-1" />
                            <Skeleton className="h-6 w-16" />
                          </div>
                          <div className="text-center">
                            <Skeleton className="h-3 w-20 mb-1" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-8 w-24 rounded-md border" />
                            <Skeleton className="h-8 w-28 rounded-md border" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : users.length > 0 ? (
                <div className="p-6 space-y-4">
                  {users.map((user, index) => (
                    <motion.div
                      key={user._id}
                      className="flex items-center justify-between p-4 bg-white rounded-md border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all duration-200"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.4,
                        delay: index * 0.1,
                        ease: "easeOut",
                      }}
                      whileHover={{
                        scale: 1.01,
                        transition: { duration: 0.2 },
                      }}
                    >
                      {/* Left Side: Avatar and Username */}
                      <div className="flex items-center gap-4">
                        <Avatar className="h-12 w-12">
                          <AvatarFallback className="bg-gradient-to-br from-sky-100 to-sky-200 text-sky-700 font-medium">
                            {user.username.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-semibold text-gray-900 text-lg">
                            {user.username
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (char) => char.toUpperCase())}
                          </div>
                        </div>
                      </div>

                      {/* Right Side: Role, Member Since, and Actions */}
                      <div className="flex items-center gap-12">
                        {/* Role Section */}
                        <div className="text-center">
                          <div className="text-xs text-gray-500 mb-1">Role</div>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sky-100 text-sky-800">
                            {user.role}
                          </span>
                        </div>

                        {/* Member Since Section */}
                        <div className="text-center">
                          <div className="text-xs text-gray-500 mb-1">
                            Member Since
                          </div>
                          <div className="text-sm text-gray-700 font-medium">
                            {new Date(user.created_at).toLocaleDateString()}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user);
                              setChangeRoleDialogOpen(true);
                            }}
                            className="text-gray-600 hover:text-gray-800 hover:bg-gray-100 border border-gray-150 px-3 py-2"
                            startIcon={<Edit className="h-4 w-4" />}
                          >
                            Change Role
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user);
                              setResetPasswordDialogOpen(true);
                            }}
                            className="text-gray-600 hover:text-gray-800 hover:bg-gray-100 border border-gray-150 px-3 py-2"
                            startIcon={<Key className="h-4 w-4" />}
                          >
                            Reset Password
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <motion.div
                  className="p-12 text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="w-16 h-16 bg-gray-50 rounded-md flex items-center justify-center mx-auto mb-4">
                    <UsersIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No users found
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {inputValue || filterRole
                      ? "Try adjusting your search or filter criteria."
                      : "Get started by inviting your first user."}
                  </p>
                  {!inputValue && !filterRole && (
                    <Button
                      onClick={() => setInviteUserDialogOpen(true)}
                      startIcon={<UserPlus className="h-4 w-4" />}
                    >
                      Invite User
                    </Button>
                  )}
                </motion.div>
              )}

              {/* Loading Overlay */}
              {loading && !initialLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                  <div className="flex items-center gap-3">
                    <Loader2 className="h-6 w-6 animate-spin text-sky-600" />
                    <span className="text-sm text-gray-600">
                      Loading users...
                    </span>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Pagination */}
          {meta.total > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Pagination
                count={meta.total}
                rowsPerPage={meta.limit}
                page={meta.page - 1}
                onPageChange={(newPage) => handleChangePage(newPage)}
                onRowsPerPageChange={(newLimit) =>
                  handleChangeRowsPerPage(newLimit)
                }
              />
            </motion.div>
          )}
        </div>
      </motion.div>
    </>
  );
}

type User = {
  _id: string;
  username: string;
  role: string;
  created_at: string;
};

type Meta = {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
};

export type NewPasswordFields = {
  username: string;
  newPassword: string;
};
