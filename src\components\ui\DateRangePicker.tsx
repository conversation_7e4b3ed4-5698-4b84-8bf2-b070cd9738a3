import * as React from "react";
// import { CalendarIcon } from "@radix-ui/react"
import { format, startOfMonth, startOfYear, subDays } from "date-fns";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Calendar } from "./calendar";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";
import { CalendarIcon } from "lucide-react";

interface DateRangePreset {
  label: string;
  range: {
    from: Date;
    to?: Date;
  };
}

interface DateRangePickerProps extends React.HTMLAttributes<HTMLDivElement> {
  showExternalPresets?: boolean;
  showInternalPresets?: boolean;
  numberOfMonths?: 2 | 3 | 4 | 5 | 6;
  presets?: DateRangePreset[];
}

export function DateRangePicker({
  showExternalPresets,
  showInternalPresets,
  numberOfMonths = 2,
  presets,
  className,
}: DateRangePickerProps) {
  const defaultPresets: DateRangePreset[] = [
    {
      label: "Today",
      range: {
        from: new Date(),
        to: new Date(),
      },
    },
    {
      label: "Yesterday",
      range: {
        from: subDays(new Date(), 1),
        to: subDays(new Date(), 1),
      },
    },
    {
      label: "Last 7 Days",
      range: {
        from: subDays(new Date(), 6),
        to: new Date(),
      },
    },
    {
      label: "Last 30 Days",
      range: {
        from: subDays(new Date(), 29),
        to: new Date(),
      },
    },
    {
      label: "Month to Date",
      range: {
        from: startOfMonth(new Date()),
        to: new Date(),
      },
    },
    {
      label: "Year to Date",
      range: {
        from: startOfYear(new Date()),
        to: new Date(),
      },
    },
    {
      label: "This Year",
      range: {
        from: new Date(new Date().getFullYear(), 0, 1),
        to: new Date(),
      },
    },
  ];

  const allPresets = presets ?? defaultPresets;

  const [date, setDate] = React.useState<DateRange | undefined>();

  const handlePresetSelect = (preset: DateRangePreset) => {
    setDate({
      from: preset.range.from,
      to: preset.range.to,
    });
  };

  return (
    <div className={cn("inline-flex", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-[260px] justify-start text-left font-normal",
              !date && "text-muted-foreground",
              showExternalPresets && "rounded-r-none"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          {showInternalPresets ? (
            <>
              <div className="flex">
                <div className="justify-evenly p-2">
                  <div className="text-muted-foreground">
                    <span className="font-bold underline">Presets</span>
                  </div>
                  {allPresets.map((preset) => (
                    <div
                      key={preset.label}
                      role="button"
                      className="text-sm text-muted-foreground hover:text-primary"
                      onClick={() => handlePresetSelect(preset)}
                    >
                      {preset.label}
                    </div>
                  ))}
                </div>
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={date?.from}
                  selected={date}
                  onSelect={setDate}
                  numberOfMonths={numberOfMonths}
                />
              </div>
            </>
          ) : (
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={setDate}
              numberOfMonths={numberOfMonths}
            />
          )}
        </PopoverContent>
      </Popover>
      {showExternalPresets && (
        <Select
          onValueChange={(value) => {
            setDate(undefined);
            const selectedPreset = allPresets.find(
              (preset) => preset.label === value
            );
            if (selectedPreset) {
              handlePresetSelect(selectedPreset);
            }
          }}
        >
          <SelectTrigger className="w-[140px] rounded-l-none border-l-0">
            <SelectValue placeholder="Select Range" />
          </SelectTrigger>
          <SelectContent position="popper">
            {allPresets.map((preset) => (
              <SelectItem key={preset.label} value={preset.label}>
                {preset.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
