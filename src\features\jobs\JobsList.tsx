import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { GetJobsService } from "@/services/jobs.service";
import JobDialog from "./JobDialog";
// import { useAppSelector } from "@/redux"; // Not used in the provided snippet directly for UI
import { snackbar } from "@/utils/snackbar.util";
import { formatDate } from "@/utils/commons.utils";
import {
  FileText,
  Mic,
  Briefcase,
  BarChartBig,
  ClipboardList,
  CalendarDays,
  User as UserIcon, // Renamed to avoid conflict with User status icon
  CheckCircle2,
  Clock,
  AlertCircle,
  RotateCw,
  Info,
  Search,
  ChevronDown,
  Users as UsersStatusIcon, // Icon for Reviewing status
} from "lucide-react";
import { Pagination } from "@/components/custom/Pagination";
import Button from "@/components/custom/Button";

// Types for Pill details
type StatusPillDetails = {
  text: string;
  bgColor: string;
  textColor: string;
  icon: JSX.Element;
};

type PriorityPillDetails = {
  text: string;
  bgColor: string;
  textColor: string;
};

// Mock priorities and user names as they are not in the Project type
const MOCK_PRIORITIES = ["HIGH", "MEDIUM", "LOW"];
const MOCK_USER_NAMES = [
  "Sarah Chen",
  "Mike Johnson",
  "Lisa Wang",
  "David Kumar",
  "Anna Lee",
  "Omar Ibrahim",
  "Sophia Miller",
];
const getMockUserName = (userId: string, index: number): string => {
  // In a real app, you'd fetch user details or have them in `created_by`
  // For now, use a seeded approach based on index for variety
  return MOCK_USER_NAMES[index % MOCK_USER_NAMES.length];
};

// Helper function for Job Type Icons
const getJobTypeIcon = (jobName: string, processName: string | undefined) => {
  const lowerJobName = jobName.toLowerCase();
  const lowerProcessName = (processName || "").toLowerCase();

  if (lowerProcessName.includes("legal document processing"))
    return <ClipboardList size={20} className="text-green-600" />;
  if (
    lowerJobName.includes("transcribe customer interview") ||
    lowerProcessName.includes("customer research")
  )
    return <Mic size={20} className="text-yellow-600" />;
  if (lowerProcessName.includes("insurance claims"))
    return <FileText size={20} className="text-blue-600" />;
  if (lowerProcessName.includes("healthcare analytics"))
    return <BarChartBig size={20} className="text-green-600" />;
  if (lowerProcessName.includes("meeting intelligence"))
    return <Mic size={20} className="text-yellow-600" />;
  // Fallback based on general job name terms
  if (lowerJobName.includes("document") || lowerJobName.includes("text"))
    return <FileText size={20} className="text-gray-500" />;
  if (
    lowerJobName.includes("audio") ||
    lowerJobName.includes("transcribe") ||
    lowerJobName.includes("interview")
  )
    return <Mic size={20} className="text-gray-500" />;
  if (lowerJobName.includes("data") || lowerJobName.includes("report"))
    return <BarChartBig size={20} className="text-gray-500" />;

  return <Briefcase size={20} className="text-gray-500" />; // Default
};

// Helper function for Status Pills
const getStatusPillDetails = (status: string): StatusPillDetails => {
  const s = status.toLowerCase();
  switch (s) {
    case "completed":
      return {
        text: "COMPLETED",
        bgColor: "bg-green-500",
        textColor: "text-white",
        icon: <CheckCircle2 size={14} />,
      };
    case "reviewing": // Example, adapt if your status is different
      return {
        text: "REVIEWING",
        bgColor: "bg-teal-600",
        textColor: "text-white",
        icon: <UsersStatusIcon size={14} />,
      };
    case "processing":
      return {
        text: "PROCESSING",
        bgColor: "bg-cyan-500",
        textColor: "text-white",
        icon: <RotateCw size={14} className="opacity-75" />,
      };
    case "pending":
      return {
        text: "PENDING",
        bgColor: "bg-yellow-400",
        textColor: "text-yellow-900",
        icon: <Clock size={14} />,
      };
    case "failed": // Example, adapt if your status is different
      return {
        text: "FAILED",
        bgColor: "bg-red-600",
        textColor: "text-white",
        icon: <AlertCircle size={14} />,
      };
    default:
      return {
        text: status.toUpperCase(),
        bgColor: "bg-gray-400",
        textColor: "text-white",
        icon: <Info size={14} />,
      };
  }
};

// Helper function for Priority Pills (matching image style)
const getPriorityPillDetails = (priority: string): PriorityPillDetails => {
  const p = priority.toUpperCase();
  switch (p) {
    case "HIGH":
      return { text: "HIGH", bgColor: "bg-pink-600", textColor: "text-white" };
    case "MEDIUM":
      return {
        text: "MEDIUM",
        bgColor: "bg-yellow-500",
        textColor: "text-white",
      };
    case "LOW":
      return { text: "LOW", bgColor: "bg-green-600", textColor: "text-white" }; // Image shows green for low
    default:
      return { text: p, bgColor: "bg-gray-400", textColor: "text-white" };
  }
};

// Job Card Component
interface JobCardProps {
  job: Project;
  priority: string;
  userName: string;
  onViewDetails: () => void;
}

const JobCard: React.FC<JobCardProps> = ({
  job,
  priority,
  userName,
  onViewDetails,
}) => {
  const jobIcon = getJobTypeIcon(job.name, job.process_name);
  const statusPill = getStatusPillDetails(job.status);
  // const priorityPill = getPriorityPillDetails(priority);

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow duration-200">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        {/* Left side: Icon, Title, Meta */}
        <div className="flex items-start gap-3 flex-grow min-w-0 w-full sm:w-auto">
          <div className="flex-shrink-0 mt-1">{jobIcon}</div>
          <div className="flex-grow min-w-0">
            <h3
              className="text-base font-semibold text-gray-800 truncate"
              title={job.name}
            >
              {job.name}
            </h3>
            <div className="mt-1 flex items-center flex-wrap text-xs text-gray-500 gap-x-2 gap-y-1">
              <span className="flex items-center">
                <CalendarDays size={12} className="mr-1 flex-shrink-0" />
                {formatDate(job.created_at)}
              </span>
              <span className="text-gray-300 hidden md:inline">•</span>
              <span
                className="truncate hidden md:inline bg-gray-100 rounded-full p-1"
                title={job.process_name || "N/A"}
              >
                {job.process_name || "N/A"}
              </span>
              {/* <span className="text-gray-300 hidden md:inline">•</span> */}
              {/* <span className="flex items-center">
                <UserIcon size={12} className="mr-1 flex-shrink-0" />
                {userName}
              </span> */}
            </div>
          </div>
        </div>

        {/* Right side: Priority, Status, View Details */}
        <div className="flex flex-col items-stretch sm:items-center sm:flex-row gap-2 sm:gap-3 flex-shrink-0 w-full sm:w-auto mt-3 sm:mt-0">
          <div className="flex items-center justify-end sm:justify-start gap-2">
            {/* {priority && (
              <span
                className={`px-2 py-0.5 text-xs font-semibold rounded-full ${priorityPill.bgColor} ${priorityPill.textColor}`}
              >
                {priorityPill.text}
              </span>
            )} */}
            <span
              className={`flex items-center px-2 py-0.5 text-xs font-bold rounded-full ${statusPill.bgColor} ${statusPill.textColor}`}
            >
              {statusPill.icon && (
                <span className="mr-1.5">{statusPill.icon}</span>
              )}
              {statusPill.text}
            </span>
          </div>
          <button
            onClick={onViewDetails}
            className="px-3 py-1.5 text-xs font-medium text-yellow-700 bg-white border border-yellow-600 rounded-md hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 transition-colors whitespace-nowrap"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

// Skeleton Loader for Job Card
const JobCardSkeleton = () => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 animate-pulse">
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
      <div className="flex items-start gap-3 flex-grow min-w-0 w-full sm:w-auto">
        <div className="w-5 h-5 bg-gray-300 rounded mt-1 flex-shrink-0"></div>
        <div className="flex-grow min-w-0">
          <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div className="flex items-center text-xs text-gray-500 gap-x-2 gap-y-1">
            <div className="h-3 bg-gray-300 rounded w-20"></div>
            <div className="h-3 bg-gray-300 rounded w-24 hidden md:block"></div>
            <div className="h-3 bg-gray-300 rounded w-16"></div>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-stretch sm:items-center sm:flex-row gap-2 sm:gap-3 flex-shrink-0 w-full sm:w-auto mt-3 sm:mt-0">
        <div className="flex items-center justify-end sm:justify-start gap-2">
          <div className="h-5 w-12 bg-gray-300 rounded-full"></div>
          <div className="h-5 w-20 bg-gray-300 rounded-full"></div>
        </div>
        <div className="h-7 w-full sm:w-24 bg-gray-300 rounded-md"></div>
      </div>
    </div>
  </div>
);

export default function JobsList({ projectId, projectDetails }: JobListProps) {
  const router = useRouter();

  const getQueryNum = (key: string, fallback: number = 1) =>
    typeof router.query[key] === "string"
      ? parseInt(router.query[key])
      : fallback;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [projects, setProjects] = useState<Project[]>([]); // These are the "jobs"
  const [meta, setMeta] = useState<Meta>({
    page: getQueryNum("page", 1),
    limit: getQueryNum("limit", 10),
    total: 0,
    total_pages: 0,
  });
  const [newDialogOpen, setNewDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null); // Used for editing/creating a job

  // State for search and filter (UI only for now, no filtering logic implemented)
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  const updateUrl = (params: { page?: number; limit?: number }) => {
    const query = { ...router.query, ...params };
    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null) delete query[key];
    });
    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  const fetchProjects = async (
    page: number,
    limit: number,
    isInitial = false
  ) => {
    if (isInitial) setInitialLoading(true);
    else setLoading(true);

    const response = await GetJobsService(projectId, page, limit); // Assuming GetJobsService is correct
    if (response.success) {
      setProjects(response.data.data);
      setMeta(response.data.meta);
    } else snackbar.showErrorMessage(response.data);

    isInitial ? setInitialLoading(false) : setLoading(false);
  };

  useEffect(() => {
    if (projectId) {
      fetchProjects(meta.page, meta.limit, true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]); // Removed meta.page, meta.limit from initial fetch dependencies

  useEffect(() => {
    if (projectId && !initialLoading) {
      // Fetch only if not initial load and projectId is present
      fetchProjects(meta.page, meta.limit);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta.page, meta.limit]); // This effect runs when page/limit changes

  const handleChangePage = (delta: number) => {
    const newPage = meta.page + delta;
    setMeta((prev) => ({ ...prev, page: newPage }));
    updateUrl({ page: newPage });
  };

  const handleChangeRowsPerPage = (newLimit: number) => {
    setMeta({ ...meta, page: 1, limit: newLimit });
    updateUrl({ limit: newLimit, page: 1 });
  };

  const handleProjectSaved = () => fetchProjects(meta.page, meta.limit);

  // Filtering logic would go here if implemented
  // const filteredProjects = projects.filter(p =>
  //   p.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
  //   (statusFilter === "" || p.status.toLowerCase() === statusFilter)
  // );
  const displayedProjects = projects; // Use this until filtering is fully implemented

  return (
    <>
      <JobDialog
        open={newDialogOpen}
        setOpen={setNewDialogOpen}
        selectedProject={selectedProject}
        setSelectedProject={setSelectedProject}
        onProjectCreated={handleProjectSaved}
        projectId={projectId}
      />

      {/* Header */}
      <div className="flex items-center justify-between mt-6 mb-4">
        <h1 className="text-3xl font-bold text-gray-900">Jobs</h1>
      </div>

      {/* Search, Filter, and + New Job button row */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="relative flex-grow sm:max-w-xs md:max-w-sm lg:max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search jobs..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <select
              className="appearance-none w-full sm:w-auto bg-white border border-gray-300 rounded-md pl-3 pr-10 py-2 text-sm text-gray-700 focus:ring-blue-500 focus:border-blue-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="processing">Processing</option>
              <option value="pending">Pending</option>
              <option value="reviewing">Reviewing</option>
              <option value="failed">Failed</option>
            </select>
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </div>
          </div>
          <Button
            // className="bg-primary hover:bg-primary-foreground text-white font-medium px-4 py-2 rounded-md transition-colors duration-200 whitespace-nowrap text-sm"
            variant="default"
            onClick={() => {
              setNewDialogOpen(true);
              setSelectedProject(null);
            }}
          >
            + New Joba
          </Button>
        </div>
      </div>

      {/* Jobs List Area */}
      <div className="relative">
        {initialLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, idx) => (
              <JobCardSkeleton key={idx} />
            ))}
          </div>
        ) : displayedProjects.length > 0 ? (
          <div className="space-y-4">
            {displayedProjects.map((job, index) => (
              <JobCard
                key={job._id}
                job={job}
                priority={MOCK_PRIORITIES[index % MOCK_PRIORITIES.length]} // Mocked
                // userName={getMockUserName(job.created_by, index)} // Mocked
                onViewDetails={() =>
                  router.push(`/projects/${projectId}/job/${job._id}`)
                }
              />
            ))}
          </div>
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg p-12 text-center">
            <Briefcase size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-xl font-semibold text-gray-700">No jobs found</p>
            <p className="text-gray-500">
              {searchTerm || statusFilter
                ? "Try adjusting your search or filters, or "
                : "Please "}
              create a new job to get started.
            </p>
          </div>
        )}

        {/* Loading overlay for subsequent loads */}
        {loading && !initialLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex justify-center items-center rounded-lg z-10">
            <div className="w-10 h-10 border-4 border-blue-200 border-t-blue-500 rounded-full opacity-75" />
          </div>
        )}
      </div>

      {/* Pagination */}
      {/* {meta.total > 0 && !initialLoading && displayedProjects.length > 0 && (
        <div className="mt-6 flex flex-col md:flex-row justify-between items-center px-6 py-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <label htmlFor="rowsPerPage" className="text-sm text-gray-700">
              Rows per page:
            </label>
            <select
              id="rowsPerPage"
              className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={meta.limit}
              onChange={(e) =>
                handleChangeRowsPerPage(parseInt(e.target.value))
              }
            >
              {[5, 10, 25, 50, 100].map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-4">
            <button
              className="px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
              onClick={() => handleChangePage(-1)}
              disabled={meta.page === 1}
            >
              Previous
            </button>
            <span className="text-sm text-gray-700">
              Page {meta.page} of {meta.total_pages}
            </span>
            <button
              className="px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
              onClick={() => handleChangePage(1)}
              disabled={meta.page >= meta.total_pages}
            >
              Next
            </button>
          </div>
        </div>
      )} */}

      <Pagination
        count={meta.total}
        rowsPerPage={meta.limit}
        page={meta.page - 1}
        onPageChange={(newPage) => handleChangePage(newPage)}
        onRowsPerPageChange={(newLimit) => handleChangeRowsPerPage(newLimit)}
      />
    </>
  );
}

// Type definitions (assuming these are already defined elsewhere or should be)
type Meta = {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
};

type Project = {
  // This is effectively a "Job" in this context
  _id: string;
  name: string;
  description: string;
  process_name?: string; // Made optional as it's used with || "N/A"
  created_at: string;
  created_by: string; // Assume it's an ID, actual name is mocked
  project_id: string;
  items: Array<{
    // This might not be directly used in the list item display
    input_id: string;
    output_id: string;
    status: string;
    output_object_name: string;
  }>;
  status: string; // e.g., "completed", "pending", "processing"
  updated_at: string;
  completed_at?: string;
  error_count: number;
};

type JobListProps = {
  projectId: string;
  projectDetails: { name?: string } | null; // Simplified ProjectDetails for breadcrumbs
};
