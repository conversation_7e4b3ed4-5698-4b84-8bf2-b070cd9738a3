"use client";

import { motion } from "framer-motion";

interface LoadingAnalysisStateProps {
  className?: string;
}

export default function LoadingAnalysisState({
  className = "",
}: LoadingAnalysisStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`flex flex-col items-center justify-center p-20 text-center ${className}`}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="w-12 h-12 border-4 border-sky-200 border-t-sky-600 rounded-full mb-6"
      />
      <motion.h3
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-xl font-semibold text-gray-700 mb-2"
      >
        Loading Analysis Data
      </motion.h3>
      <motion.p
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="text-gray-500"
      >
        Please wait while we fetch the latest results...
      </motion.p>
    </motion.div>
  );
}
