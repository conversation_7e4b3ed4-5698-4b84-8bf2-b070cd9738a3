// import { ReactElement, useState } from "react";
// import { NextAppProvider } from "@toolpad/core/nextjs";
// import { DashboardLayout } from "@toolpad/core/DashboardLayout";
// import { Stack, Chip, Avatar, IconButton } from "@mui/material";
// import Image from "next/image";
// import { getCookie } from "cookies-next";
// import Menu from "@/components/mui-vanced/Menu";
// import MenuItem from "@/components/mui-vanced/MenuItem";
// import { NAVIGATION, BRANDING } from "@/constants/navigation";
// import Snackbar from "../mui-vanced/Snackbar";
// import dynamic from "next/dynamic";
// import { LogoutService } from "@/services/auth.service";
// const ChangePasswordDialog = dynamic(
//   () => import("@/features/change-password/ChangePasswordDialog"),
//   { ssr: false }
// );

// function CustomAppTitle() {
//   const tenantLabel = (getCookie("tenant_label") as string) || "test_tenant";
//   return (
//     <Stack direction="row" alignItems="center" spacing={1}>
//       <Image src="/logo.png" alt="Logo" width={40} height={40} />
//       {/* <Typography
//         variant="h5"
//         sx={{
//           fontWeight: 600,
//           color: (theme) => theme.palette.primary.main,
//           display: "flex",
//           alignItems: "center",
//         }}
//       >
//         Aroma
//       </Typography> */}
//       <Chip size="small" label={tenantLabel} color="info" />
//     </Stack>
//   );
// }

// function ToolbarProfile() {
//   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
//   const open = Boolean(anchorEl);

//   const [changePasswordDialogOpen, setChangePasswordDialogOpen] =
//     useState(false);

//   const handleClick = (event: React.MouseEvent<HTMLElement>) => {
//     setAnchorEl(event.currentTarget);
//   };

//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleChangePassword = () => {
//     handleClose();
//     setChangePasswordDialogOpen(true);
//   };

//   return (
//     <>
//       <ChangePasswordDialog
//         open={changePasswordDialogOpen}
//         setOpen={() => setChangePasswordDialogOpen(false)}
//       />
//       <IconButton sx={{ p: 0 }} onClick={handleClick}>
//         <Avatar
//           sx={{ width: 32, height: 32, bgcolor: "primary.main" }}
//           alt="User Image"
//         >
//           {/* {username ? username.charAt(0).toUpperCase() : null} */}
//         </Avatar>
//       </IconButton>
//       <Menu
//         anchorEl={anchorEl}
//         open={open}
//         onClose={handleClose}
//         anchorOrigin={{
//           vertical: "bottom",
//           horizontal: "right",
//         }}
//         transformOrigin={{
//           vertical: "top",
//           horizontal: "right",
//         }}
//       >
//         <MenuItem onClick={handleChangePassword}>Change Password</MenuItem>
//         <MenuItem onClick={LogoutService}>Sign Out</MenuItem>
//       </Menu>
//     </>
//   );
// }

// export default function ProtectedLayout({ children }: ProtectedLayoutProps) {
//   return (
//     <NextAppProvider navigation={NAVIGATION} branding={BRANDING}>
//       <DashboardLayout
//         slots={{
//           appTitle: CustomAppTitle,
//           toolbarAccount: ToolbarProfile,
//         }}
//       >
//         {/* <PageContainer title={title}> */}
//         {children}
//         {/* </PageContainer> */}
//         <Snackbar />
//       </DashboardLayout>
//     </NextAppProvider>
//   );
// }

// interface ProtectedLayoutProps {
//   children: ReactElement;
// }
