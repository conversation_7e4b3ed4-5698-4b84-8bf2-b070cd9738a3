import { Fragment, useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Trash } from "lucide-react";
import Button from "@/components/custom/Button";
import { DeleteProjectService } from "@/services/projects.service";
import { snackbar } from "@/utils/snackbar.util";
type DeleteProjectDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedProject?: Project | null;
  setSelectedProject?: React.Dispatch<React.SetStateAction<Project | null>>;
  onProjectDeleted?: () => void;
};

export default function DeleteProjectDialog({
  open,
  setOpen,
  selectedProject,
  setSelectedProject,
  onProjectDeleted,
}: DeleteProjectDialogProps) {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!open) {
      setLoading(false);
      // Clear selected project when dialog closes
      setTimeout(() => {
        setSelectedProject?.(null);
      }, 150); // Shorter timeout to clear faster
    }
  }, [open, setSelectedProject]);

  if (!selectedProject || !setSelectedProject) {
    return null;
  }

  const handleClose = () => {
    setOpen(false);
    // selectedProject will be cleared by useEffect when open becomes false
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const res = await DeleteProjectService(selectedProject._id);
    if (res.success) {
      snackbar.showSuccessMessage(
        res.data?.detail || "Project deleted successfully"
      );
      onProjectDeleted?.();
      handleClose();
    } else {
      snackbar.showErrorMessage(res.data || "Failed to delete project");
      setLoading(false);
    }
  };

  return (
    <Transition appear show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-150"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/70" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto p-4">
          <div className="flex min-h-full items-center justify-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-200"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-150"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-full max-w-md rounded-xl bg-white dark:bg-zinc-900 p-6 shadow-lg transition-all">
                <div className="flex items-center justify-between mb-4">
                  <DialogTitle className="text-lg font-semibold">
                    Delete Project
                  </DialogTitle>
                </div>

                <div className="text-sm text-zinc-600 dark:text-zinc-300 mb-6">
                  Are you sure you want to delete the project{" "}
                  <span className="font-semibold text-zinc-800 dark:text-white">
                    {selectedProject.name}
                  </span>
                  ? This action cannot be undone.
                </div>

                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleClose}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    className="btn-danger"
                    onClick={handleSubmit}
                    disabled={loading}
                    loading={loading}
                    startIcon={<Trash className="w-4 h-4" />}
                  >
                    Confirm
                  </Button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
