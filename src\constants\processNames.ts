const a = {
  "audio-transcribe-analysis": {
    "name": "audio-transcribe-analysis",
    "description": "Transcribe audio and perform analysis",
    "schema": {
      "urls": {
        "type": "array",
        "description": "List of audio URLs to process",
        "items": {
          "type": "string",
          "format": "uri"
        }
      },
      "project_name": {
        "type": "string",
        "description": "Optional project name",
        "default": "Audio Analysis Project"
      },
      "job_name": {
        "type": "string",
        "description": "Optional job name",
        "default": "Audio Analysis Job"
      }
    }
  },
  "generic-entity-extraction": {
    "name": "generic-entity-extraction",
    "description": "Extract structured entities from text, URLs, or audio sources",
    "schema": {
      "input_type": {
        "type": "string",
        "description": "Type of input: text, url, or audio",
        "enum": [
          "text",
          "url",
          "audio"
        ]
      },
      "inputs": {
        "type": "array",
        "description": "List of raw texts, URLs, or audio file URLs",
        "items": {
          "type": "string"
        }
      },
      "project_name": {
        "type": "string",
        "description": "Optional project name",
        "default": "Entity Extraction Project"
      },
      "job_name": {
        "type": "string",
        "description": "Optional job name",
        "default": "Entity Extraction Job"
      }
    }
  },
  "extract-image-data": {
    "name": "extract-image-data",
    "description": "Extract text and diagrams from images",
    "schema": {
      "urls": {
        "type": "array",
        "description": "List of image URLs to process",
        "items": {
          "type": "string",
          "format": "uri"
        }
      },
      "project_name": {
        "type": "string",
        "description": "Optional project name",
        "default": "Image Extraction Project"
      },
      "job_name": {
        "type": "string",
        "description": "Optional job name",
        "default": "Image Extraction Job"
      }
    }
  }
}