import { useState, useEffect } from "react";

interface UseScrollPositionOptions {
  threshold?: number;
  element?: HTMLElement | null;
}

export const useScrollPosition = (options: UseScrollPositionOptions = {}) => {
  const { threshold = 0, element } = options;
  const [scrollY, setScrollY] = useState(0);
  const [isScrolledPast, setIsScrolledPast] = useState(false);

  useEffect(() => {
    // If element is provided but null, wait for it to be available
    if (options.element !== undefined && !element) {
      return;
    }

    const targetElement = element || window;

    const handleScroll = () => {
      const currentScrollY = element
        ? element.scrollTop
        : window.pageYOffset || document.documentElement.scrollTop;

      setScrollY(currentScrollY);
      const shouldShow = currentScrollY > threshold;
      setIsScrolledPast(shouldShow);
    };

    // Set initial values
    handleScroll();

    if (element) {
      element.addEventListener("scroll", handleScroll, { passive: true });
    } else {
      window.addEventListener("scroll", handleScroll, { passive: true });
    }

    return () => {
      if (element) {
        element.removeEventListener("scroll", handleScroll);
      } else {
        window.removeEventListener("scroll", handleScroll);
      }
    };
  }, [threshold, element]);

  return {
    scrollY,
    isScrolledPast,
  };
};
