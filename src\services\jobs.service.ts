import api from "@/utils/axios.util";

export const GetJobsService = async (
  projectId: string,
  page = 1,
  limit = 50,
  created_at_start?: string | null,
  created_at_end?: string | null,
  search?: string | null,
  status?: string | null,
  sortByCreatedAt?: boolean | null,
  sortAscending?: boolean | null
) => {
  try {
    const projections = [
      "_id",
      "name",
      "description",
      "process_name",
      "created_at",
      "created_by",
      "project_id",
      "items",
      "status",
      "updated_at",
      "completed_at",
      "error",
      "error_count",
      "assigned_to",
      "assigned_to_name",
    ];

    const response = await api.get(`/projects/${projectId}/jobs`, {
      params: {
        page,
        limit,
        ...(created_at_start ? { created_at_start } : {}),
        ...(created_at_end ? { created_at_end } : {}),
        ...(search ? { search } : {}),
        ...(status ? { status } : {}),
        projection: projections,
        sort_by_created_at: sortByCreatedAt,
        sort_ascending: sortAscending,
      },
      paramsSerializer: (params) => {
        const parts: string[] = [];
        for (const [key, value] of Object.entries(params)) {
          if (Array.isArray(value)) {
            value.forEach((val) => {
              parts.push(
                `${encodeURIComponent(key)}=${encodeURIComponent(val)}`
              );
            });
          } else if (value !== undefined && value !== null) {
            parts.push(
              `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`
            );
          }
        }
        return parts.join("&");
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.message ||
        "An error occured while getting projects",
    };
  }
};

export const CreateJobService = async (
  projectId: string,
  name: string,
  description: string,
  process_name: string
) => {
  try {
    const response = await api.post(`/jobs/create/${projectId}`, {
      name,
      description,
      process_name,
    });

    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while creating project",
    };
  }
};

export const AddMediaToJobService = async (
  jobId: string,
  mediaUris: string[],
  mediaFiles: File[]
) => {
  try {
    const formData = new FormData();
    mediaFiles.forEach((file) => {
      formData.append("media_files", file);
    });
    mediaUris.forEach((uri) => {
      formData.append("media_uris", uri);
    });
    const response = await api.post(`/jobs/add-media/${jobId}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while adding media to job",
    };
  }
};

export const GetJobStatusService = async (jobId: string) => {
  try {
    const response = await api.get(`/jobs/status/${jobId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job status",
    };
  }
};

export const ExecuteJobService = async (jobId: string) => {
  try {
    const response = await api.post(`/jobs/execute/${jobId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while executing job",
    };
  }
};

export const GetJobOutputService = async (
  jobId: string,
  includePresignedUrl = true,
  expiry = 3600
) => {
  try {
    const response = await api.get(`/jobs/output/${jobId}`, {
      params: {
        include_presigned_urls: includePresignedUrl,
        expiry,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job output",
    };
  }
};

export const GetPresignedUrlService = async (
  objectName: string,
  expiry = 3600
) => {
  try {
    const response = await api.get(`/jobs/media/presigned-url`, {
      params: {
        object_name: objectName,
        expiry,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting presigned URL",
    };
  }
};

export const GetJobDetailsService = async (jobId: string) => {
  try {
    const response = await api.get(`/jobs/${jobId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job details",
    };
  }
};

export const GetJobItemsService = async (
  jobId: string,
  page = 1,
  limit = 50,
  status?: string | null
) => {
  try {
    const response = await api.get(`/jobs/${jobId}/items`, {
      params: {
        page,
        limit,
        ...(status ? { status } : {}),
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job items",
    };
  }
};

export const AssignJobsToAgentService = async (
  jobIds: string[],
  userId: string
) => {
  try {
    const response = await api.post("/jobs/assign", {
      job_ids: jobIds,
      user_id: userId,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while assigning jobs to agent",
    };
  }
};

export const AssignJobToAgentService = async (
  jobId: string,
  userId: string
) => {
  try {
    const response = await api.post("/jobs/assign", {
      job_ids: [jobId],
      user_id: userId,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while assigning job to agent",
    };
  }
};

export const UnassignJobsService = async (jobIds: string[]) => {
  try {
    const response = await api.post("/jobs/unassign", {
      job_ids: jobIds,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while unassigning jobs",
    };
  }
};

export const GetMediaService = async (mediaId: string) => {
  try {
    const response = await api.get(`/media/${mediaId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting media details",
    };
  }
};

export const UpdateJobService = async (
  jobId: string,
  name: string,
  description: string
) => {
  try {
    const response = await api.put(`/jobs/${jobId}`, {
      name,
      description,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while updating job",
    };
  }
};

export const DeleteJobService = async (jobId: string) => {
  try {
    const response = await api.delete(`/jobs/${jobId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while deleting job",
    };
  }
};

export const GetAllJobs = async (
  page = 1,
  limit = 50,
  unassigned_only = false,
  status?: string | null,
  search?: string,
  assigned_to?: string | null
) => {
  try {
    const response = await api.get(`/jobs/assignment`, {
      params: {
        page,
        limit,
        ...(status ? { status } : {}),
        ...(search ? { search } : {}),
        ...(assigned_to ? { assigned_to } : {}),
        unassigned_only,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while getting jobs",
    };
  }
};

export const VerifyJobOutputService = async (
  jobId: string,
  outputId: string,
  verified: boolean,
  verificationNotes: string
) => {
  try {
    const response = await api.post(
      `/jobs/${jobId}/output/${outputId}/verify`,
      {
        verified,
        verification_notes: verificationNotes,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while verifying job output",
    };
  }
};

export const EditJobOutputService = async (
  jobId: string,
  outputId: string,
  updatedResult: any
) => {
  try {
    const response = await api.put(`/jobs/${jobId}/output/${outputId}/edit`, {
      result: updatedResult,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while editing job output",
    };
  }
};

export const GetJobOutputVerificationCountService = async (jobId: string) => {
  // return new Promise<{ verified: number; unverified: number; total: number }>(
  //   (resolve) => {
  //     const delay = Math.random() * 2000 + 500; // 500ms to 2500ms
  //     setTimeout(() => {
  //       resolve({
  //         verified: 2,
  //         unverified: 7,
  //         total: 9,
  //       });
  //     }, delay);
  //   }
  // );
  try {
    const response = await api.get(`/jobs/${jobId}/verification-count`);
    const data = response.data;
    return {
      verified: data.verified,
      unverified: data.unverified,
      total: data.verified + data.unverified,
    };
    // return response.data;
  } catch (error: any) {
    return {
      verified: 0,
      unverified: 0,
      total: 0,
    };
  }
};
