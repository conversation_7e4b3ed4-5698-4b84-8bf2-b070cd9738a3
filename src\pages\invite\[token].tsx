import { useRouter } from "next/router";
import Head from "next/head";
import Image from "next/image";
import { getJwtTokenData } from "@/utils/jwt.util";
import { RegisterUserService } from "@/services/users.service";
import { snackbar } from "@/utils/snackbar.util";
import { useState } from "react";
import { ArrowRight, UserPlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import AppInfoSection from "@/components/layout/AppInfoSection";

export default function InvitePage() {
  const router = useRouter();
  const { token, tenant } = router.query;

  // get the tenant from the url
  console.log(tenant);

  let decodedToken = getJwtTokenData(token as string);

  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!password) {
      setPasswordError("Password is required");
      return;
    }

    setLoading(true);
    const registerResponse = await RegisterUserService(
      decodedToken?.username as string,
      decodedToken?.role as string,
      password,
      token as string
    );

    if (registerResponse.success) {
      snackbar.showSuccessMessage(registerResponse.data.msg);
      // router.push("/login");
      // if tenant id exist, redirect to /tenant/login
      if (tenant) {
        router.push(`/${tenant}/login`);
      } else {
        router.push("/login");
      }
    } else {
      setLoading(false);
      snackbar.showErrorMessage(registerResponse.data);
    }
  };

  return (
    <>
      <Head>
        <title>Aroma - Join</title>
      </Head>
      <div className="min-h-screen flex w-full">
        {/* Left Side - App Information */}
        <AppInfoSection
          title="Aroma"
          subtitle="Task & Process Management"
          description="You've been invited to join our comprehensive platform for managing tasks, processing files with AI, and collaborating with your team."
        />

        {/* Right Side - Invitation Form */}
        <div className="w-full lg:w-[55%] flex items-center justify-center px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
            {/* Mobile card wrapper */}
            <div className="lg:hidden bg-white rounded-2xl shadow-xl border border-gray-200 p-6 sm:p-8 mb-8 animate-fade-in-up backdrop-blur-sm">
              {/* Mobile header */}
              <div className="text-center mb-6">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg animate-gentle-bounce">
                  <Image
                    src="/logo.png"
                    alt="Aroma Logo"
                    width={48}
                    height={48}
                    className="w-10 h-10 sm:w-12 sm:h-12"
                  />
                </div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 animate-fade-in-up animation-delay-200">
                  Join Aroma
                </h1>
                <p className="text-base sm:text-lg text-gray-600 animate-fade-in-up animation-delay-400">
                  Complete your invitation to get started
                </p>
              </div>

              {!decodedToken ? (
                <div className="text-center bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-600 font-medium">
                    Invalid or expired invitation token.
                  </p>
                  <p className="text-xs text-red-500 mt-2">
                    Please contact your administrator for a new invitation.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6 animate-fade-in-up animation-delay-600">
                  <div className="space-y-2">
                    <Label
                      htmlFor="username"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Username
                    </Label>
                    <Input
                      id="username"
                      type="text"
                      value={decodedToken?.username || ""}
                      disabled
                      className="h-12 text-sm px-4 bg-gray-50 border-2 border-gray-200 rounded-xl shadow-sm cursor-not-allowed"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label
                      htmlFor="role"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Role
                    </Label>
                    <Input
                      id="role"
                      type="text"
                      value={decodedToken?.role || ""}
                      disabled
                      className="h-12 text-sm px-4 bg-gray-50 border-2 border-gray-200 rounded-xl shadow-sm cursor-not-allowed"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label
                      htmlFor="password"
                      className="text-sm font-semibold text-gray-700"
                    >
                      Password <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        setPasswordError("");
                      }}
                      className="h-12 text-sm px-4 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                      disabled={loading}
                      autoFocus
                    />
                    {passwordError && (
                      <p className="text-xs text-red-600 animate-fade-in-left">{passwordError}</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-bold text-sm py-2 shadow-xl rounded-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] active:scale-[0.98]"
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span className="text-sm">Accepting...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <span className="text-sm">Accept Invitation</span>
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    )}
                  </Button>

                  {tenant && (
                    <div className="mt-3 text-xs text-slate-700">
                      Tenant: <span className="font-bold">{tenant}</span>
                    </div>
                  )}
                </form>
              )}
            </div>

            {/* Desktop content */}
            <div className="hidden lg:block w-full">
              <div className="text-center mb-8 sm:mb-10 lg:mb-12 animate-fade-in-up">
                <div className="w-14 h-14 sm:w-16 sm:h-16 lg:w-18 lg:h-18 bg-gradient-to-r from-slate-800 to-slate-900 rounded-xl flex items-center justify-center mx-auto mb-4 sm:mb-5 lg:mb-6 shadow-xl animate-pulse-glow">
                  <UserPlus className="w-7 h-7 sm:w-8 sm:h-8 lg:w-9 lg:h-9 text-white" />
                </div>
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 animate-fade-in-up animation-delay-200">
                  Join Aroma
                </h2>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600 animate-fade-in-up animation-delay-400">
                  Set your password to complete your invitation
                </p>
              </div>

              {!decodedToken ? (
                <div className="text-center bg-red-50 border border-red-200 rounded-lg p-4 sm:p-5 lg:p-6">
                  <p className="text-sm sm:text-base text-red-600 font-medium">
                    Invalid or expired invitation token.
                  </p>
                  <p className="text-xs sm:text-sm text-red-500 mt-2">
                    Please contact your administrator for a new invitation.
                  </p>
                </div>
              ) : (
                <form
                  onSubmit={handleSubmit}
                  className="space-y-6 sm:space-y-7 lg:space-y-8 animate-fade-in-up animation-delay-600"
                >
                  <div className="space-y-2 sm:space-y-3">
                    <Label
                      htmlFor="username"
                      className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                    >
                      Username
                    </Label>
                    <Input
                      id="username"
                      type="text"
                      value={decodedToken?.username || ""}
                      disabled
                      className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 bg-gray-50 border-2 border-gray-200 rounded-xl shadow-sm cursor-not-allowed"
                    />
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    <Label
                      htmlFor="role"
                      className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                    >
                      Role
                    </Label>
                    <Input
                      id="role"
                      type="text"
                      value={decodedToken?.role || ""}
                      disabled
                      className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 bg-gray-50 border-2 border-gray-200 rounded-xl shadow-sm cursor-not-allowed"
                    />
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    <Label
                      htmlFor="password"
                      className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                    >
                      Password <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        setPasswordError("");
                      }}
                      className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 lg:px-6 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                      disabled={loading}
                      autoFocus
                    />
                    {passwordError && (
                      <p className="text-xs sm:text-sm text-red-600 animate-fade-in-left">
                        {passwordError}
                      </p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 sm:h-14 lg:h-16 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-bold text-sm sm:text-base lg:text-lg py-2 sm:py-3 lg:py-4 shadow-xl rounded-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] active:scale-[0.98]"
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2 sm:gap-3">
                        <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span className="text-sm sm:text-base lg:text-lg">
                          Accepting...
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-1 sm:gap-2">
                        <span className="text-sm sm:text-base lg:text-lg">
                          Accept Invitation
                        </span>
                        <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                      </div>
                    )}
                  </Button>

                  {tenant && (
                    <div className="mt-3 sm:mt-4 text-xs sm:text-sm lg:text-base text-slate-700">
                      Tenant: <span className="font-bold">{tenant}</span>
                    </div>
                  )}
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
