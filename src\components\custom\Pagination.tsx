// import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Button from "./Button";

// import prev and next icons
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect } from "react";

interface PaginationProps {
  count: number;
  rowsPerPage: number;
  page: number; // zero-based
  onPageChange: (newPage: number) => void;
  onRowsPerPageChange: (newLimit: number) => void;
  rowsPerPageOptions?: number[];
}

const getPaginationRange = (current: number, total: number) => {
  const delta = 2;
  const range: (number | string)[] = [];

  const showLeftDots = current > delta + 2;
  const showRightDots = current < total - (delta + 3);

  range.push(0); // first page

  if (showLeftDots) range.push("...");

  const start = Math.max(1, current - delta);
  const end = Math.min(total - 2, current + delta);
  for (let i = start; i <= end; i++) range.push(i);

  if (showRightDots) range.push("...");

  if (total > 1) range.push(total - 1); // last page

  return range;
};

export const Pagination = ({
  count,
  rowsPerPage,
  page,
  onPageChange,
  onRowsPerPageChange,
  rowsPerPageOptions,
}: PaginationProps) => {
  const totalPages = Math.ceil(count / rowsPerPage);
  // if (totalPages <= 1) return null;

  const paginationRange = getPaginationRange(page, totalPages);

  const rowsPerPageOptionsList = rowsPerPageOptions || [10, 25, 50, 100];

  // on page change or size change, scroll to top, with smooth scroll animation
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [page, rowsPerPage]);

  return (
    <div className="flex justify-end items-center mt-4 space-x-4 flex-wrap">
      {/* Rows per page */}
      <div className="flex items-center space-x-2">
        <Select
          value={rowsPerPage.toString()}
          onValueChange={(value) => onRowsPerPageChange(parseInt(value))}
        >
          <SelectTrigger className="w-36">
            <SelectValue placeholder="Rows per page" />
          </SelectTrigger>
          <SelectContent>
            {rowsPerPageOptionsList.map((option) => (
              <SelectItem key={option} value={option.toString()}>
                {option} per page
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <span className="text-sm text-gray-500">
          Page {page + 1} of {totalPages}
        </span>
      </div>

      {/* Pagination controls */}
      <div className="flex space-x-2 items-center">
        <Button
          variant="outline"
          className="btn-secondary h-10 w-10 p-0"
          onClick={() => onPageChange(page - 1)}
          disabled={page === 0}
        >
          <ChevronLeft />
        </Button>

        {paginationRange.map((item, index) => {
          if (item === "...") {
            return (
              <span
                key={`dots-${index}`}
                className="px-2 text-gray-400 select-none"
              >
                ...
              </span>
            );
          }

          const itemPage = item as number;
          const isActive = itemPage === page;

          return (
            <Button
              key={itemPage}
              variant={isActive ? "default" : "outline"}
              onClick={() => onPageChange(itemPage)}
              className="min-w-[2.5rem]"
            >
              {itemPage + 1}
            </Button>
          );
        })}

        <Button
          variant="outline"
          className="btn-secondary h-10 w-10 p-0"
          onClick={() => onPageChange(page + 1)}
          disabled={page + 1 >= totalPages}
        >
          <ChevronRight />
        </Button>
      </div>
    </div>
  );
};
