// Random name generators for projects, jobs, and other entities

// Project name generator
const projectAdjectives = [
  "Awesome",
  "Brilliant",
  "Creative",
  "Dynamic",
  "Epic",
  "Fantastic",
  "Great",
  "Innovative",
  "Legendary",
  "Magnificent",
  "Outstanding",
  "Perfect",
  "Revolutionary",
  "Spectacular",
  "Ultimate",
  "Visionary",
  "Wonderful",
  "Amazing",
  "Incredible",
  "Extraordinary"
];

const projectNouns = [
  "Project",
  "Initiative",
  "Venture",
  "Mission",
  "Campaign",
  "Operation",
  "Program",
  "Solution",
  "System",
  "Platform",
  "Framework",
  "Application",
  "Service",
  "Tool",
  "Engine",
  "Hub",
  "Center",
  "Studio",
  "Lab",
  "Workshop"
];

// Job name generator
const jobTypes = [
  "Analysis",
  "Research",
  "Development",
  "Design",
  "Implementation",
  "Testing",
  "Review",
  "Optimization",
  "Integration",
  "Migration",
  "Deployment",
  "Monitoring",
  "Maintenance",
  "Documentation",
  "Training",
  "Support",
  "Consultation",
  "Planning",
  "Evaluation",
  "Assessment"
];

const jobDomains = [
  "Data",
  "Web",
  "Mobile",
  "Cloud",
  "Security",
  "Performance",
  "User Experience",
  "Database",
  "API",
  "Frontend",
  "Backend",
  "Infrastructure",
  "Analytics",
  "Machine Learning",
  "AI",
  "Blockchain",
  "IoT",
  "DevOps",
  "Quality",
  "Content"
];

// General name components
const techTerms = [
  "Alpha",
  "Beta",
  "Gamma",
  "Delta",
  "Omega",
  "Prime",
  "Core",
  "Edge",
  "Nexus",
  "Vertex",
  "Matrix",
  "Vector",
  "Quantum",
  "Neural",
  "Digital",
  "Smart",
  "Advanced",
  "Modern",
  "Future",
  "Next"
];

/**
 * Generates a random project name
 * @returns {string} A random project name
 */
export const generateRandomProjectName = (): string => {
  const adjective = projectAdjectives[Math.floor(Math.random() * projectAdjectives.length)];
  const noun = projectNouns[Math.floor(Math.random() * projectNouns.length)];
  const year = new Date().getFullYear();
  
  // 50% chance to include year
  if (Math.random() > 0.5) {
    return `${adjective} ${noun} ${year}`;
  }
  
  return `${adjective} ${noun}`;
};

/**
 * Generates a random job name
 * @returns {string} A random job name
 */
export const generateRandomJobName = (): string => {
  const domain = jobDomains[Math.floor(Math.random() * jobDomains.length)];
  const type = jobTypes[Math.floor(Math.random() * jobTypes.length)];
  
  return `${domain} ${type}`;
};

/**
 * Generates a random tech-focused name
 * @returns {string} A random tech name
 */
export const generateRandomTechName = (): string => {
  const tech = techTerms[Math.floor(Math.random() * techTerms.length)];
  const noun = projectNouns[Math.floor(Math.random() * projectNouns.length)];
  
  return `${tech} ${noun}`;
};

/**
 * Generates a random name with custom components
 * @param {string[]} prefixes - Array of prefix options
 * @param {string[]} suffixes - Array of suffix options
 * @returns {string} A random name combining prefix and suffix
 */
export const generateRandomName = (prefixes: string[], suffixes: string[]): string => {
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  
  return `${prefix} ${suffix}`;
};

/**
 * Generates a random code name (single word)
 * @returns {string} A random code name
 */
export const generateRandomCodeName = (): string => {
  const allTerms = [...projectAdjectives, ...techTerms];
  return allTerms[Math.floor(Math.random() * allTerms.length)];
};

/**
 * Generates a random name with number suffix
 * @param {string} baseName - Base name to append number to
 * @returns {string} Name with random number suffix
 */
export const generateRandomNameWithNumber = (baseName: string): string => {
  const number = Math.floor(Math.random() * 999) + 1;
  return `${baseName} ${number}`;
};
