// TypeScript file for generating random descriptions

// Arrays of words/phrases for generating random descriptions
const adjectives = [
  "amazing",
  "incredible",
  "stunning",
  "beautiful",
  "magnificent",
  "extraordinary",
  "breathtaking",
  "remarkable",
  "fantastic",
  "wonderful",
  "spectacular",
  "gorgeous",
  "elegant",
  "sophisticated",
  "charming",
  "delightful",
  "impressive",
  "outstanding",
  "brilliant",
  "marvelous",
  "captivating",
  "enchanting",
  "mesmerizing",
  "dazzling",
];

const nouns = [
  "landscape",
  "sunset",
  "mountain",
  "ocean",
  "forest",
  "garden",
  "cityscape",
  "adventure",
  "journey",
  "experience",
  "destination",
  "paradise",
  "sanctuary",
  "masterpiece",
  "creation",
  "discovery",
  "treasure",
  "wonder",
  "miracle",
  "phenomenon",
  "spectacle",
  "vista",
  "panorama",
  "scenery",
];

const descriptors = [
  "that takes your breath away",
  "filled with natural beauty",
  "beyond imagination",
  "that captivates the soul",
  "with endless possibilities",
  "that inspires wonder",
  "perfect for exploration",
  "rich in history and culture",
  "that tells a story",
  "waiting to be discovered",
  "that creates lasting memories",
  "full of surprises",
  "that touches the heart",
  "where dreams come true",
  "that exceeds expectations",
];

const contexts = [
  "nestled in the heart of nature",
  "hidden away from the world",
  "standing proudly",
  "gracefully positioned",
  "majestically rising",
  "peacefully situated",
  "dramatically positioned",
  "elegantly placed",
  "mysteriously located",
  "perfectly positioned",
  "strategically placed",
  "beautifully arranged",
];

/**
 * Generates a random description by combining random elements
 * @returns {string} A randomly generated description
 */
export const generateRandomDescription = (): string => {
  const randomAdjective =
    adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
  const randomDescriptor =
    descriptors[Math.floor(Math.random() * descriptors.length)];
  const randomContext = contexts[Math.floor(Math.random() * contexts.length)];

  // Different sentence structures for variety
  const structures = [
    `A ${randomAdjective} ${randomNoun} ${randomDescriptor}.`,
    `This ${randomAdjective} ${randomNoun} is ${randomContext} and ${randomDescriptor}.`,
    `Discover an ${randomAdjective} ${randomNoun} ${randomDescriptor}, ${randomContext}.`,
    `Experience the ${randomAdjective} ${randomNoun} ${randomContext}, ${randomDescriptor}.`,
    `An ${randomAdjective} ${randomNoun} awaits, ${randomContext} and ${randomDescriptor}.`,
  ];

  const randomStructure =
    structures[Math.floor(Math.random() * structures.length)];
  return randomStructure;
};

/**
 * Demo function to show how the generator works
 */
export const demoGenerator = (): void => {
  console.log("--- Random Description Generator Demo ---");

  for (let i = 1; i <= 5; i++) {
    const description = generateRandomDescription();
    console.log(`${i}. ${description}`);
  }

  console.log("--- End Demo ---");
};

/**
 * Generate multiple descriptions at once
 * @param count - Number of descriptions to generate
 * @returns Array of generated descriptions
 */
export const generateMultipleDescriptions = (count: number): string[] => {
  const descriptions: string[] = [];

  for (let i = 0; i < count; i++) {
    descriptions.push(generateRandomDescription());
  }

  return descriptions;
};

/**
 * Generate description with custom word arrays
 * @param customAdjectives - Custom adjectives array (optional)
 * @param customNouns - Custom nouns array (optional)
 * @returns Generated description using custom or default words
 */
export const generateCustomDescription = (
  customAdjectives?: string[],
  customNouns?: string[]
): string => {
  const adjectivesToUse =
    customAdjectives && customAdjectives.length > 0
      ? customAdjectives
      : adjectives;
  const nounsToUse =
    customNouns && customNouns.length > 0 ? customNouns : nouns;

  const randomAdjective =
    adjectivesToUse[Math.floor(Math.random() * adjectivesToUse.length)];
  const randomNoun = nounsToUse[Math.floor(Math.random() * nounsToUse.length)];
  const randomDescriptor =
    descriptors[Math.floor(Math.random() * descriptors.length)];
  const randomContext = contexts[Math.floor(Math.random() * contexts.length)];

  const structures = [
    `A ${randomAdjective} ${randomNoun} ${randomDescriptor}.`,
    `This ${randomAdjective} ${randomNoun} is ${randomContext} and ${randomDescriptor}.`,
    `Discover an ${randomAdjective} ${randomNoun} ${randomDescriptor}, ${randomContext}.`,
    `Experience the ${randomAdjective} ${randomNoun} ${randomContext}, ${randomDescriptor}.`,
    `An ${randomAdjective} ${randomNoun} awaits, ${randomContext} and ${randomDescriptor}.`,
  ];

  const randomStructure =
    structures[Math.floor(Math.random() * structures.length)];
  return randomStructure;
};
