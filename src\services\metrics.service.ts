import api from "@/utils/axios.util";

export const JobCountByStatusService = async (projectid?: string) => {
  try {
    const response = await api.get(`/metrics/job-count-by-status`, {
      params: {
        project_id: projectid,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job count by status for project",
    };
  }
};

export const JobCountByProcessService = async (projectid?: string) => {
  try {
    const response = await api.get(`/metrics/job-count-by-process`, {
      params: {
        project_id: projectid,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting job count by process for project",
    };
  }
};

export const GetRecentActivityService = async (
  page = 1,
  limit = 10,
  status?: string | null
) => {
  try {
    const response = await api.get(`/metrics/recent-activity`, {
      params: {
        page,
        limit,
        ...(status ? { status } : {}),
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting recent activity",
    };
  }
};
