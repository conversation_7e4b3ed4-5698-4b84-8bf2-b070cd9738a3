"use client";

import { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Clock,
  User,
  Bot,
  TrendingUp,
  BarChart3,
  AlertCircle,
  FileText,
  Edit3,
  Save,
  X,
  Shield,
  FileDown,
  Check,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  VerifyJobOutputService,
  EditJobOutputService,
  GetJobItemsService,
} from "@/services/jobs.service";
import {
  DeleteMediaService,
  GetInputInputMediaService,
  GetJobOutputMediaService,
} from "@/services/media.service";
import { snackbar } from "@/utils/snackbar.util";
import RemarkDialog from "@/components/custom/RemarkDialog";
import Button from "@/components/custom/Button";
import { MediaDialog } from "@/components/media-dialog";
import { motion } from "framer-motion";
import {
  LoadingAnalysisState,
  PendingAnalysisState,
  NoMediaFilesState,
  AnalysisErrorState,
} from "./index";

type JobMediaManagerProps = {
  jobId: string;
  onAddMediaClick: () => void;
  processName?: string;
};

export interface JobMediaManagerRef {
  refreshMediaList: () => void;
}

type TranscriptionSegment = {
  sender: string;
  timestamp: string;
  utterance: string;
};

type AnalysisData = {
  Clarity: number;
  "Clarity Description": string;
  "Overall Agent Performance": string;
  "Overall Agent Rating": number;
  Politeness: number;
  "Politeness Description": string;
  "Potential Customer Intention": string;
  "Product Mentions (Phrases)": string[];
  "Resolution Effort": number;
  "Resolution Effort Description": string;
  "Speech Tone": string;
  "Talk Ratio (Agent:Customer)": string;
};

type OutputResult = {
  transcription: TranscriptionSegment[];
  analysis: AnalysisData;
  pricing: {
    transcription_price_nep: number;
    analysis_price_nep: number;
    total_price_nep: number;
  };
  usage_metadata: {
    transcription: {
      prompt_token_count: number;
      candidates_token_count: number;
    };
    analysis: {
      prompt_token_count: number;
      candidates_token_count: number;
    };
  };
};

const JobMediaManager = forwardRef<JobMediaManagerRef, JobMediaManagerProps>(
  ({ jobId, onAddMediaClick }, ref) => {
    const [selectedIndex, setSelectedIndex] = useState<number>(0);
    const [jobInputMediaItemsData, setJobInputMediaItemsData] = useState<any[]>(
      []
    );
    const [currentItemOutput, setCurrentItemOutput] = useState<any>(null);
    const [isLoadingMediaList, setIsLoadingMediaList] = useState(true);
    const [isLoadingOutputs, setIsLoadingOutputs] = useState(true);

    const [allJobOutputs, setAllJobOutputs] = useState<any[] | null>(null);

    // Media dialog state
    const [showMediaDialog, setShowMediaDialog] = useState(false);
    const [mediaDialogItems, setMediaDialogItems] = useState<any[]>([]);
    const [activeMediaIndex, setActiveMediaIndex] = useState(0);

    // Editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editedResult, setEditedResult] = useState<any>(null);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Verification state
    const [remarkDialogOpen, setRemarkDialogOpen] = useState(false);
    const [verificationAction, setVerificationAction] = useState<
      "verify" | "unverify"
    >("verify");
    const [isVerifying, setIsVerifying] = useState(false);

    // Download state
    const [isDownloading, setIsDownloading] = useState(false);

    // Download service for JSON results
    const downloadJsonResult = (result: OutputResult, filename: string) => {
      const jsonData = JSON.stringify(result, null, 2);
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    };

    // Convert media data to MediaDialog format
    const convertToMediaDialogFormat = async (mediaList: any[]) => {
      const convertedItems = mediaList.map((media, index) => {
        // Use presigned URL that's already included in the media data
        const url = media.presigned_url || "";

        // Determine media type
        let type: "image" | "video" | "audio" | "document" = "document";
        if (media.content_type?.startsWith("image/")) {
          type = "image";
        } else if (media.content_type?.startsWith("video/")) {
          type = "video";
        } else if (media.content_type?.startsWith("audio/")) {
          type = "audio";
        }

        return {
          id: index,
          type,
          title: media.filename,
          url: url,
          thumbnail: url,
          product: {
            name: media.filename,
            price: media.content_type || "Unknown type",
          },
          mediaData: media, // Store original media data
        };
      });
      return convertedItems;
    };

    // Handle media dialog actions
    const handleMediaChange = (index: number) => {
      setActiveMediaIndex(index);
      setSelectedIndex(index);
    };

    // Handle media deletion
    const handleMediaDeleted = async (mediaId: string) => {
      try {
        const result = await DeleteMediaService(jobId, mediaId);
        if (result.success) {
          snackbar.showSuccessMessage("Media deleted successfully!");

          // Find the index of the deleted media
          const deletedIndex = jobInputMediaItemsData.findIndex(
            (media) => media._id === mediaId
          );

          // Update the selected index to show the next available media
          if (deletedIndex !== -1) {
            const newMediaCount = jobInputMediaItemsData.length - 1;
            if (newMediaCount === 0) {
              // No media left
              setSelectedIndex(0);
            } else if (deletedIndex >= newMediaCount) {
              // If we deleted the last item, select the previous one
              setSelectedIndex(newMediaCount - 1);
            } else {
              // Select the same index (which will now show the next item)
              setSelectedIndex(deletedIndex);
            }
          }

          // Refresh media list and outputs
          setIsLoadingMediaList(true);
          loadJobMediaItems();
          loadJobOutputs();
        } else {
          snackbar.showErrorMessage(result.data || "Failed to delete media");
        }
      } catch (error) {
        console.error("Failed to delete media:", error);
        snackbar.showErrorMessage("Failed to delete media");
      }
    };

    // Expose refresh function to parent component
    useImperativeHandle(ref, () => ({
      refreshMediaList: () => {
        // Refresh both media list and outputs
        if (jobId) {
          setIsLoadingMediaList(true);
          loadJobMediaItems();
          loadJobOutputs();
        }
      },
    }));

    const loadJobMediaItems = async () => {
      if (!jobId) {
        setIsLoadingMediaList(false);
        return;
      }

      try {
        // Step 1: Get job items to get input_id and output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error("Failed to load job items:", jobItemsResponse.data);
          snackbar.showErrorMessage("Failed to load job items.");
          setJobInputMediaItemsData([]);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setJobInputMediaItemsData([]);
          return;
        }

        // Step 2: Get input media details using input_id from job items
        const inputMediaPromises = jobItems.map(async (item: any) => {
          if (!item.input_id) return null;

          try {
            const mediaData = await GetInputInputMediaService(item.input_id);
            if (mediaData) {
              return {
                ...mediaData,
                status: item.status, // Add status from job item
              };
            }
            return null;
          } catch (error) {
            console.error(
              `Failed to load input media ${item.input_id}:`,
              error
            );
            return null;
          }
        });

        const inputMediaResults = await Promise.all(inputMediaPromises);
        const validInputMedia = inputMediaResults.filter(
          (media) => media !== null
        );

        setJobInputMediaItemsData(validInputMedia);
      } catch (error) {
        console.error("Failed to load job media items:", error);
        snackbar.showErrorMessage("Failed to load media files.");
        setJobInputMediaItemsData([]);
      } finally {
        setIsLoadingMediaList(false);
      }
    };

    const loadJobOutputs = async () => {
      if (!jobId) {
        setIsLoadingOutputs(false);
        return;
      }

      setIsLoadingOutputs(true);
      try {
        // Step 1: Get job items to get output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error(
            "Failed to load job items for outputs:",
            jobItemsResponse.data
          );
          snackbar.showErrorMessage("Failed to load job items.");
          setAllJobOutputs([]);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setAllJobOutputs([]);
          return;
        }

        // Step 2: Get output media details using output_id from job items (if exists)
        const outputMediaPromises = jobItems.map(async (item: any) => {
          if (!item.output_id) return null; // output_id might be null for pending jobs

          try {
            const outputData = await GetJobOutputMediaService(item.output_id);
            if (outputData) {
              // Handle verification status from metadata if available
              if (outputData.metadata?.verification) {
                outputData.verified =
                  outputData.metadata.verification.verified || false;
                outputData.verification_notes =
                  outputData.metadata.verification.verification_notes || "";
                outputData.verified_at =
                  outputData.metadata.verification.verified_at;
                outputData.verified_by =
                  outputData.metadata.verification.verified_by;
              }
              return outputData;
            }
            return null;
          } catch (error) {
            console.error(
              `Failed to load output media ${item.output_id}:`,
              error
            );
            return null;
          }
        });

        const outputMediaResults = await Promise.all(outputMediaPromises);
        const validOutputMedia = outputMediaResults.filter(
          (output) => output !== null
        );

        setAllJobOutputs(validOutputMedia.reverse());
      } catch (error) {
        console.error("Failed to load job outputs:", error);
        snackbar.showErrorMessage("Failed to load analysis data.");
        setAllJobOutputs([]);
      } finally {
        setIsLoadingOutputs(false);
      }
    };

    // Load media items and outputs on component mount
    useEffect(() => {
      if (jobId) {
        loadJobMediaItems();
        loadJobOutputs();
      }
    }, [jobId]);

    // Effect to set currentItemOutput based on selectedIndex and allJobOutputs
    useEffect(() => {
      if (
        jobInputMediaItemsData.length === 0 ||
        !jobInputMediaItemsData[selectedIndex]?._id
      ) {
        setCurrentItemOutput(null);
        return;
      }

      // Reset editing state when switching items
      setIsEditing(false);
      setEditedResult(null);
      setHasUnsavedChanges(false);

      // If outputs are still loading, don't set anything yet
      if (allJobOutputs === null) {
        return;
      }

      // Find the output for the selected input media
      // The output should have source_media_id that matches the input media _id
      const currentOutput =
        allJobOutputs.find(
          (output: any) =>
            output.metadata?.source_media_id ===
            jobInputMediaItemsData[selectedIndex]?._id
        ) || null;

      setCurrentItemOutput(currentOutput);
    }, [selectedIndex, jobInputMediaItemsData, allJobOutputs]);

    // Editing handlers
    const handleStartEdit = () => {
      if (currentItemOutput?.result) {
        setEditedResult(JSON.parse(JSON.stringify(currentItemOutput.result)));
        setIsEditing(true);
        setHasUnsavedChanges(false);
      }
    };

    const handleCancelEdit = () => {
      setIsEditing(false);
      setEditedResult(null);
      setHasUnsavedChanges(false);
    };

    const handleSaveResult = async () => {
      if (!editedResult || !currentItemOutput || !jobId) return;

      setIsSaving(true);
      try {
        const response = await EditJobOutputService(
          jobId,
          currentItemOutput._id,
          editedResult
        );
        if (response.success) {
          snackbar.showSuccessMessage("Changes saved successfully!");
          setHasUnsavedChanges(false);
          setIsEditing(false);
          setCurrentItemOutput({
            ...currentItemOutput,
            result: editedResult,
          });
        } else {
          snackbar.showErrorMessage(response.data || "Failed to save changes");
        }
      } catch (error) {
        snackbar.showErrorMessage("Failed to save changes");
      } finally {
        setIsSaving(false);
      }
    };

    // Verification handlers
    const handleVerify = () => {
      setVerificationAction("verify");
      setRemarkDialogOpen(true);
    };

    const handleUnverify = () => {
      setVerificationAction("unverify");
      setRemarkDialogOpen(true);
    };

    const handleVerificationConfirm = async (remark: string) => {
      if (!currentItemOutput || !jobId) return;

      setIsVerifying(true);
      try {
        const isVerifying = verificationAction === "verify";
        const response = await VerifyJobOutputService(
          jobId,
          currentItemOutput._id,
          isVerifying,
          remark
        );

        if (response.success) {
          const action = isVerifying ? "verified" : "unverified";
          snackbar.showSuccessMessage(`Output ${action} successfully!`);

          // Update the current output with new verification status
          const updatedOutput = {
            ...currentItemOutput,
            verified: isVerifying,
            verification_notes: remark,
            verified_at: isVerifying ? new Date().toISOString() : undefined,
          };

          // Also update the metadata.verification field
          if (updatedOutput.metadata) {
            updatedOutput.metadata.verification = {
              verified: isVerifying,
              verification_notes: remark,
              verified_at: isVerifying ? new Date().toISOString() : undefined,
              verified_by: "current_user", // You might want to get this from user context
            };
          }

          setCurrentItemOutput(updatedOutput);
          setRemarkDialogOpen(false);
        } else {
          snackbar.showErrorMessage(
            response.data || `Failed to ${verificationAction} output`
          );
        }
      } catch (error) {
        snackbar.showErrorMessage(`Failed to ${verificationAction} output`);
      } finally {
        setIsVerifying(false);
      }
    };

    // Download handler
    const handleDownload = async () => {
      if (!currentItemOutput?.result) return;

      setIsDownloading(true);
      try {
        const filename = `result_${currentItemOutput._id}.json`;
        downloadJsonResult(currentItemOutput.result, filename);
        snackbar.showSuccessMessage("JSON download started!");
      } catch (error) {
        snackbar.showErrorMessage("Failed to download result");
      } finally {
        setIsDownloading(false);
      }
    };

    // Handle retry analysis
    const handleRetryAnalysis = () => {
      setIsLoadingOutputs(true);
      loadJobOutputs();
    };

    const renderTranscription = (transcription: TranscriptionSegment[]) => {
      return (
        <div className="space-y-6 overflow-y-auto pr-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {transcription.map((segment, index) => (
            <div
              key={index}
              className={`flex ${
                segment.sender === "customer" ? "justify-start" : "justify-end"
              } mb-6 group`}
            >
              <div
                className={`flex ${
                  segment.sender === "customer"
                    ? "flex-row"
                    : "flex-row-reverse"
                } items-end gap-4 max-w-[85%] transition-all duration-200 group-hover:scale-[1.02]`}
              >
                {/* Avatar */}
                <div
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center shadow-md transition-all duration-200 ${
                    segment.sender === "customer"
                      ? "bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 border-2 border-gray-300"
                      : "bg-gradient-to-br from-sky-500 to-sky-600 text-white border-2 border-sky-400"
                  }`}
                >
                  {segment.sender === "customer" ? (
                    <User className="w-5 h-5" />
                  ) : (
                    <Bot className="w-5 h-5" />
                  )}
                </div>

                {/* Message bubble */}
                <div
                  className={`rounded-2xl px-5 py-4 shadow-lg transition-all duration-200 hover:shadow-xl ${
                    segment.sender === "customer"
                      ? "bg-white border border-gray-200 rounded-bl-md hover:border-gray-300"
                      : "bg-gradient-to-br from-sky-500 to-sky-600 text-white rounded-br-md shadow-sky-200"
                  }`}
                >
                  {/* Message header */}
                  <div
                    className={`flex items-center gap-3 mb-3 ${
                      segment.sender === "customer"
                        ? "justify-start"
                        : "justify-end"
                    }`}
                  >
                    <Badge
                      variant="outline"
                      className={`text-xs font-semibold px-3 py-1 ${
                        segment.sender === "customer"
                          ? "bg-gray-100 text-gray-700 border-gray-300"
                          : "bg-white/20 text-white border-white/40"
                      }`}
                    >
                      {segment.sender === "customer" ? "Customer" : "Agent"}
                    </Badge>
                    <span
                      className={`text-xs font-medium ${
                        segment.sender === "customer"
                          ? "text-gray-500"
                          : "text-white/80"
                      }`}
                    >
                      {segment.timestamp}
                    </span>
                  </div>

                  {/* Message content */}
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.transcription?.[index]?.utterance ||
                        segment.utterance
                      }
                      onChange={(e) => {
                        const newTranscription = [
                          ...(editedResult?.transcription || transcription),
                        ];
                        newTranscription[index] = {
                          ...newTranscription[index],
                          utterance: e.target.value,
                        };
                        setEditedResult({
                          ...editedResult,
                          transcription: newTranscription,
                        });
                        setHasUnsavedChanges(true);
                      }}
                      className={`text-md leading-relaxed min-h-[80px] resize-none border-2 rounded-lg p-3 transition-all duration-200 ${
                        segment.sender === "customer"
                          ? "text-gray-800 bg-white/90 border-gray-300 focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                          : "text-white bg-white/10 border-white/30 focus:ring-2 focus:ring-white/50 focus:border-white/50 placeholder:text-white/70"
                      }`}
                    />
                  ) : (
                    <p
                      className={`text-md leading-relaxed font-medium ${
                        segment.sender === "customer"
                          ? "text-gray-800"
                          : "text-white"
                      }`}
                    >
                      {segment.utterance}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    };

    const renderAnalysis = (analysis: AnalysisData) => {
      const getRatingColor = (rating: number) => {
        if (rating >= 4) return "text-green-700";
        if (rating >= 3) return "text-amber-700";
        return "text-red-700";
      };

      const getProgressColor = (rating: number) => {
        if (rating >= 4) return "bg-green-500";
        if (rating >= 3) return "bg-amber-500";
        return "bg-red-500";
      };

      const getProgressBgColor = () => {
        // if (rating >= 4) return "bg-green-50";
        // if (rating >= 3) return "bg-amber-50";
        // return "bg-red-50";
        return "";
      };

      const getBorderColor = () => {
        // if (rating >= 4) return "border-green-200";
        // if (rating >= 3) return "border-amber-200";
        // return "border-red-200";
        return "";
      };

      const handleAnalysisChange = (field: string, value: any) => {
        setEditedResult({
          ...editedResult,
          analysis: {
            ...editedResult.analysis,
            [field]: value,
          },
        });
        setHasUnsavedChanges(true);
      };

      return (
        <Card className="shadow-sm border bg-gradient-to-br from-white to-gray-50/50">
          <CardHeader className="pb-4 border-b border-gray-100">
            <CardTitle className="flex items-center gap-3 text-xl font-semibold">
              <div className="p-2 bg-sky-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-sky-600" />
              </div>
              <span className="text-gray-800">Call Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Performance Metrics Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-sky-600" />
                Performance Metrics
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Overall Rating */}
                <div
                  className={`p-5 rounded-md border-2 ${getBorderColor()} ${getProgressBgColor()} transition-all duration-200`}
                >
                  <div className="flex justify-between items-center mb-3">
                    <span className="font-semibold text-gray-900 text-base">
                      Overall Agent Rating
                    </span>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        max="5"
                        step="0.1"
                        value={
                          editedResult?.analysis?.["Overall Agent Rating"] ||
                          analysis["Overall Agent Rating"]
                        }
                        onChange={(e) =>
                          handleAnalysisChange(
                            "Overall Agent Rating",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      />
                    ) : (
                      <span
                        className={`font-bold text-lg ${getRatingColor(
                          analysis["Overall Agent Rating"]
                        )}`}
                      >
                        {analysis["Overall Agent Rating"]}/5
                      </span>
                    )}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(
                        analysis["Overall Agent Rating"]
                      )}`}
                      style={{
                        width: `${analysis["Overall Agent Rating"] * 20}%`,
                      }}
                    ></div>
                  </div>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.analysis?.["Overall Agent Performance"] ||
                        analysis["Overall Agent Performance"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Overall Agent Performance",
                          e.target.value
                        )
                      }
                      className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                      placeholder="Overall agent performance description..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {analysis["Overall Agent Performance"]}
                    </p>
                  )}
                </div>

                {/* Clarity */}
                <div
                  className={`p-5 rounded-md border-2 ${getBorderColor()} ${getProgressBgColor()} transition-all duration-200`}
                >
                  <div className="flex justify-between items-center mb-3">
                    <span className="font-semibold text-gray-900 text-base">
                      Clarity
                    </span>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        max="5"
                        step="0.1"
                        value={
                          editedResult?.analysis?.Clarity || analysis.Clarity
                        }
                        onChange={(e) =>
                          handleAnalysisChange(
                            "Clarity",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      />
                    ) : (
                      <span
                        className={`font-bold text-lg ${getRatingColor(
                          analysis.Clarity
                        )}`}
                      >
                        {analysis.Clarity}/5
                      </span>
                    )}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(
                        analysis.Clarity
                      )}`}
                      style={{ width: `${analysis.Clarity * 20}%` }}
                    ></div>
                  </div>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.analysis?.["Clarity Description"] ||
                        analysis["Clarity Description"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Clarity Description",
                          e.target.value
                        )
                      }
                      className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                      placeholder="Clarity description..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {analysis["Clarity Description"]}
                    </p>
                  )}
                </div>

                {/* Politeness */}
                <div
                  className={`p-5 rounded-md border-2 ${getBorderColor()} ${getProgressBgColor()} transition-all duration-200`}
                >
                  <div className="flex justify-between items-center mb-3">
                    <span className="font-semibold text-gray-900 text-base">
                      Politeness
                    </span>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        max="5"
                        step="0.1"
                        value={
                          editedResult?.analysis?.Politeness ||
                          analysis.Politeness
                        }
                        onChange={(e) =>
                          handleAnalysisChange(
                            "Politeness",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      />
                    ) : (
                      <span
                        className={`font-bold text-lg ${getRatingColor(
                          analysis.Politeness
                        )}`}
                      >
                        {analysis.Politeness}/5
                      </span>
                    )}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(
                        analysis.Politeness
                      )}`}
                      style={{ width: `${analysis.Politeness * 20}%` }}
                    ></div>
                  </div>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.analysis?.["Politeness Description"] ||
                        analysis["Politeness Description"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Politeness Description",
                          e.target.value
                        )
                      }
                      className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                      placeholder="Politeness description..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {analysis["Politeness Description"]}
                    </p>
                  )}
                </div>

                {/* Resolution Effort */}
                <div
                  className={`p-5 rounded-md border-2 ${getBorderColor()} ${getProgressBgColor()} transition-all duration-200`}
                >
                  <div className="flex justify-between items-center mb-3">
                    <span className="font-semibold text-gray-900 text-base">
                      Resolution Effort
                    </span>
                    {isEditing ? (
                      <input
                        type="number"
                        min="1"
                        max="5"
                        step="0.1"
                        value={
                          editedResult?.analysis?.["Resolution Effort"] ||
                          analysis["Resolution Effort"]
                        }
                        onChange={(e) =>
                          handleAnalysisChange(
                            "Resolution Effort",
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      />
                    ) : (
                      <span
                        className={`font-bold text-lg ${getRatingColor(
                          analysis["Resolution Effort"]
                        )}`}
                      >
                        {analysis["Resolution Effort"]}/5
                      </span>
                    )}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(
                        analysis["Resolution Effort"]
                      )}`}
                      style={{
                        width: `${analysis["Resolution Effort"] * 20}%`,
                      }}
                    ></div>
                  </div>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.analysis?.[
                          "Resolution Effort Description"
                        ] || analysis["Resolution Effort Description"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Resolution Effort Description",
                          e.target.value
                        )
                      }
                      className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                      placeholder="Resolution effort description..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {analysis["Resolution Effort Description"]}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Insights Section */}
            <div className="space-y-4 mt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-sky-600" />
                Additional Insights
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Speech Tone */}
                <div className="p-5 border-2 rounded-md transition-all duration-200 hover:shadow-md">
                  <h4 className="font-semibold mb-3 text-gray-900 flex items-center gap-2">
                    <div className="w-2 h-2 bg-sky-500 rounded-full"></div>
                    Speech Tone
                  </h4>
                  {isEditing ? (
                    <input
                      type="text"
                      value={
                        editedResult?.analysis?.["Speech Tone"] ||
                        analysis["Speech Tone"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange("Speech Tone", e.target.value)
                      }
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      placeholder="Speech tone..."
                    />
                  ) : (
                    <Badge
                      variant="outline"
                      className="border-sky-400 text-sky-700 bg-sky-100 px-3 py-1 text-sm font-medium"
                    >
                      {analysis["Speech Tone"]}
                    </Badge>
                  )}
                </div>

                {/* Talk Ratio */}
                <div className="p-5 border-2 rounded-md transition-all duration-200 hover:shadow-md">
                  <h4 className="font-semibold mb-3 text-gray-900 flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Talk Ratio
                  </h4>
                  {isEditing ? (
                    <input
                      type="text"
                      value={
                        editedResult?.analysis?.[
                          "Talk Ratio (Agent:Customer)"
                        ] || analysis["Talk Ratio (Agent:Customer)"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Talk Ratio (Agent:Customer)",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white"
                      placeholder="Talk ratio..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 font-medium">
                      {analysis["Talk Ratio (Agent:Customer)"]}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Customer Intention */}
                <div className="p-5 border-2  rounded-md transition-all duration-200 hover:shadow-md">
                  <h4 className="font-semibold mb-3 text-gray-900 flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Customer Intention
                  </h4>
                  {isEditing ? (
                    <Textarea
                      value={
                        editedResult?.analysis?.[
                          "Potential Customer Intention"
                        ] || analysis["Potential Customer Intention"]
                      }
                      onChange={(e) =>
                        handleAnalysisChange(
                          "Potential Customer Intention",
                          e.target.value
                        )
                      }
                      className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                      placeholder="Customer intention..."
                    />
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {analysis["Potential Customer Intention"]}
                    </p>
                  )}
                </div>

                {/* Product Mentions */}
                {analysis["Product Mentions (Phrases)"] &&
                  analysis["Product Mentions (Phrases)"].length > 0 && (
                    <div className="p-5 border-2  rounded-md transition-all duration-200 hover:shadow-md">
                      <h4 className="font-semibold mb-3 text-gray-900 flex items-center gap-2">
                        <div className="w-2 h-2  rounded-full"></div>
                        Product Mentions
                      </h4>
                      {isEditing ? (
                        <Textarea
                          value={
                            editedResult?.analysis?.[
                              "Product Mentions (Phrases)"
                            ]?.join(", ") ||
                            analysis["Product Mentions (Phrases)"].join(", ")
                          }
                          onChange={(e) =>
                            handleAnalysisChange(
                              "Product Mentions (Phrases)",
                              e.target.value.split(", ").filter((p) => p.trim())
                            )
                          }
                          className="text-sm text-gray-700 min-h-[80px] resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg"
                          placeholder="Product mentions (comma separated)..."
                        />
                      ) : (
                        <div className="flex flex-wrap gap-2">
                          {analysis["Product Mentions (Phrases)"].map(
                            (phrase, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs bg-emerald-100 text-emerald-800 border-emerald-300 px-3 py-1 font-medium"
                              >
                                {phrase}
                              </Badge>
                            )
                          )}
                        </div>
                      )}
                    </div>
                  )}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    };

    // Load media items and outputs on component mount
    useEffect(() => {
      if (jobId) {
        loadJobMediaItems();
        loadJobOutputs();
      }
    }, [jobId]);

    // Auto-open media dialog when media items are loaded and update dialog items when media changes
    useEffect(() => {
      if (!isLoadingMediaList && jobInputMediaItemsData.length > 0) {
        const updateMediaDialog = async () => {
          const convertedItems = await convertToMediaDialogFormat(
            jobInputMediaItemsData
          );
          setMediaDialogItems(convertedItems);

          // Open dialog if not already open
          if (!showMediaDialog) {
            setShowMediaDialog(true);
          }
        };
        updateMediaDialog();
      } else if (jobInputMediaItemsData.length === 0 && showMediaDialog) {
        // Close dialog if no media items
        setShowMediaDialog(false);
        setMediaDialogItems([]);
      }
    }, [
      isLoadingMediaList,
      jobInputMediaItemsData.length,
      jobInputMediaItemsData,
    ]);

    return (
      <>
        {/* Media Dialog */}
        {showMediaDialog && mediaDialogItems.length > 0 && (
          <MediaDialog
            mediaItems={mediaDialogItems}
            activeIndex={activeMediaIndex}
            onMediaChange={handleMediaChange}
            onAddMediaClick={onAddMediaClick}
            onMediaDeleted={handleMediaDeleted}
          />
        )}

        {/* Main Content */}
        {isLoadingMediaList || isLoadingOutputs ? (
          <LoadingAnalysisState />
        ) : currentItemOutput ? (
          currentItemOutput.result?.status === "error" ? (
            <AnalysisErrorState
              title="Media Processing Failed"
              description="The analysis failed to process this media file successfully."
              errorMessage={
                currentItemOutput.result.message ||
                currentItemOutput.result.raw_response ||
                "Unknown error occurred during processing"
              }
              onRetry={handleRetryAnalysis}
              showRetryButton={false}
            />
          ) : currentItemOutput.result ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              {/* Header with title, status and actions */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1, duration: 0.4 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-sky-100 rounded-lg">
                      <FileText className="w-6 h-6 text-sky-600" />
                    </div>
                    <div className="flex flex-col">
                      <h2 className="text-xl font-bold text-gray-800">
                        Output Details
                      </h2>
                      {/* {jobInputMediaItemsData[selectedIndex]?.filename && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <p className="text-sm text-gray-600 truncate max-w-[300px] cursor-help">
                              {jobInputMediaItemsData[selectedIndex].filename}
                            </p>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {jobInputMediaItemsData[selectedIndex].filename}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      )} */}
                    </div>
                  </div>

                  {/* Verification status and unsaved changes badges */}
                  <div className="flex items-center gap-3">
                    {currentItemOutput.verified ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            variant="outline"
                            className="text-green-700 border-green-400 bg-green-100 font-medium px-3 py-1 cursor-help"
                          >
                            <Check className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {currentItemOutput.verification_notes ||
                              "No verification notes"}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            variant="outline"
                            className="text-gray-600 border-gray-300 bg-gray-50 font-medium px-3 py-1 cursor-help"
                          >
                            <Shield className="w-3 h-3 mr-1" />
                            Not Verified
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>This output has not been verified yet</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                    {hasUnsavedChanges && (
                      <Badge
                        variant="outline"
                        className="text-amber-700 border-amber-400 bg-amber-100 font-medium px-3 py-1"
                      >
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Unsaved Changes
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {isEditing ? (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancelEdit}
                        disabled={isSaving}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSaveResult}
                        disabled={!hasUnsavedChanges || isSaving}
                        className="bg-sky-600 hover:bg-sky-700 text-white font-medium shadow-md focus:ring-sky-500"
                      >
                        {isSaving ? (
                          <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            className="w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-1"
                          />
                        ) : (
                          <Save className="w-4 h-4 mr-1" />
                        )}
                        {isSaving ? "Saving..." : "Save Changes"}
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleStartEdit}
                      >
                        <Edit3 className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={
                          currentItemOutput.verified
                            ? handleUnverify
                            : handleVerify
                        }
                        disabled={isVerifying}
                        className="text-green-700 border-green-400 bg-green-50 hover:bg-green-100 font-medium focus:ring-green-500"
                      >
                        <Check className="w-4 h-4 mr-1" />
                        {currentItemOutput.verified ? "Unverify" : "Verify"}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleDownload}
                        disabled={isDownloading || !currentItemOutput}
                        className="text-blue-700 border-blue-400 bg-blue-50 hover:bg-blue-100 font-medium focus:ring-blue-500"
                      >
                        <FileDown className="w-4 h-4 mr-1" />
                        Download Result
                      </Button>
                    </>
                  )}
                </div>
              </motion.div>

              {/* Content grid */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="grid grid-cols-1 gap-8"
              >
                {/* Transcription */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                >
                  <Card className="border shadow-sm bg-gradient-to-br from-white to-gray-50/50">
                    <CardHeader className="pb-4 border-b border-gray-100">
                      <CardTitle className="flex items-center gap-3 text-xl font-semibold">
                        <div className="p-2 bg-sky-100 rounded-lg">
                          <Clock className="w-6 h-6 text-sky-600" />
                        </div>
                        <span className="text-gray-800">Transcription</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                      {currentItemOutput.result.transcription &&
                        renderTranscription(
                          currentItemOutput.result.transcription
                        )}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Analysis */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4, duration: 0.4 }}
                  className="space-y-6"
                >
                  {currentItemOutput.result.analysis &&
                    renderAnalysis(currentItemOutput.result.analysis)}
                </motion.div>
              </motion.div>
            </motion.div>
          ) : (
            <div className="flex flex-col items-center justify-center p-20 text-center bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border-2 border-orange-200">
              <div className="p-4 bg-orange-100 rounded-full mb-6">
                <AlertCircle className="w-12 h-12 text-orange-600" />
              </div>
              <h3 className="text-2xl font-bold text-orange-700 mb-3">
                Processing Error
              </h3>
              <p className="text-gray-600 text-lg leading-relaxed max-w-md">
                We encountered an issue while processing this media file. Please
                try again or contact support if the problem persists.
              </p>
            </div>
          )
        ) : jobInputMediaItemsData.length === 0 && !isLoadingMediaList ? (
          <NoMediaFilesState onAddMediaClick={onAddMediaClick} />
        ) : jobInputMediaItemsData.length > 0 &&
          !isLoadingMediaList &&
          !currentItemOutput ? (
          <PendingAnalysisState />
        ) : null}

        {/* Verification Remark Dialog */}
        <RemarkDialog
          open={remarkDialogOpen}
          onOpenChange={setRemarkDialogOpen}
          title={
            verificationAction === "verify"
              ? "Verify Output"
              : "Unverify Output"
          }
          description={
            verificationAction === "verify"
              ? "Please provide a remark for verifying this output. This will mark the output as verified."
              : "Please provide a remark for unverifying this output. This will remove the verification status."
          }
          placeholder={
            verificationAction === "verify"
              ? "Enter verification notes..."
              : "Enter reason for unverifying..."
          }
          confirmLabel={verificationAction === "verify" ? "Verify" : "Unverify"}
          onConfirm={handleVerificationConfirm}
          loading={isVerifying}
          initialRemark={currentItemOutput?.verification_notes || ""}
          required={true}
        />
      </>
    );
  }
);

JobMediaManager.displayName = "JobMediaManager";

export default JobMediaManager;
