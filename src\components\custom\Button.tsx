import React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link";
type ButtonSize = "default" | "sm" | "lg" | "icon";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
}

const getVariantClasses = (variant: ButtonVariant) => {
  switch (variant) {
    case "default":
      return "bg-primary text-white hover:bg-secondary-foreground focus:black";
    case "destructive":
      return "bg-red-600 text-white hover:bg-red-700 focus:ring-red-500";
    case "outline":
      return "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-400";
    case "secondary":
      return "bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-400";
    case "ghost":
      return "text-gray-700 hover:bg-gray-100 focus:ring-gray-400";
    case "link":
      return "text-sky-700 underline-offset-4 hover:underline focus:black";
    default:
      return "bg-black text-white hover:bg-sky-700 focus:black";
  }
};

const getSizeClasses = (size: ButtonSize) => {
  switch (size) {
    case "sm":
      return "h-9 px-3 text-sm";
    case "lg":
      return "h-11 px-8 text-base";
    case "icon":
      return "h-10 w-10 p-0";
    default:
      return "h-10 px-4 py-2 text-sm";
  }
};

export default function Button({
  children,
  loading,
  startIcon,
  endIcon,
  variant = "default",
  size = "default",
  className,
  ...props
}: ButtonProps) {
  const isDisabled = loading || props.disabled;
  const baseClasses =
    "inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";
  const cursorClasses = isDisabled ? "cursor-not-allowed" : "cursor-pointer";
  const variantClasses = getVariantClasses(variant);
  const sizeClasses = getSizeClasses(size);

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses,
        cursorClasses,
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {loading ? (
        startIcon ? (
          <>
            <Loader2 className="animate-spin w-4 h-4" />
            {children}
          </>
        ) : endIcon ? (
          <>
            {children}
            <Loader2 className="animate-spin w-4 h-4" />
          </>
        ) : (
          <>
            <Loader2 className="animate-spin w-4 h-4" />
            Loading...
          </>
        )
      ) : (
        <>
          {startIcon && startIcon}
          {children}
          {endIcon && endIcon}
        </>
      )}
    </button>
  );
}
