import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface SnackbarState {
  open: boolean;
  type: "success" | "error" | "warning" | "info";
  message: string;
}

const initialState: SnackbarState = {
  open: false,
  type: "info",
  message: "",
};

const snackbarSlice = createSlice({
  name: "snackbar",
  initialState,
  reducers: {
    updateSnackbar: (state, action: PayloadAction<SnackbarState>) => {
      state.open = action.payload.open;
      state.type = action.payload.type;
      state.message = action.payload.message;
    },
    showSnackbar: (
      state,
      action: PayloadAction<{
        type: SnackbarState["type"];
        message: SnackbarState["message"];
      }>
    ) => {
      state.open = true;
      state.type = action.payload.type;
      state.message = action.payload.message;
    },
    hideSnackbar: (state) => {
      state.open = false;
    },
  },
});

export const { updateSnackbar, showSnackbar, hideSnackbar } = snackbarSlice.actions;
export default snackbarSlice.reducer;