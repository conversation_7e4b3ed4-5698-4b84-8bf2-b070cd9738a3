import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Head from "next/head";
import Image from "next/image";
import { setCookie, deleteCookie } from "cookies-next";
import { LoginService, VerifyTokenService } from "@/services/auth.service";
import { snackbar } from "@/utils/snackbar.util";
import type { GetServerSidePropsContext } from "next";
import { ArrowRight, Building2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import AppInfoSection from "@/components/layout/AppInfoSection";

export default function LoginPage() {
  const router = useRouter();
  const { tenant } = router.query;

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [usernameError, setUsernameError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    deleteCookie("access_token");
    deleteCookie("role");
    deleteCookie("username");
    deleteCookie("id");
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let valid = true;
    if (!username) {
      setUsernameError("Username is required");
      valid = false;
    }
    if (!password) {
      setPasswordError("Password is required");
      valid = false;
    }
    if (!valid) return;

    setLoading(true);
    const response = await LoginService(username, password, tenant as string);
    if (response.success) {
      const data = response.data;
      setCookie("access_token", data.access_token);
      setCookie("tenant_id", data.tenant_id);
      setCookie("tenant_label", data.tenant_label);
      setCookie("tenant_slug", data.tenant_slug);
      setCookie("username", data.username);
      setCookie("role", data.role);
      setCookie("id", data.id);
      router.push("/dashboard");
    } else {
      snackbar.showErrorMessage(response.data);
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Aroma - Login</title>
      </Head>
      <div className="min-h-screen flex w-full">
        {/* Left Side - App Information */}
        <AppInfoSection />

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-[55%] flex items-center justify-center px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
            {/* Mobile card wrapper */}
            <div className="lg:hidden bg-white rounded-2xl shadow-xl border border-gray-200 p-6 sm:p-8 mb-8 animate-fade-in-up backdrop-blur-sm">
              {/* Mobile header */}
              <div className="text-center mb-6">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg animate-gentle-bounce">
                  <Image
                    src="/logo.png"
                    alt="Aroma Logo"
                    width={48}
                    height={48}
                    className="w-10 h-10 sm:w-12 sm:h-12"
                  />
                </div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 animate-fade-in-up animation-delay-200">
                  Login to Aroma
                </h1>
                <p className="text-base sm:text-lg text-gray-600 animate-fade-in-up animation-delay-400">
                  Welcome back, you've been missed!
                </p>
                {tenant && (
                  <div className="mt-4 inline-flex items-center space-x-2 px-3 py-1.5 bg-slate-100 rounded-full text-xs">
                    <Building2 className="w-4 h-4 text-slate-600" />
                    <span className="font-medium text-slate-700">
                      Organization:{" "}
                      <span className="font-bold text-slate-900">{tenant}</span>
                    </span>
                  </div>
                )}
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label
                    htmlFor="username"
                    className="text-sm font-medium text-gray-700"
                  >
                    Username
                  </Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="your_username"
                    value={username}
                    onChange={(e) => {
                      setUsername(e.target.value);
                      setUsernameError("");
                    }}
                    className="h-10 text-sm px-3 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 rounded-lg shadow-sm"
                    disabled={loading}
                  />
                  {usernameError && (
                    <p className="text-xs text-red-600">{usernameError}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="password"
                    className="text-sm font-medium text-gray-700"
                  >
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      setPasswordError("");
                    }}
                    className="h-10 text-sm px-3 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-2 focus:ring-slate-200 rounded-lg shadow-sm"
                    disabled={loading}
                  />
                  {passwordError && (
                    <p className="text-xs text-red-600">{passwordError}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-10 bg-slate-800 hover:bg-slate-900 text-white font-bold text-sm py-2 shadow-lg rounded-lg transition-colors duration-200"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">Signing in...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-1">
                      <span className="text-sm">Sign In</span>
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  )}
                </Button>
              </form>
            </div>

            {/* Desktop content */}
            <div className="hidden lg:block w-full">
              <div className="text-center mb-8 sm:mb-10 lg:mb-12 animate-fade-in-up">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 animate-fade-in-up animation-delay-200">
                  Hello Again!
                </h2>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600 animate-fade-in-up animation-delay-400">
                  Welcome back, you've been missed!
                </p>
                {tenant && (
                  <div className="mt-4 sm:mt-5 lg:mt-6 inline-flex items-center space-x-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-slate-100 rounded-full text-xs sm:text-sm lg:text-base">
                    <Building2 className="w-4 h-4 sm:w-5 sm:h-5 text-slate-600" />
                    <span className="font-medium text-slate-700 bg-slate-100">
                      Organization:{" "}
                      <span className="font-bold text-slate-900">{tenant}</span>
                    </span>
                  </div>
                )}
              </div>

              <form
                onSubmit={handleSubmit}
                className="space-y-6 sm:space-y-7 lg:space-y-8 animate-fade-in-up animation-delay-600"
              >
                <div className="space-y-2 sm:space-y-3">
                  <Label
                    htmlFor="username"
                    className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                  >
                    Username
                  </Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="your_username"
                    value={username}
                    onChange={(e) => {
                      setUsername(e.target.value);
                      setUsernameError("");
                    }}
                    className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 lg:px-6 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                    disabled={loading}
                  />
                  {usernameError && (
                    <p className="text-xs sm:text-sm text-red-600 animate-fade-in-left">
                      {usernameError}
                    </p>
                  )}
                </div>

                <div className="space-y-2 sm:space-y-3">
                  <Label
                    htmlFor="password"
                    className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700"
                  >
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      setPasswordError("");
                    }}
                    className="h-12 sm:h-14 lg:h-16 text-sm sm:text-base lg:text-lg px-4 sm:px-5 lg:px-6 bg-white border-2 border-gray-300 focus:border-slate-500 focus:ring-4 focus:ring-slate-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                    disabled={loading}
                  />
                  {passwordError && (
                    <p className="text-xs sm:text-sm text-red-600 animate-fade-in-left">
                      {passwordError}
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 sm:h-14 lg:h-16 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-bold text-sm sm:text-base lg:text-lg py-2 sm:py-3 lg:py-4 shadow-xl rounded-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] active:scale-[0.98]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2 sm:gap-3">
                      <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm sm:text-base lg:text-lg">
                        Signing in...
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-1 sm:gap-2">
                      <span className="text-sm sm:text-base lg:text-lg">
                        Sign In
                      </span>
                      <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                    </div>
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const token = context.req.cookies.access_token;

  if (!token) return { props: {} };

  const auth = await VerifyTokenService(token);
  if (auth?.success) {
    return {
      redirect: {
        destination: "/dashboard",
        permanent: false,
      },
    };
  }

  return { props: {} };
}
