services:
  aroma-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
        - NEXT_PUBLIC_ROOT_URL=${NEXT_PUBLIC_ROOT_URL}
        - PORT=${PORT:-3030}
    network_mode: host
    environment:
      - NODE_ENV=production
      - PORT=${PORT:-3030}
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
      - NEXT_PUBLIC_ROOT_URL=${NEXT_PUBLIC_ROOT_URL}
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --tries=1 --spider http://localhost:${PORT:-3030}/api/health || exit 1"
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
