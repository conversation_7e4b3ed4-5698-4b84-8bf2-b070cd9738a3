import { Html, Head, Main, NextScript, DocumentProps } from "next/document";

export default function Document(props: DocumentProps) {
  return (
    <Html lang="en" data-toolpad-color-scheme="light">
      <Head>
        <meta name="emotion-insertion-point" content="" />
        <style>
          {`
            #preloader {
              position: fixed;
              inset: 0;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              background: #ffffff;
              z-index: 9999;
            }

            #preloader img {
              width: 80px;
              margin-bottom: 32px;
              animation: pulse 1.5s ease-in-out infinite;
            }

            .progress-bar {
              width: 200px;
              height: 8px;
              background: #e5e7eb;
              overflow: hidden;
              border-radius: 4px;
              position: relative;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .progress-bar::before {
              content: "";
              position: absolute;
              height: 100%;
              width: 40%;
              background: #30475E;
              animation: move 1.2s linear infinite;
              border-radius: 4px;
            }

            @keyframes move {
              0% {
                left: -40%;
              }
              100% {
                left: 100%;
              }
            }

            @keyframes pulse {
              0% {
                transform: scale(1);
                opacity: 1;
              }
              50% {
                transform: scale(1.1);
                opacity: 0.7;
              }
              100% {
                transform: scale(1);
                opacity: 1;
              }
            }

            @media (max-width: 768px) {
              #preloader img {
                width: 60px;
                margin-bottom: 24px;
              }
              .progress-bar {
                width: 150px;
              }
            }
          `}
        </style>
      </Head>
      <body>
        <div id="preloader">
          <img src="/logo.png" alt="Loading..." />
          {/* <div className="progress-bar"></div> */}
        </div>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
