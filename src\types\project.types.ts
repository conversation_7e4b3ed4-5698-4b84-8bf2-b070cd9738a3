// Job statistics by status
export interface JobStatisticsByStatus {
  pending: number;
  "in-progress": number;
  completed: number;
  failed: number;
  timeout: number;
  cancelled: number;
  "partially-completed": number;
}

// Job statistics by process type
export interface JobStatisticsByProcessType {
  "extract-image-data": number;
  "audio-transcribe-analysis": number;
  "generic-entity-extraction": number;
}

// Complete job statistics structure
export interface JobStatistics {
  by_status: JobStatisticsByStatus;
  by_process_type: JobStatisticsByProcessType;
  total_jobs: number;
}

// Base project type for API responses
export interface BaseProject {
  _id: string;
  name: string;
  description: string;
  created_at: string;
  created_by_username?: string;
}

// Extended project type with job statistics (matches actual API response)
export interface Project extends BaseProject {
  updated_at: string | null;
  created_by: string;
  job_statistics?: JobStatistics;
}

// Project form data type (for create/update operations)
export interface ProjectFormData {
  name: string;
  description: string;
}

// Meta type for pagination
export interface Meta {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
}

// API response types
export interface ProjectsResponse {
  data: Project[];
  meta: Meta;
}

export interface ProjectResponse {
  data: Project;
}
