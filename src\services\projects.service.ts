import api from "@/utils/axios.util";

export const GetProjectService = async (
  projectId: string,
  includeJobStats = true
) => {
  try {
    const response = await api.get(`/projects/${projectId}`, {
      params: {
        include_job_stats: includeJobStats,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting project details",
    };
  }
};

export const GetProjectsService = async (
  page = 1,
  limit = 12,
  created_at_start?: string | null,
  created_at_end?: string | null,
  search?: string | null,
  includeJobStats = true
) => {
  try {
    const response = await api.get("/projects/", {
      params: {
        page,
        limit,
        ...(search ? { search } : {}),
        ...(created_at_start ? { created_at_start } : {}),
        ...(created_at_end ? { created_at_end } : {}),
        include_job_stats: includeJobStats,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while getting projects",
    };
  }
};

export const CreateProjectService = async (
  name: string,
  description: string
) => {
  try {
    const response = await api.post("/projects/create", {
      name,
      description,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while creating project",
    };
  }
};

export const UpdateProjectService = async (
  projectId: string,
  name: string,
  description: string
) => {
  try {
    const response = await api.put(`/projects/${projectId}`, {
      name,
      description,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while updating project",
    };
  }
};

export const DeleteProjectService = async (projectId: string) => {
  try {
    const response = await api.delete(`/projects/${projectId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while deleting project",
    };
  }
};
