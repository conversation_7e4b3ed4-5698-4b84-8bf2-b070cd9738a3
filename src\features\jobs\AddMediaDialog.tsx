import { Fragment, useRef, useState } from "react";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Description,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { X, UploadCloud } from "lucide-react";
import TextField from "@/components/custom/TextField";
import Button from "@/components/custom/Button";
import { AddMediaToJobService } from "@/services/jobs.service";
import { useAppSelector } from "@/redux";
import { snackbar } from "@/utils/snackbar.util";

type AddMediaDialogProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  jobId: string;
  processName: ProcessName;
  onMediaUploadSuccess?: () => void;
};

export default function AddMediaDialog({
  open,
  setOpen,
  jobId,
  processName,
  onMediaUploadSuccess,
}: AddMediaDialogProps) {
  const [urlInput, setUrlInput] = useState("");
  const [mediaUrls, setMediaUrls] = useState<string[]>([]);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const processes = useAppSelector((state) => state.processes);

  // console.log("Processes:", processes[processName]?.valid_extensions);
  const validExtensions = processes[processName]?.valid_extensions || [];

  if (!jobId || !processName) {
    return;
  }

  const handleAddUrl = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const trimmed = urlInput.trim();
    if (trimmed) {
      setMediaUrls((prev) => [...prev, trimmed]);
      setUrlInput("");
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      setMediaFiles((prev) => [...prev, ...Array.from(files)]);
    }
  };

  const handleRemoveUrl = (url: string) => {
    setMediaUrls((prev) => prev.filter((u) => u !== url));
  };

  const handleRemoveFile = (file: File) => {
    setMediaFiles((prev) => prev.filter((f) => f !== file));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (mediaUrls.length === 0 && mediaFiles.length === 0) {
      snackbar.showWarningMessage(
        "Please provide at least one media URL or upload a file."
      );
      return;
    }

    setIsSubmitting(true);
    const uploadResponse = await AddMediaToJobService(
      jobId,
      mediaUrls,
      mediaFiles
    );

    if (uploadResponse.success) {
      snackbar.showSuccessMessage("Media added successfully!");
      setOpen(false);
      setUrlInput("");
      setMediaUrls([]);
      setMediaFiles([]);

      // Notify parent component about successful upload
      onMediaUploadSuccess?.();
    } else {
      snackbar.showErrorMessage(
        uploadResponse.data || "Upload failed. Please try again."
      );
    }
    setIsSubmitting(false);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <Transition appear show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={setOpen}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-200"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-150"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/70" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto p-4">
          <div className="flex min-h-full items-center justify-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-200"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-150"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-full max-w-lg transform rounded-xl bg-white dark:bg-zinc-900 p-6 shadow-xl transition-all">
                <DialogTitle className="text-xl font-semibold">
                  Add Media Items
                </DialogTitle>
                <Description className="text-sm text-muted-foreground mb-4">
                  The following types are supported for URL and File Upload.
                  <br />
                  <span className="font-semibold">
                    {validExtensions.join(", ")}
                  </span>
                </Description>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* URL Input */}
                  <div className="space-y-2">
                    <label
                      htmlFor="mediaUrl"
                      className="text-sm font-medium block"
                    >
                      Media URL
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-[1fr_auto] gap-2">
                      <TextField
                        id="mediaUrl"
                        type="url"
                        value={urlInput}
                        onChange={(e) => setUrlInput(e.target.value)}
                        placeholder={
                          processName === "audio-transcribe-analysis"
                            ? "https://example.com/media.mp3"
                            : processName === "extract-image-data"
                            ? "https://example.com/image.jpg"
                            : "https://example.com/media"
                        }
                        className="w-full"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleAddUrl();
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={handleAddUrl}
                        className="w-full sm:w-auto"
                      >
                        Add
                      </Button>
                    </div>

                    {mediaUrls.length > 0 && (
                      <div className="grid gap-2 mt-2">
                        {mediaUrls.map((url, idx) => (
                          <div
                            key={idx}
                            className="w-full flex items-center gap-2 bg-gray-100 dark:bg-zinc-800 px-3 py-2 rounded-md overflow-hidden"
                          >
                            <span
                              title={url}
                              className="text-sm truncate overflow-hidden whitespace-nowrap flex-1"
                            >
                              {url}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleRemoveUrl(url)}
                              className="text-red-500 hover:text-red-700 shrink-0"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* File Upload */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium block">
                      Upload Files
                    </label>
                    <div>
                      <input
                        type="file"
                        accept={
                          validExtensions.length > 0
                            ? validExtensions.join(", ")
                            : "audio/*,image/*"
                        }
                        multiple
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <div
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full h-24 flex flex-col items-center justify-center border-2 border-dashed border-gray-400 dark:border-zinc-600 rounded-md cursor-pointer bg-gray-100 dark:bg-zinc-800 hover:bg-gray-200 dark:hover:bg-zinc-700 transition-colors text-center text-sm text-gray-700 dark:text-gray-300"
                      >
                        <UploadCloud className="w-6 h-6 mb-1" />
                        <span>Click to select files</span>
                      </div>
                    </div>

                    {mediaFiles.length > 0 && (
                      <div className="grid gap-2 mt-2">
                        {mediaFiles.map((file, idx) => (
                          <div
                            key={idx}
                            className="w-full flex items-center gap-2 bg-gray-100 dark:bg-zinc-800 px-3 py-2 rounded-md overflow-hidden"
                          >
                            <span className="text-sm truncate overflow-hidden whitespace-nowrap flex-1">
                              {file.name}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile(file)}
                              className="text-red-500 hover:text-red-700 shrink-0"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Adding..." : "Add Media"}
                    </Button>
                  </div>
                </form>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
