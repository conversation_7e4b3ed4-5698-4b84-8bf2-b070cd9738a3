// components/shared/AudioPlayerButton.tsx

import React, { useEffect, useRef } from "react";
import { Play, Pause } from "lucide-react";
import Button from "@/components/custom/Button";

type AudioPlayerButtonProps = {
  mediaId: string;
  objectName: string;
  loadedUrl?: string;
  isPlaying: boolean;
  onPlay: (mediaId: string, audioEl: HTMLAudioElement) => void;
  onPause: () => void;
  size?: "sm" | "xs"; // Add size prop for smaller buttons
};

export const AudioPlayerButton: React.FC<AudioPlayerButtonProps> = ({
  mediaId,
  objectName,
  loadedUrl,
  isPlaying,
  onPlay,
  onPause,
  size = "sm",
}) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.play().catch((e) => console.error("Playback failed", e));
    } else {
      audio.pause();
    }

    const handleEnded = () => {
      onPause();
    };

    audio.addEventListener("ended", handleEnded);
    return () => {
      audio.removeEventListener("ended", handleEnded);
    };
  }, [isPlaying]);

  const handleClick = () => {
    if (!audioRef.current) return;
    if (isPlaying) {
      onPause();
    } else {
      onPlay(mediaId, audioRef.current);
    }
  };

  return (
    <>
      {size === "xs" ? (
        <Button
          size="icon"
          variant="outline"
          onClick={handleClick}
          className="h-7 w-7"
          aria-label={isPlaying ? "Pause audio" : "Play audio"}
        >
          {isPlaying ? (
            <Pause className="w-3 h-3" />
          ) : (
            <Play className="w-3 h-3" />
          )}
        </Button>
      ) : (
        <Button size="sm" variant="outline" onClick={handleClick}>
          {isPlaying ? (
            <>
              <Pause className="w-4 h-4 mr-1" /> Pause
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-1" /> Play
            </>
          )}
        </Button>
      )}
      <audio ref={audioRef} src={loadedUrl} className="hidden" />
    </>
  );
};
