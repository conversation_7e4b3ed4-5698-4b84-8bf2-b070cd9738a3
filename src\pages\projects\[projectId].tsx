import { useRouter } from "next/router";
import { withAuth } from "@/utils/withAuth";
import useFetchProcesses from "@/hooks/useProcesses";
import { useEffect, useState, useRef } from "react";
import { GetProjectService } from "@/services/projects.service";
import Head from "next/head";
import ProjectDetailsSection, {
  ProjectDetailsSectionRef,
} from "@/features/projects/ProjectDetailsSection";
import JobsList from "@/features/projects/JobsList";
import { Skeleton } from "@/components/ui/skeleton";
import { FolderOpen, ArrowLeft } from "lucide-react";
import Button from "@/components/custom/Button";
import AddMediaToSelectedJobsDialog from "@/features/jobs/AddMediaToSelectedJobsDialog";
import { ProjectDetailFloatingHeader } from "@/components/projects/ProjectDetailFloatingHeader";
import ProjectDialog from "@/features/projects/ProjectDialog";
import DeleteProjectDialog from "@/features/projects/DeleteProjectDialog";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";

export default function Project() {
  const router = useRouter();
  const { projectId } = router.query;

  const [projectDetails, setProjectDetails] = useState<Project | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());
  const [addMediaDialogOpen, setAddMediaDialogOpen] = useState(false);
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Filter state for sharing between JobsList and FloatingHeader
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [isSearching, setIsSearching] = useState(false);
  const [jobsLength, setJobsLength] = useState(0);

  // Refs for debouncing
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const projectDetailsSectionRef = useRef<ProjectDetailsSectionRef | null>(
    null
  );

  useFetchProcesses();

  // URL update function
  const updateUrl = (params: {
    search?: string;
    status?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const query: any = { ...router.query, ...params };
    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null || query[key] === "all") {
        delete query[key];
      }
    });
    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  // Scroll detection for floating header
  useEffect(() => {
    const handleScroll = () => {
      const mainElement = document.querySelector("main") as HTMLElement;
      if (mainElement) {
        const scrollTop = mainElement.scrollTop;
        const shouldShow = scrollTop > 300; // Show after scrolling 300px
        setShowFloatingHeader(shouldShow);
      }
    };

    const mainElement = document.querySelector("main") as HTMLElement;
    if (mainElement) {
      mainElement.addEventListener("scroll", handleScroll, { passive: true });
      // Check initial position
      handleScroll();

      return () => {
        mainElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, []);

  const fetchProjectDetails = async (id: string) => {
    const fetchProjectResponse = await GetProjectService(id as string);
    if (fetchProjectResponse.success) {
      setProjectDetails(fetchProjectResponse.data);
    } else {
      console.error(
        "Error fetching project details:",
        fetchProjectResponse.data
      );
    }
    setInitialLoading(false);
  };

  const handleStatusCardClick = (status: string) => {
    // Update URL with status filter
    const query = { ...router.query, status, page: 1 };
    router.push({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  const handleJobCountChange = () => {
    // This will be called by JobsTable when jobs are created/deleted
    // We can trigger a refresh of the project details status counts
    if (projectDetailsSectionRef.current?.refreshStatusCounts) {
      projectDetailsSectionRef.current.refreshStatusCounts();
    }
  };

  const handleJobSelectionChange = (newSelectedJobs: Set<string>) => {
    setSelectedJobs(newSelectedJobs);
  };

  const handleJobsLengthChange = (length: number) => {
    setJobsLength(length);
  };

  const handleAddMediaClick = () => {
    setAddMediaDialogOpen(true);
  };

  const handleMediaUploadSuccess = () => {
    // Refresh the jobs table or any other necessary updates
    // The JobsTable will handle its own refresh
  };

  // Filter handlers for sharing between JobsList and FloatingHeader
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Show searching state if there's a value and it's different from current debounced term
    if (value !== debouncedSearchTerm) {
      setIsSearching(true);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(value);
      setIsSearching(false);
      updateUrl({
        search: value || undefined,
        status: statusFilter === "all" ? undefined : statusFilter,
        start_date: dateRange?.from
          ? format(dateRange.from, "yyyy-MM-dd")
          : undefined,
        end_date: dateRange?.to
          ? format(dateRange.to, "yyyy-MM-dd")
          : undefined,
      });
    }, 500); // 500ms debounce delay
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    updateUrl({
      status: value === "all" ? undefined : value,
      search: debouncedSearchTerm || undefined,
      start_date: dateRange?.from
        ? format(dateRange.from, "yyyy-MM-dd")
        : undefined,
      end_date: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
    });
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);

    // Only update URL and trigger API call if both dates are selected or range is cleared
    if (!range || (range.from && range.to)) {
      updateUrl({
        start_date: range?.from ? format(range.from, "yyyy-MM-dd") : undefined,
        end_date: range?.to ? format(range.to, "yyyy-MM-dd") : undefined,
        search: debouncedSearchTerm || undefined,
        status: statusFilter === "all" ? undefined : statusFilter,
      });
    }
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setDebouncedSearchTerm("");
    setStatusFilter("all");
    setDateRange(undefined);
    setIsSearching(false);

    // Clear any pending search timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    updateUrl({
      search: undefined,
      status: undefined,
      start_date: undefined,
      end_date: undefined,
    });
  };

  const handleSelectAll = () => {
    // We need to get the actual job IDs from the JobsList component
    // For now, we'll just toggle the selection state
    if (selectedJobs.size === jobsLength) {
      setSelectedJobs(new Set());
    } else {
      // This will be handled by creating a ref to JobsList or passing job IDs up
      // For now, let's just clear the selection as a fallback
      setSelectedJobs(new Set());
    }
  };

  // Check if any filters are active
  const hasActiveFilters = Boolean(
    debouncedSearchTerm || statusFilter !== "all" || dateRange
  );

  // Initialize filter state from URL parameters
  useEffect(() => {
    if (router.isReady) {
      const urlSearchTerm = (router.query.search as string) || "";
      const urlStatusFilter = (router.query.status as string) || "all";
      const startDate = router.query.start_date as string;
      const endDate = router.query.end_date as string;

      setSearchTerm(urlSearchTerm);
      setDebouncedSearchTerm(urlSearchTerm);
      setStatusFilter(urlStatusFilter);

      if (startDate && endDate) {
        setDateRange({
          from: new Date(startDate),
          to: new Date(endDate),
        });
      }
    }
  }, [router.isReady]);

  useEffect(() => {
    if (projectId) {
      fetchProjectDetails(projectId as string);
    }
  }, [projectId]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Show skeleton during initial load

  // Show "Project not found" only after loading completes and no project data
  if (!initialLoading && !projectDetails) {
    return (
      <div className="flex flex-col items-center justify-center py-32 px-6 text-center">
        {/* Icon Container with enhanced gradient background */}
        <div className="w-32 h-32 bg-gradient-to-br from-sky-400 to-sky-600 rounded-2xl flex items-center justify-center mb-8 shadow-xl ring-4 ring-sky-100">
          <FolderOpen className="h-16 w-16 text-white" />
        </div>

        {/* Title */}
        <h3 className="text-3xl font-bold text-gray-900 mb-4">
          Project not found
        </h3>

        {/* Description */}
        <p className="text-gray-600 mb-12 max-w-lg leading-relaxed text-xl font-medium">
          The project you're looking for doesn't exist or may have been deleted.
        </p>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 justify-center">
          <Button
            variant="outline"
            onClick={() => router.back()}
            startIcon={<ArrowLeft className="w-5 h-5" />}
            className="px-8 py-4 text-lg font-semibold text-gray-700 border-gray-300 bg-white hover:bg-gray-50 hover:border-gray-400 shadow-md transition-all duration-200"
          >
            Go Back
          </Button>
          <Button
            onClick={() => router.push("/projects")}
            startIcon={<FolderOpen className="w-5 h-5" />}
            className="px-8 py-4 text-lg font-semibold bg-sky-600 hover:bg-sky-700 text-white shadow-lg transition-all duration-200 hover:shadow-xl"
          >
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  // At this point, we know projectDetails is not null due to the checks above
  if (!projectDetails) {
    return null; // This should never happen due to the checks above
  }

  return (
    <>
      <Head>
        <title>{projectDetails.name}</title>
      </Head>
      <div className="min-h-screen relative">
        {/* Floating Header */}
        <ProjectDetailFloatingHeader
          isVisible={showFloatingHeader && !initialLoading}
          project={projectDetails}
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          dateRange={dateRange}
          isSearching={isSearching}
          hasActiveFilters={hasActiveFilters}
          jobsLength={jobsLength}
          selectedJobsSize={selectedJobs.size}
          onSearchChange={handleSearchChange}
          onStatusFilterChange={handleStatusFilterChange}
          onDateRangeChange={handleDateRangeChange}
          onClearFilters={handleClearFilters}
          onSelectAll={handleSelectAll}
        />

        <div className="space-y-6">
          {/* Persistent Project Details Section - never remounts on search/filter changes */}
          <ProjectDetailsSection
            ref={projectDetailsSectionRef}
            project={projectDetails}
            onProjectUpdated={() => fetchProjectDetails(projectId as string)}
            onStatusCardClick={handleStatusCardClick}
            selectedJobs={selectedJobs}
            onAddMediaClick={handleAddMediaClick}
          />
          {/* JobsList with shared filter state */}
          <JobsList
            projectId={projectId as string}
            projectDetails={projectDetails}
            onJobCountChange={handleJobCountChange}
            onJobSelectionChange={handleJobSelectionChange}
            onJobsLengthChange={handleJobsLengthChange}
            searchTerm={searchTerm}
            debouncedSearchTerm={debouncedSearchTerm}
            statusFilter={statusFilter}
            dateRange={dateRange}
            isSearching={isSearching}
            hasActiveFilters={hasActiveFilters}
            onSearchChange={handleSearchChange}
            onStatusFilterChange={handleStatusFilterChange}
            onDateRangeChange={handleDateRangeChange}
            onClearFilters={handleClearFilters}
            onSelectAll={handleSelectAll}
          />
        </div>
      </div>
      {/* Add Media Dialog */}
      <AddMediaToSelectedJobsDialog
        open={addMediaDialogOpen}
        setOpen={setAddMediaDialogOpen}
        selectedJobIds={Array.from(selectedJobs)}
        onMediaUploadSuccess={handleMediaUploadSuccess}
      />

      {/* Project Edit Dialog */}
      <ProjectDialog
        open={editDialogOpen}
        setOpen={setEditDialogOpen}
        selectedProject={projectDetails}
        setSelectedProject={() => {}} // Not needed since we refresh from server
        onProjectCreated={() => fetchProjectDetails(projectId as string)}
      />

      {/* Project Delete Dialog */}
      <DeleteProjectDialog
        open={deleteDialogOpen}
        setOpen={setDeleteDialogOpen}
        selectedProject={projectDetails}
        setSelectedProject={() => {}} // Not needed since we navigate away
        onProjectDeleted={() => router.push("/projects")}
      />
    </>
  );
}

export const getServerSideProps = withAuth();
