import { useRouter } from "next/router";
import { withAuth } from "@/utils/withAuth";
import useFetchProcesses from "@/hooks/useProcesses";
import { useEffect, useState, useRef } from "react";
import Head from "next/head";
import { GetJobDetailsService } from "@/services/jobs.service";
import JobDetail, { JobDetailSkeleton } from "@/features/jobs/JobDetail";
import AddMediaDialog from "@/features/jobs/AddMediaDialog";
import JobNotFound from "@/features/jobs/JobNotFound";

import JobMediaFiles, {
  JobMediaManagerRef,
} from "@/features/jobs/JobMediaFiles";
import JobImageFiles, {
  JobImageManagerRef,
} from "@/features/jobs/JobImageFiles";
import JobGenericFiles, {
  JobGenericFilesRef,
} from "@/features/jobs/JobGenericFiles";

// import {
//   GetJobInputMediaService,
//   GetJobOutputMediaService,
// } from "@/services/media.service";

export default function Job() {
  const router = useRouter();
  const { jobId } = router.query;

  const [loading, setLoading] = useState(true);
  const [jobDetails, setJobDetails] = useState<Job | null>(null);

  const [mediaDialogVisible, setMediaDialogVisible] = useState(false);
  const mediaFilesRef = useRef<
    JobMediaManagerRef | JobImageManagerRef | JobGenericFilesRef | null
  >(null);

  useFetchProcesses();

  const fetchJobDetails = async (jobId: string) => {
    const fetchJobResponse = await GetJobDetailsService(jobId);

    if (fetchJobResponse.success) {
      setJobDetails(fetchJobResponse.data);
    }

    setLoading(false);
  };

  const handleMediaUploadSuccess = () => {
    // Refresh media list when new media is uploaded
    if (mediaFilesRef.current?.refreshMediaList) {
      mediaFilesRef.current.refreshMediaList();
    }
  };

  const handleJobStatusChange = (newStatus: string) => {
    // Update job details when status changes
    if (jobDetails) {
      setJobDetails({ ...jobDetails, status: newStatus });
    }

    // Refresh media list and outputs when job completes
    if (newStatus === "completed" && mediaFilesRef.current?.refreshMediaList) {
      mediaFilesRef.current.refreshMediaList();
    }
  };

  useEffect(() => {
    if (jobId) {
      fetchJobDetails(jobId as string);
    }
  }, [jobId]);

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Job Detail Skeleton */}
        <JobDetailSkeleton />
      </div>
    );
  }

  // (async () => {
  //   const ida = await GetJobInputMediaService("6847fbc6f6a11028ab49f463");
  //   const oda = await GetJobOutputMediaService("6847fea7f6a11028ab49f5df");
  //   console.log("jimsssssssssssssssssss", JSON.stringify(ida));
  //   console.log("jomsssssssssssssssssss", JSON.stringify(oda));
  // })();

  if ((!loading && !jobDetails) || !jobDetails) {
    return <JobNotFound />;
  }

  return (
    <>
      <Head>
        <title>{jobDetails?.name}</title>
      </Head>

      {/* <JobsList jobId={jobId as string} projectDetails={projectDetails} /> */}
      <JobDetail
        jobDetails={jobDetails}
        onJobUpdated={() => fetchJobDetails(jobId as string)}
        onJobStatusChange={handleJobStatusChange}
      />

      <AddMediaDialog
        open={mediaDialogVisible}
        setOpen={setMediaDialogVisible}
        jobId={jobId as string}
        processName={jobDetails.process_name}
        onMediaUploadSuccess={handleMediaUploadSuccess}
      />

      <div className="mb-8" />

      <div className="border-2 p-4 rounded-md">
        {jobDetails?.process_name === "audio-transcribe-analysis" ? (
          <JobMediaFiles
            ref={mediaFilesRef}
            jobId={jobId as string}
            onAddMediaClick={() => setMediaDialogVisible(true)}
            processName={jobDetails.process_name}
          />
        ) : jobDetails?.process_name === "extract-image-data" ? (
          <JobImageFiles
            ref={mediaFilesRef}
            jobId={jobId as string}
            onAddMediaClick={() => setMediaDialogVisible(true)}
          />
        ) : (
          <JobGenericFiles
            ref={mediaFilesRef}
            jobId={jobId as string}
            onAddMediaClick={() => setMediaDialogVisible(true)}
            processName={jobDetails.process_name}
          />
        )}
      </div>
    </>
  );
}

export const getServerSideProps = withAuth();
