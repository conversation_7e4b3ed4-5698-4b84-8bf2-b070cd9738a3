import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import Button from "@/components/custom/Button";
import { Co<PERSON>, Check, X, <PERSON>, Mail } from "lucide-react";
import { getCookie } from "cookies-next";

export default function InvitationUrlDialog({
  open,
  setOpen,
  invitationToken,
}: InvitationUrlDialogProps) {
  const tenant = getCookie("tenant_slug");
  const invitationUrl = `${process.env.NEXT_PUBLIC_ROOT_URL}/invite/${invitationToken}?tenant=${tenant}`;
  const [copyButtonChecked, setCopyButtonChecked] = useState(false);

  const handleCloseDialog = (event: React.SyntheticEvent) => {
    setOpen(false);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    setCopyButtonChecked(true);
    navigator.clipboard
      .writeText(invitationUrl)
      .then(() => {
        console.log("Invitation URL copied to clipboard");
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
    setTimeout(() => {
      setCopyButtonChecked(false);
    }, 2000);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {/* <div className="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
              <Mail className="h-5 w-5 text-green-600" />
            </div> */}
            <div>
              <h3 className="text-lg font-semibold">Invitation Generated</h3>
              <p className="text-sm text-gray-500 font-normal">
                Share this invitation link with the user
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="py-6 space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Invitation URL
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Link className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-gray-700 break-all font-mono">
                  {invitationUrl}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> This invitation link will allow the user to
              set their password and join your organization.
            </p>
          </div>
        </div>
        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
          >
            Close
          </Button>
          <Button
            onClick={handleSubmit}
            loading={copyButtonChecked}
            startIcon={
              copyButtonChecked ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )
            }
          >
            {copyButtonChecked ? "Copied!" : "Copy Link"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type InvitationUrlDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  invitationToken: string;
  setInviteUserDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
};
