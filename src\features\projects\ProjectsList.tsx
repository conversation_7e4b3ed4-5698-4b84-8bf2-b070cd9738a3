import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Button from "@/components/custom/Button";
import { Skeleton } from "@/components/ui/skeleton";
import { LoaderCircle, PlusIcon, FolderOpen } from "lucide-react";
import { GetProjectsService } from "@/services/projects.service";
import DeleteProjectDialog from "./DeleteProjectDialog";
import ProjectDialog from "./ProjectDialog";
import { snackbar } from "@/utils/snackbar.util";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { motion } from "framer-motion";

import { Pagination } from "@/components/custom/Pagination";
import ProjectCard from "./ProjectCard";
import TextField from "@/components/custom/TextField";
import EmptyState from "@/components/custom/EmptyState";
import { Meta, BaseProject } from "@/types/project.types";
import { FloatingHeader } from "@/components/projects/FloatingHeader";
import ProjectsFiltersBar from "./ProjectsFiltersBar";

export default function ProjectsList() {
  const router = useRouter();

  // Extract query params
  const getQuery = (key: string, fallback = "") =>
    typeof router.query[key] === "string" ? router.query[key] : fallback;

  const getQueryNum = (key: string, fallback = 1) =>
    typeof router.query[key] === "string"
      ? parseInt(router.query[key])
      : fallback;

  // Initialize state
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [projects, setProjects] = useState<Project[]>([]);
  const [meta, setMeta] = useState<Meta>({
    page: getQueryNum("page", 1),
    limit: getQueryNum("limit", 12),
    total: 0,
    total_pages: 0,
  });
  const [searchQuery, setSearchQuery] = useState(getQuery("search", ""));
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: getQuery("created_at_start")
      ? new Date(getQuery("created_at_start"))
      : undefined,
    to: getQuery("created_at_end")
      ? new Date(getQuery("created_at_end"))
      : undefined,
  });
  const [deleteProjectDialogOpen, setDeleteProjectDialogOpen] = useState(false);
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<BaseProject | null>(
    null
  );
  const [showFloatingHeader, setShowFloatingHeader] = useState(false);

  const updateUrl = (params: Record<string, any>) => {
    const query = {
      ...router.query,
      ...params,
    };

    Object.keys(query).forEach((key) => {
      if (query[key] === "" || query[key] == null) delete query[key];
    });

    router.replace({ pathname: router.pathname, query }, undefined, {
      shallow: true,
    });
  };

  useEffect(() => {
    const page = getQueryNum("page", 1);
    const limit = getQueryNum("limit", 12);
    const createdStart = getQuery("created_at_start", "");
    const createdEnd = getQuery("created_at_end", "");
    const search = getQuery("search", "");

    setSearchQuery(search);
    setDateRange({
      from: createdStart ? new Date(createdStart) : undefined,
      to: createdEnd ? new Date(createdEnd) : undefined,
    });
    setMeta((prev) => ({
      ...prev,
      page,
      limit,
    }));

    // Fetch real projects data on mount
    fetchProjects(
      page,
      limit,
      true,
      createdStart || null,
      createdEnd || null,
      search || null
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Scroll detection for floating header
  useEffect(() => {
    const handleScroll = () => {
      const mainElement = document.querySelector("main") as HTMLElement;
      if (mainElement) {
        const scrollTop = mainElement.scrollTop;
        const shouldShow = scrollTop > 300; // Show after scrolling 300px
        setShowFloatingHeader(shouldShow);
      }
    };

    const mainElement = document.querySelector("main") as HTMLElement;
    if (mainElement) {
      mainElement.addEventListener("scroll", handleScroll);
      return () => mainElement.removeEventListener("scroll", handleScroll);
    }
  }, []);

  const fetchProjects = async (
    page: number,
    limit: number,
    isInitial = false,
    createdAtStart?: string | null,
    createdAtEnd?: string | null,
    search?: string | null
  ) => {
    if (isInitial) {
      setInitialLoading(true);
    } else {
      setLoading(true);
    }

    const projectResponse = await GetProjectsService(
      page,
      limit,
      createdAtStart,
      createdAtEnd,
      search
    );

    if (projectResponse.success) {
      setProjects(projectResponse.data.data);
      setMeta(projectResponse.data.meta);
    } else {
      console.error("Error fetching projects:", projectResponse.data);
      snackbar.showErrorMessage(projectResponse.data);
    }

    if (isInitial) {
      setInitialLoading(false);
    } else {
      setLoading(false);
    }
  };

  const handleProjectSaved = () => {
    fetchProjects(
      meta.page,
      meta.limit,
      false,
      dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : null,
      dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : null,
      searchQuery
    );
  };

  useEffect(() => {
    if (!initialLoading) {
      // Only make API call if:
      // 1. No date range is selected (both from and to are null/undefined)
      // 2. Both from and to dates are selected
      const shouldFetch =
        (!dateRange?.from && !dateRange?.to) || // No date range
        (dateRange?.from && dateRange?.to); // Complete date range

      if (shouldFetch) {
        fetchProjects(
          meta.page,
          meta.limit,
          false,
          dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : null,
          dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : null,
          searchQuery
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta.page, meta.limit, dateRange, searchQuery]);

  const handleChangePage = (newPage: number) => {
    const actualPage = newPage + 1;
    setMeta((prev) => ({ ...prev, page: actualPage }));
    updateUrl({ page: actualPage });
  };

  const handleChangeRowsPerPage = (newLimit: number) => {
    setMeta({ ...meta, page: 1, limit: newLimit });
    updateUrl({ limit: newLimit, page: 1 });
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setMeta((prev) => ({ ...prev, page: 1 }));
    updateUrl({ search: value || null, page: 1 });
  };

  const hasActiveFilters = !!(searchQuery || dateRange?.from || dateRange?.to);

  const handleClearFilters = () => {
    setDateRange(undefined);
    setSearchQuery("");
    setMeta((prev) => ({ ...prev, page: 1 }));
    updateUrl({
      created_at_start: null,
      created_at_end: null,
      search: null,
      page: 1,
    });
    fetchProjects(1, meta.limit, false, null, null, null);
  };

  const handleNewProject = () => {
    setSelectedProject(null);
    setNewProjectDialogOpen(true);
  };

  return (
    <div className="min-h-screen relative">
      {/* Floating Header */}
      <FloatingHeader
        isVisible={showFloatingHeader && !initialLoading}
        searchQuery={searchQuery}
        dateRange={dateRange}
        onSearchChange={handleSearch}
        onDateRangeChange={(range) => {
          setDateRange(range);
          if (range?.from && range?.to) {
            setMeta((prev) => ({ ...prev, page: 1 }));
            updateUrl({
              created_at_start: format(range.from, "yyyy-MM-dd"),
              created_at_end: format(range.to, "yyyy-MM-dd"),
              page: 1,
            });
          } else if (!range?.from && !range?.to) {
            setMeta((prev) => ({ ...prev, page: 1 }));
            updateUrl({
              created_at_start: null,
              created_at_end: null,
              page: 1,
            });
          }
        }}
        onClearFilters={handleClearFilters}
        onNewProject={handleNewProject}
        hasActiveFilters={hasActiveFilters}
      />

      <ProjectDialog
        open={newProjectDialogOpen}
        setOpen={setNewProjectDialogOpen}
        selectedProject={selectedProject}
        setSelectedProject={setSelectedProject}
        onProjectCreated={handleProjectSaved}
      />

      <DeleteProjectDialog
        open={deleteProjectDialogOpen}
        setOpen={setDeleteProjectDialogOpen}
        selectedProject={selectedProject as any}
        setSelectedProject={setSelectedProject as any}
        onProjectDeleted={handleProjectSaved}
      />

      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between mb-8"
      >
        <div>
          <h1 className="text-4xl font-bold text-gray-900">Projects</h1>
          <p className="text-lg text-gray-600 mt-2">
            Manage and organize your projects
          </p>
        </div>
        <Button
          onClick={handleNewProject}
          startIcon={<PlusIcon className="w-4 h-4" />}
          className="px-4 py-2"
        >
          New Project
        </Button>
      </motion.div>

      {/* Filters Section */}
      {/* Removed old inline filter controls. Now handled by ProjectsFiltersBar above. */}
      <ProjectsFiltersBar
        searchQuery={searchQuery}
        dateRange={dateRange}
        onSearchChange={handleSearch}
        onDateRangeChange={(range) => {
          setDateRange(range);
          if (range?.from && range?.to) {
            setMeta((prev) => ({ ...prev, page: 1 }));
            updateUrl({
              created_at_start: format(range.from, "yyyy-MM-dd"),
              created_at_end: format(range.to, "yyyy-MM-dd"),
              page: 1,
            });
          } else if (!range?.from && !range?.to) {
            setMeta((prev) => ({ ...prev, page: 1 }));
            updateUrl({
              created_at_start: null,
              created_at_end: null,
              page: 1,
            });
          }
        }}
        onClearFilters={handleClearFilters}
        hasActiveFilters={hasActiveFilters}
      />

      {/* Projects Grid */}
      <div className="relative">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
        >
          {initialLoading || (loading && projects.length === 0) ? (
            Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className="animate-pulse rounded-md bg-white border border-gray-200 p-4"
              >
                <div className="flex items-start gap-3 mb-3">
                  <Skeleton className="h-10 w-10 rounded-md" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-3/4" />
                  </div>
                </div>
                <div className="flex items-center justify-between gap-4 mb-4">
                  <div className="flex items-center gap-2 flex-1">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
                <div className="flex gap-2 justify-end pt-2 border-t border-gray-100">
                  <Skeleton className="h-8 w-16 rounded" />
                  <Skeleton className="h-8 w-16 rounded" />
                </div>
              </div>
            ))
          ) : projects.length > 0 ? (
            projects.map((project) => (
              <ProjectCard
                project={project}
                key={project._id}
                onEdit={() => {
                  const baseProject: BaseProject = {
                    _id: project._id,
                    name: project.name,
                    description: project.description,
                    created_at: project.created_at,
                    created_by_username: undefined,
                  };
                  setSelectedProject(baseProject);
                  setNewProjectDialogOpen(true);
                }}
                onDelete={() => {
                  const baseProject: BaseProject = {
                    _id: project._id,
                    name: project.name,
                    description: project.description,
                    created_at: project.created_at,
                    created_by_username: undefined,
                  };
                  setSelectedProject(baseProject);
                  setDeleteProjectDialogOpen(true);
                }}
                onView={() => {
                  router.push(`/projects/${project._id}`);
                }}
              />
            ))
          ) : (
            <div className="col-span-full">
              {searchQuery || dateRange?.from || dateRange?.to ? (
                <EmptyState
                  icon={<Search className="h-12 w-12 text-gray-500" />}
                  title="No Projects Match Your Search"
                  description="We couldn't find any projects matching your current filters. Try adjusting your search criteria or clear the filters to see all projects."
                />
              ) : (
                <EmptyState
                  icon={<FolderOpen className="h-12 w-12 text-sky-600" />}
                  title="No Projects Found"
                  description="You haven't created any projects yet. Start by creating your first project to organize and manage your work efficiently."
                />
              )}
            </div>
          )}
        </motion.div>

        {/* Content Loading Overlay - only when there are existing projects */}
        {loading && !initialLoading && projects.length > 0 && (
          <div className="absolute inset-0 bg-white/75 flex justify-center items-center rounded-md">
            <div className="bg-white rounded-md p-4 shadow-lg">
              <LoaderCircle className="h-8 w-8 animate-spin text-sky-600" />
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {projects.length > 0 && (
        <div className="mt-4 flex justify-end">
          <Pagination
            count={meta.total}
            rowsPerPage={meta.limit}
            rowsPerPageOptions={[12, 24, 48, 120]}
            page={meta.page - 1}
            onPageChange={(newPage) => handleChangePage(newPage)}
            onRowsPerPageChange={(newLimit) =>
              handleChangeRowsPerPage(newLimit)
            }
          />
        </div>
      )}
    </div>
  );
}
