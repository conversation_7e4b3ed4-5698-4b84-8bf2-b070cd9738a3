import React from "react";
import {
  Select as UISelect,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  options: SelectOption[];
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  className?: string;
  clearable?: boolean;
  clearLabel?: string;
}

export default function Select({
  value,
  onValueChange,
  placeholder,
  options,
  error,
  helperText,
  disabled,
  className,
  clearable = false,
  clearLabel = "Clear selection",
}: SelectProps) {
  const allOptions = clearable
    ? [{ value: "CLEAR_SELECTION", label: clearLabel }, ...options]
    : options;

  const handleValueChange = (newValue: string) => {
    if (newValue === "CLEAR_SELECTION") {
      onValueChange?.("");
    } else {
      onValueChange?.(newValue);
    }
  };

  return (
    <div>
      <UISelect
        value={value}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger
          className={`w-full px-4 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200
            ${error ? "border-red-200 focus:ring-red-600" : "border-gray-200 focus:ring-sky-600"}
            ${disabled ? "opacity-50 cursor-not-allowed" : ""}
            ${className || ""}`}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {allOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </UISelect>
      {helperText && <p className="text-xs mt-1 text-red-500">{helperText}</p>}
    </div>
  );
}
