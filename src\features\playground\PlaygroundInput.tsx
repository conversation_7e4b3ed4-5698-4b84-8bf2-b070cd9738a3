import React, { useState, useRef, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Button from "@/components/custom/Button";
import JsonView from "@uiw/react-json-view";
import {
  Send,
  Paperclip,
  Volume2,
  Image as ImageIcon,
  FileText as FileTextIcon,
  X,
  Code,
  Edit3,
  Video,
} from "lucide-react";
// import { MediaDialog } from "@/components/media-dialog";

// Define attachment type clearly
export interface Attachment {
  id: string; // Use a unique id for keys
  type: "image" | "video" | "audio" | "document" | "other";
  name: string;
  url: string; // For preview
  file?: File; // The actual file object
}

interface PlaygroundInputProps {
  value: string;
  onChange: (value: string) => void;
  onProcess: (attachments: Attachment[]) => void;
  isProcessing: boolean;
  processingStep?: string;
  generatedSchema?: any;
  hasCompletedProcessing?: boolean;
  hasOutput?: boolean;
  shouldOpenInputDialog?: boolean;
  onInputDialogOpened?: () => void;
  onAttachmentsChange?: (attachments: Attachment[]) => void;
}

export const PlaygroundInput: React.FC<PlaygroundInputProps> = ({
  value,
  onChange,
  onProcess,
  isProcessing,
  processingStep,
  generatedSchema,
  hasCompletedProcessing,
  hasOutput = false,
  shouldOpenInputDialog = false,
  onInputDialogOpened,
  onAttachmentsChange,
}) => {
  // If attachments are managed internally:
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [showSchema, setShowSchema] = useState(false);
  const [showMediaDialog, setShowMediaDialog] = useState(false);
  const [mediaDialogItems, setMediaDialogItems] = useState<any[]>([]);
  const [activeMediaIndex, setActiveMediaIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        // Only take the first file for playground (single file at a time)
        const file = files[0];
        const fileType = file.type.split("/")[0];
        let type: Attachment["type"] = "other";
        if (fileType === "image") type = "image";
        else if (fileType === "video") type = "video";
        else if (fileType === "audio") type = "audio";
        else if (
          file.type === "application/pdf" ||
          file.type.startsWith("text/") ||
          file.type.includes("document")
        )
          type = "document";

        const newAttachment: Attachment = {
          id: crypto.randomUUID(), // Generate unique ID
          type,
          name: file.name,
          url: URL.createObjectURL(file),
          file,
        };

        // Replace any existing attachment with the new one
        const newAttachments = [newAttachment];
        setAttachments(newAttachments);
        onAttachmentsChange?.(newAttachments);
      }
      // Reset file input to allow uploading the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    []
  );

  const removeAttachment = useCallback(
    (id: string) => {
      setAttachments((prev) => {
        const attachmentToRemove = prev.find((att) => att.id === id);
        if (attachmentToRemove) {
          URL.revokeObjectURL(attachmentToRemove.url); // Clean up object URL
        }
        const newAttachments = prev.filter((att) => att.id !== id);
        onAttachmentsChange?.(newAttachments);
        return newAttachments;
      });
    },
    [onAttachmentsChange]
  );

  // Convert attachment to MediaDialog format
  const convertAttachmentToMediaDialog = useCallback(
    (attachment: Attachment) => {
      return {
        id: 0,
        type: attachment.type,
        title: attachment.name,
        url: attachment.url,
        thumbnail: attachment.url,
        product: {
          name: attachment.name,
          price: attachment.type || "Unknown type",
        },
      };
    },
    []
  );

  // Handle preview button click
  const handlePreviewFile = useCallback(
    (attachment: Attachment) => {
      const mediaItem = convertAttachmentToMediaDialog(attachment);
      setMediaDialogItems([mediaItem]);
      setActiveMediaIndex(0);
      setShowMediaDialog(true);
    },
    [convertAttachmentToMediaDialog]
  );

  // Handle media dialog change
  const handleMediaChange = useCallback((index: number) => {
    setActiveMediaIndex(index);
  }, []);

  // Auto-open input dialog when processing completes
  useEffect(() => {
    if (shouldOpenInputDialog) {
      if (attachments.length > 0) {
        // Add a small delay to ensure the UI has settled
        const timer = setTimeout(() => {
          // Open the media dialog for the first attachment
          const firstAttachment = attachments[0];
          const mediaItem = convertAttachmentToMediaDialog(firstAttachment);
          setMediaDialogItems([mediaItem]);
          setActiveMediaIndex(0);
          setShowMediaDialog(true);
        }, 100);

        // Cleanup timer
        return () => clearTimeout(timer);
      }

      // Notify parent that dialog has been opened (even if no attachments)
      onInputDialogOpened?.();
    }
  }, [
    shouldOpenInputDialog,
    attachments,
    convertAttachmentToMediaDialog,
    onInputDialogOpened,
  ]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Corrected event type
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      if (!isProcessing && value.trim() && attachments.length > 0) {
        onProcess(attachments);
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: 1,
        y: 0,
        height:
          showSchema && generatedSchema
            ? hasOutput
              ? "400px"
              : "500px"
            : hasOutput
            ? "140px"
            : "280px",
      }}
      transition={{
        duration: 0.5,
        ease: "easeOut",
        height: { duration: 0.4, ease: "easeInOut" },
      }}
      className="flex flex-col border border-gray-200 bg-white rounded-md shadow-sm overflow-hidden"
      style={{ boxShadow: "none" }}
    >
      <div className="flex-1 flex flex-col p-4 overflow-hidden">
        <AnimatePresence mode="wait">
          {showSchema && generatedSchema ? (
            // Schema View with Error Boundary
            <motion.div
              key="schema-view"
              initial={{ opacity: 0, x: 20, scale: 0.95 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: -20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="flex-1 overflow-hidden"
            >
              <motion.div
                initial={{ y: 10 }}
                animate={{ y: 0 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="h-full overflow-y-auto"
              >
                {(() => {
                  try {
                    // Validate schema data before rendering
                    if (
                      !generatedSchema ||
                      typeof generatedSchema !== "object"
                    ) {
                      return (
                        <motion.div
                          initial={{ scale: 0.9, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ delay: 0.2, duration: 0.3 }}
                          className="p-4 bg-red-50 border border-red-200 rounded-md"
                        >
                          <div className="flex items-center gap-2 text-red-700">
                            <X className="w-4 h-4" />
                            <span className="text-sm font-medium">
                              Invalid Schema Data
                            </span>
                          </div>
                          <p className="text-red-600 text-sm mt-1">
                            The generated schema data is not in a valid format.
                          </p>
                        </motion.div>
                      );
                    }

                    return (
                      <motion.div
                        initial={{ scale: 0.98, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.2, duration: 0.4 }}
                      >
                        <JsonView
                          value={generatedSchema}
                          style={{
                            backgroundColor: "#ffffff",
                            fontSize: "14px",
                          }}
                          displayDataTypes={false}
                          displayObjectSize={false}
                          enableClipboard={false}
                          // collapsed={1}
                        />
                      </motion.div>
                    );
                  } catch (error) {
                    console.error("Error rendering schema:", error);
                    return (
                      <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.2, duration: 0.3 }}
                        className="p-4 bg-red-50 border border-red-200 rounded-md"
                      >
                        <div className="flex items-center gap-2 text-red-700">
                          <X className="w-4 h-4" />
                          <span className="text-sm font-medium">
                            Schema Display Error
                          </span>
                        </div>
                        <p className="text-red-600 text-sm mt-1">
                          Unable to display the schema. The data may be
                          corrupted or in an unexpected format.
                        </p>
                        {/* <details className="mt-2">
                          <summary className="text-xs text-red-500 cursor-pointer">
                            Show raw data
                          </summary>
                          <pre className="text-xs text-red-600 mt-1 p-2 bg-red-100 rounded overflow-auto max-h-32">
                            {JSON.stringify(generatedSchema, null, 2)}
                          </pre>
                        </details> */}
                      </motion.div>
                    );
                  }
                })()}
              </motion.div>
            </motion.div>
          ) : (
            // Text Input View
            <motion.div
              key="text-input"
              initial={{ opacity: 0, x: -20, scale: 0.95 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="flex-1"
            >
              <motion.textarea
                initial={{ y: 10 }}
                animate={{ y: 0 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter your prompt describing what you want to extract from the uploaded file. Add a file and enter your prompt to generate results. (Ctrl+Enter or Cmd+Enter to submit)"
                className={`${
                  hasOutput
                    ? "min-h-[80px] max-h-[120px]"
                    : "min-h-[180px] max-h-[300px]"
                } w-full resize-none border-none focus:outline-none text-base leading-relaxed placeholder:text-gray-400 bg-transparent transition-all duration-300`}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Action Bar */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
        className="flex items-center justify-between p-4 border-t border-gray-100 bg-gradient-to-r from-gray-50/80 to-gray-50/60 rounded-b-md"
      >
        <div className="flex gap-2">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            onChange={handleFileUpload}
            className="hidden"
          />
          <AnimatePresence mode="wait">
            {attachments.length === 0 ? (
              <motion.div
                key="add-file"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                  startIcon={
                    <motion.div
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatDelay: 3,
                        ease: "easeInOut",
                      }}
                    >
                      <Paperclip className="w-4 h-4" />
                    </motion.div>
                  }
                  className="focus:ring-0 focus:ring-offset-0 shadow-none"
                  style={{ boxShadow: "none" }}
                >
                  Add File
                </Button>
              </motion.div>
            ) : (
              <motion.div
                key="file-attached"
                initial={{ opacity: 0, scale: 0.9, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.9, x: 20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="flex items-center gap-2"
              >
                <motion.button
                  onClick={() => handlePreviewFile(attachments[0])}
                  disabled={isProcessing}
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-md border border-gray-200 hover:border-sky-500 hover:bg-gradient-to-r hover:from-sky-500/5 hover:to-sky-600/5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.1, duration: 0.3, ease: "easeOut" }}
                    className="p-0.5 rounded-md bg-gradient-to-br from-sky-500/10 to-sky-600/10"
                  >
                    {attachments[0].type === "image" ? (
                      <ImageIcon className="w-3 h-3 text-sky-600" />
                    ) : attachments[0].type === "video" ? (
                      <Video className="w-3 h-3 text-sky-600" />
                    ) : attachments[0].type === "audio" ? (
                      <Volume2 className="w-3 h-3 text-sky-600" />
                    ) : (
                      <FileTextIcon className="w-3 h-3 text-sky-600" />
                    )}
                  </motion.div>
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                    className="text-sm font-medium text-gray-900 max-w-[200px] truncate"
                  >
                    {attachments[0].name}
                  </motion.span>
                  {!isProcessing && (
                    <motion.button
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3, duration: 0.2 }}
                      whileHover={{ scale: 1.2, rotate: 90 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        removeAttachment(attachments[0].id);
                      }}
                      className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-all duration-200"
                      title="Remove file"
                    >
                      <X className="w-4 h-4" />
                    </motion.button>
                  )}
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <div className="flex gap-2">
          {generatedSchema && hasCompletedProcessing && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.4, ease: "easeOut" }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="outline"
                // size="sm"
                onClick={() => setShowSchema(!showSchema)}
                startIcon={
                  <motion.div
                    animate={{
                      rotate: showSchema ? 180 : 0,
                      scale: showSchema ? 1.1 : 1,
                    }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  >
                    {showSchema ? (
                      <Edit3 className="w-4 h-4" />
                    ) : (
                      <Code className="w-4 h-4" />
                    )}
                  </motion.div>
                }
                className="focus:ring-0 focus:ring-offset-0 shadow-none"
                style={{ boxShadow: "none" }}
              >
                <motion.span
                  key={showSchema ? "hide" : "view"}
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {showSchema ? "Hide Schema" : "View Schema"}
                </motion.span>
              </Button>
            </motion.div>
          )}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              onClick={() => onProcess(attachments)}
              disabled={
                !value.trim() || attachments.length === 0 || isProcessing
              }
              loading={isProcessing}
              startIcon={
                !isProcessing ? <Send className="w-4 h-4" /> : undefined
              }
              variant="default"
              className="bg-gradient-to-r from-sky-600 to-sky-700 hover:from-sky-700 hover:to-sky-800 text-white border-none focus:ring-0 focus:ring-offset-0 shadow-none"
              style={{ boxShadow: "none" }}
            >
              {isProcessing ? processingStep || "Processing..." : "Process"}
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Media Dialog */}
      {/* {showMediaDialog && mediaDialogItems.length > 0 && (
        <MediaDialog
          mediaItems={mediaDialogItems}
          activeIndex={activeMediaIndex}
          onMediaChange={handleMediaChange}
          onClose={() => setShowMediaDialog(false)}
          showNavigation={false}
          defaultMinimized={false}
        />
      )} */}
    </motion.div>
  );
};
