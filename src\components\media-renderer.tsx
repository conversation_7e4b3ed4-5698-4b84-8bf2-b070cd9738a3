"use client";

import { useState, useRef, useEffect } from "react";
import {
  Play,
  Pause,
  FileText,
  Download,
  ImageIcon,
  Video,
  Music,
  File,
  ZoomIn,
  ZoomOut,
  RotateCcw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

interface MediaItem {
  id: number;
  type: "image" | "video" | "audio" | "document";
  title: string;
  url: string;
  thumbnail: string;
  product: any;
}

interface MediaRendererProps {
  mediaItem: MediaItem;
  isMinimized: boolean;
  className?: string;
  containerStyle?: React.CSSProperties;
}

export function MediaRenderer({
  mediaItem,
  isMinimized,
  className = "",
  containerStyle = {},
}: MediaRendererProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Image zoom and pan state
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const imageContainerRef = useRef<HTMLDivElement>(null);

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Image zoom and pan handlers
  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev * 1.2, 5));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev / 1.2, 0.1));
  };

  const handleResetZoom = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom((prev) => Math.min(Math.max(prev * delta, 0.1), 5));
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Touch event handlers for mobile support
  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 1 && zoom > 1) {
      setIsDragging(true);
      const touch = e.touches[0];
      setDragStart({ x: touch.clientX - pan.x, y: touch.clientY - pan.y });
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (e.touches.length === 1 && isDragging && zoom > 1) {
      e.preventDefault();
      const touch = e.touches[0];
      setPan({
        x: touch.clientX - dragStart.x,
        y: touch.clientY - dragStart.y,
      });
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // Reset zoom and pan when media item changes
  useEffect(() => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  }, [mediaItem.id]);

  // Listen to audio events to sync state
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [mediaItem.url]);

  // Helper function to determine if a document can be previewed
  const canPreviewDocument = (url: string, title: string) => {
    const extension = title.split(".").pop()?.toLowerCase();
    const isPdf = extension === "pdf" || url.includes(".pdf");
    return isPdf;
  };

  // Helper function to handle file download
  const handleDownload = () => {
    if (mediaItem.url) {
      const link = document.createElement("a");
      link.href = mediaItem.url;
      link.download = mediaItem.title;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (isMinimized) {
    switch (mediaItem.type) {
      case "image":
        return (
          <div
            className={`relative rounded-md overflow-hidden bg-gray-100 ${className}`}
          >
            <Image
              src={mediaItem.thumbnail || "/placeholder.svg"}
              alt={mediaItem.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black/20">
              <ImageIcon className="w-6 h-6 text-white" />
            </div>
          </div>
        );

      case "video":
        return (
          <div
            className={`relative rounded-md overflow-hidden bg-gray-900 ${className}`}
          >
            <Image
              src={mediaItem.thumbnail || "/placeholder.svg"}
              alt={mediaItem.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-black/50 rounded-full p-2">
                <Video className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        );

      case "audio":
        return (
          <div
            className={`flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-500 rounded-md ${className}`}
          >
            <Button
              size="sm"
              variant="ghost"
              onClick={handlePlayPause}
              className="text-white hover:bg-white/20 p-1"
            >
              {isPlaying ? (
                <Pause className="w-3 h-3" />
              ) : (
                <Play className="w-3 h-3" />
              )}
            </Button>
            {/* Hidden audio element for actual playback */}
            <audio ref={audioRef} key={mediaItem.url} className="hidden">
              <source src={mediaItem.url} type="audio/mpeg" />
              <source src={mediaItem.url} type="audio/wav" />
              <source src={mediaItem.url} type="audio/ogg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        );

      case "document":
        return (
          <div
            className={`flex items-center justify-center bg-blue-500 rounded-md ${className}`}
          >
            <FileText className="w-4 h-4 text-white" />
          </div>
        );

      default:
        return <div className={`bg-gray-200 rounded-md ${className}`} />;
    }
  }

  // Maximized view - Full file preview
  switch (mediaItem.type) {
    case "image":
      return (
        <div
          className={`relative rounded-md overflow-hidden bg-gray-100 ${className}`}
          style={containerStyle}
        >
          {/* Zoom Controls */}
          <div className="absolute top-2 right-2 z-10 flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={handleZoomIn}
              className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleZoomOut}
              className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleResetZoom}
              className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
              title="Reset Zoom"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>

          {/* Zoom Level Indicator */}
          {zoom !== 1 && (
            <div className="absolute top-2 left-2 z-10 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {Math.round(zoom * 100)}%
            </div>
          )}

          {/* Image Container */}
          <div
            ref={imageContainerRef}
            className="w-full h-full flex items-center justify-center overflow-hidden"
            onWheel={handleWheel}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{
              cursor: zoom > 1 ? (isDragging ? "grabbing" : "grab") : "default",
              touchAction: zoom > 1 ? "none" : "auto",
              minHeight: '200px',
              height: '100%'
            }}
          >
            <Image
              src={mediaItem.url || "/placeholder.svg"}
              alt={mediaItem.title}
              fill
              className="object-contain transition-transform duration-200"
              style={{
                transform: `scale(${zoom}) translate(${pan.x / zoom}px, ${pan.y / zoom}px)`,
                maxWidth: 'none',
                maxHeight: 'none',
              }}
              draggable={false}
            />
          </div>
        </div>
      );

    case "video":
      return (
        <div 
          className={`rounded-md overflow-hidden bg-black ${className}`}
          style={containerStyle}
        >
          <video
            key={mediaItem.url}
            controls
            className="w-full h-full object-contain"
            poster={mediaItem.thumbnail}
            style={{ 
              minHeight: '200px',
              height: '100%'
            }}
          >
            <source src={mediaItem.url} type="video/mp4" />
            <source src={mediaItem.url} type="video/webm" />
            <source src={mediaItem.url} type="video/ogg" />
            Your browser does not support the video tag.
          </video>
        </div>
      );

    case "audio":
      return (
        <div
          className={`flex flex-col items-center justify-center bg-gradient-to-br rounded-md ${className}`}
          style={containerStyle}
        >
          <div className="flex flex-col items-center space-y-0 w-full h-full justify-center">
            <audio
              ref={audioRef}
              key={mediaItem.url}
              controls
              className="w-full max-w-2xl"
            >
              <source src={mediaItem.url} type="audio/mpeg" />
              <source src={mediaItem.url} type="audio/wav" />
              <source src={mediaItem.url} type="audio/ogg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>
      );

    case "document":
      const canPreview = canPreviewDocument(mediaItem.url, mediaItem.title);

      if (canPreview) {
        return (
          <div 
            className={`rounded-md overflow-hidden bg-white ${className}`}
            style={containerStyle}
          >
            <iframe
              src={mediaItem.url}
              className="w-full h-full border-0"
              title={mediaItem.title}
              style={{ 
                minHeight: '200px',
                height: '100%'
              }}
            />
          </div>
        );
      }

      // Non-previewable document
      return (
        <div
          className={`flex flex-col items-center justify-center bg-blue-50 rounded-md p-6 ${className}`}
          style={containerStyle}
        >
          <div className="flex flex-col items-center space-y-4 max-w-sm text-center">
            <File className="w-16 h-16 text-blue-500" />
            <div>
              <p className="text-lg font-medium text-gray-900 mb-1">
                {mediaItem.title}
              </p>
              <p className="text-sm text-gray-600 mb-4">
                This file type cannot be previewed
              </p>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={handleDownload}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Download File
            </Button>
          </div>
        </div>
      );

    default:
      // Unknown file type
      return (
        <div
          className={`flex flex-col items-center justify-center bg-gray-50 rounded-md p-6 ${className}`}
          style={containerStyle}
        >
          <div className="flex flex-col items-center space-y-4 max-w-sm text-center">
            <File className="w-16 h-16 text-gray-500" />
            <div>
              <p className="text-lg font-medium text-gray-900 mb-1">
                {mediaItem.title}
              </p>
              <p className="text-sm text-gray-600 mb-4">
                Unknown file type - cannot be previewed
              </p>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={handleDownload}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Download File
            </Button>
          </div>
        </div>
      );
  }
}
