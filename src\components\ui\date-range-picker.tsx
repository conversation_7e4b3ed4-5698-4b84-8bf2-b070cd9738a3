"use client";

import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DateRangePickerProps {
  className?: string;
  date?: DateRange;
  onDateChange?: (date: DateRange | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  numberOfMonths?: number;
  align?: "start" | "center" | "end";
  disableFuture?: boolean;
  rightButton?: React.ReactNode;
}

export function DateRangePicker({
  className,
  date,
  onDateChange,
  placeholder = "Pick a date range",
  disabled = false,
  numberOfMonths = 2,
  align = "start",
  disableFuture = false,
  rightButton,
}: DateRangePickerProps) {
  const [open, setOpen] = React.useState(false);

  // Get today's date for comparison
  const today = new Date();
  today.setHours(23, 59, 59, 999); // Set to end of day to include today;

  return (
    <div className={cn("grid gap-2 relative", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal bg-white border-gray-300 hover:bg-gray-100",
              !date && "text-gray-400",
              disabled && "opacity-50 cursor-not-allowed",
              rightButton && "pr-12" // Add padding when rightButton is present
            )}
            disabled={disabled}
          >
            <CalendarIcon
              className={cn(
                "mr-2 h-4 w-4",
                !date ? "text-gray-400" : "text-gray-600"
              )}
            />
            {date?.from ? (
              date.to ? (
                <span className="text-gray-900">
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </span>
              ) : (
                <span className="text-gray-900">
                  {format(date.from, "LLL dd, y")}
                </span>
              )
            ) : (
              <span className="text-gray-400">{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align={align}>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(selectedDate) => {
              onDateChange?.(selectedDate);
              // Close popover when both dates are selected
              if (selectedDate?.from && selectedDate?.to) {
                setOpen(false);
              }
            }}
            numberOfMonths={numberOfMonths}
            disabled={disableFuture ? (date) => date > today : undefined}
          />
        </PopoverContent>
      </Popover>

      {/* Right Button - positioned inside the date picker */}
      {rightButton && (
        <div className="absolute right-0 top-0 h-full flex items-center">
          <div className="h-6 w-px bg-gray-300 mx-2"></div>
          {rightButton}
        </div>
      )}
    </div>
  );
}
