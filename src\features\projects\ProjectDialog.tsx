import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plus, SaveIcon, RefreshCw } from "lucide-react";
import TextField from "@/components/custom/TextField";
import Button from "@/components/custom/Button";
import FieldTitle from "@/components/custom/FieldTitle";
import {
  CreateProjectService,
  UpdateProjectService,
} from "@/services/projects.service";
import { snackbar } from "@/utils/snackbar.util";
import { BaseProject } from "@/types/project.types";
import { generateRandomDescription } from "@/utils/generatedescription.util";
import { generateRandomProjectName } from "@/utils/generateRandomName.util";

type ProjectDialogProps = {
  open: boolean;
  setOpen: (val: boolean) => void;
  selectedProject?: BaseProject | null;
  setSelectedProject?: (val: BaseProject | null) => void;
  onProjectCreated?: (projectData?: any) => void;
};

export default function ProjectDialog({
  open,
  setOpen,
  selectedProject,
  setSelectedProject,
  onProjectCreated,
}: ProjectDialogProps) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [nameHelperText, setNameHelperText] = useState("");
  const [descriptionHelperText, setDescriptionHelperText] = useState("");

  // Function to regenerate random project name
  const handleRegenerateRandomName = () => {
    if (!selectedProject) {
      // Only allow regeneration for new projects
      setName(generateRandomProjectName());
      setNameHelperText("");
    }
  };

  // Function to regenerate random project name
  const handleRegenerateRandomDescription = () => {
    if (!selectedProject) {
      // Only allow regeneration for new projects
      setDescription(generateRandomDescription());
      setDescriptionHelperText("");
    }
  };

  useEffect(() => {
    if (open && selectedProject) {
      // Editing existing project - populate with existing data
      setName(selectedProject.name);
      setDescription(selectedProject.description || "");
      setNameHelperText("");
      setDescriptionHelperText("");
    } else if (open && !selectedProject) {
      // Creating new project - auto-fill with random name
      setName(generateRandomProjectName());
      setDescription("");
      setNameHelperText("");
      setDescriptionHelperText("");
    }
  }, [selectedProject, open]);

  const handleClose = () => {
    setOpen(false);
    setName("");
    setDescription("");
    setNameHelperText("");
    setDescriptionHelperText("");
    setLoading(false);
    setSelectedProject?.(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset error messages
    setNameHelperText("");
    setDescriptionHelperText("");

    // Validate required fields
    let hasErrors = false;

    if (!name.trim()) {
      setNameHelperText("Enter project name");
      hasErrors = true;
    }

    if (!description.trim()) {
      setDescriptionHelperText("Enter project description");
      hasErrors = true;
    }

    if (hasErrors) {
      return;
    }

    setLoading(true);

    const response = selectedProject
      ? await UpdateProjectService(selectedProject._id, name, description)
      : await CreateProjectService(name, description);

    if (response.success) {
      snackbar.showSuccessMessage(
        `Project ${selectedProject ? "updated" : "created"} successfully`
      );
      handleClose();
      onProjectCreated?.(response.data);
    } else {
      setLoading(false);
      setNameHelperText(response.data?.message || "Something went wrong");
      snackbar.showErrorMessage(response.data);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {selectedProject ? "Update Project" : "New Project"}
          </DialogTitle>
          <DialogDescription>
            Fill in the details below to {selectedProject ? "update" : "create"}{" "}
            your project.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <div className="mb-2">
              <FieldTitle title="Name" required />
            </div>
            <TextField
              autoFocus
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                setNameHelperText("");
              }}
              placeholder="Enter project name"
              error={!!nameHelperText}
              helperText={nameHelperText}
              rightButton={
                !selectedProject ? (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRegenerateRandomName}
                    className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                    startIcon={<RefreshCw className="w-3 h-3" />}
                  ></Button>
                ) : null
              }
            />
          </div>

          <div>
            <div className="mb-2">
              <FieldTitle title="Description" required />
            </div>
            <TextField
              multiline
              value={description}
              onChange={(e) => {
                setDescription(e.target.value);
                setDescriptionHelperText("");
              }}
              placeholder="Enter project description"
              rows={4}
              error={!!descriptionHelperText}
              helperText={descriptionHelperText}
              rightButton={
                !selectedProject ? (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRegenerateRandomDescription}
                    className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                    startIcon={<RefreshCw className="w-3 h-3" />}
                  ></Button>
                ) : null
              }
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="submit"
              disabled={loading}
              loading={loading}
              startIcon={
                selectedProject ? (
                  <SaveIcon className="w-4 h-4" />
                ) : (
                  <Plus className="w-4 h-4" />
                )
              }
            >
              {selectedProject ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
