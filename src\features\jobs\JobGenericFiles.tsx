"use client";

import { useEffect, useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { Badge } from "@/components/ui/badge";
import { FileText, Edit3, Save, X, FileDown, Check } from "lucide-react";
import {
  VerifyJobOutputService,
  EditJobOutputService,
  GetJobItemsService,
} from "@/services/jobs.service";
import {
  DeleteMediaService,
  GetInputInputMediaService,
  GetJobOutputMediaService,
} from "@/services/media.service";
import { snackbar } from "@/utils/snackbar.util";
import { JobGenericFile } from "./JobGenericFile";
import Button from "@/components/custom/Button";
import RemarkDialog from "@/components/custom/RemarkDialog";
import ConfirmationDialog from "@/components/custom/ConfirmationDialog";
import { MediaDialog } from "@/components/media-dialog";
import {
  LoadingAnalysisState,
  PendingAnalysisState,
  NoMediaFilesState,
  AnalysisErrorState,
} from "./index";

type JobGenericFilesProps = {
  jobId: string;
  onAddMediaClick: () => void;
  processName?: string;
};

export interface JobGenericFilesRef {
  refreshMediaList: () => void;
}

type MediaInfo = {
  _id: string;
  filename: string;
  description: string | null;
  content_type: string;
  object_name: string;
  presigned_url?: string;
  uploaded_at: string;
};

type OutputResult = {
  status: string;
  data: Record<string, any>;
  price_nep: number;
  usage_metadata: {
    prompt_token_count: number;
    candidates_token_count: number;
  };
};

type JobOutput = {
  _id: string;
  output_id?: string;
  source_media_id: string;
  object_name: string;
  process_type: string;
  result: OutputResult;
  presigned_url: string;
  verified?: boolean;
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  metadata?: {
    verification?: {
      verified: boolean;
      verification_notes: string;
      verified_at?: string;
      verified_by?: string;
    };
    [key: string]: any;
  };
};

const JobGenericFiles = forwardRef<JobGenericFilesRef, JobGenericFilesProps>(
  ({ jobId, onAddMediaClick }, ref) => {
    const [jobInputMediaItemsData, setJobInputMediaItemsData] = useState<
      MediaInfo[]
    >([]);
    const [selectedIndex, setSelectedIndex] = useState<number>(0);
    const [currentItemOutput, setCurrentItemOutput] =
      useState<JobOutput | null>(null);
    const [isLoadingMediaList, setIsLoadingMediaList] = useState(true);
    const [isLoadingOutputs, setIsLoadingOutputs] = useState(true);

    // Media dialog state
    const [showMediaDialog, setShowMediaDialog] = useState(false);
    const [mediaDialogItems, setMediaDialogItems] = useState<any[]>([]);
    const [activeMediaIndex, setActiveMediaIndex] = useState(0);

    // Editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editedResult, setEditedResult] = useState<any>(null);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Verification state
    const [remarkDialogOpen, setRemarkDialogOpen] = useState(false);
    const [verificationAction, setVerificationAction] = useState<
      "verify" | "unverify"
    >("verify");
    const [isVerifying, setIsVerifying] = useState(false);

    // Download state
    const [isDownloading, setIsDownloading] = useState(false);

    // Delete state
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [mediaToDelete, setMediaToDelete] = useState<MediaInfo | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // Expose refresh function to parent component
    useImperativeHandle(ref, () => ({
      refreshMediaList: () => {
        if (jobId) {
          setIsLoadingMediaList(true);
          setIsLoadingOutputs(true);
          loadJobMediaItems();
          loadJobOutputs();
        }
      },
    }));

    const loadJobMediaItems = async () => {
      if (!jobId) {
        setIsLoadingMediaList(false);
        return;
      }

      try {
        setIsLoadingMediaList(true);

        // Step 1: Get job items to get input_id and output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error("Failed to load job items:", jobItemsResponse.data);
          snackbar.showErrorMessage("Failed to load job items");
          setJobInputMediaItemsData([]);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setJobInputMediaItemsData([]);
          return;
        }

        // Step 2: Get input media details using input_id from job items
        const inputMediaPromises = jobItems.map(async (item: any) => {
          if (!item.input_id) return null;

          try {
            const mediaData = await GetInputInputMediaService(item.input_id);
            if (mediaData) {
              return {
                ...mediaData,
                status: item.status, // Add status from job item
              };
            }
            return null;
          } catch (error) {
            console.error(
              `Failed to load input media ${item.input_id}:`,
              error
            );
            return null;
          }
        });

        const inputMediaResults = await Promise.all(inputMediaPromises);
        const validInputMedia = inputMediaResults.filter(
          (media) => media !== null
        );

        setJobInputMediaItemsData(validInputMedia);
        if (validInputMedia.length > 0) {
          setSelectedIndex(0);
        }
      } catch (error) {
        console.error("Error loading media items:", error);
        snackbar.showErrorMessage("Error loading media files");
        setJobInputMediaItemsData([]);
      } finally {
        setIsLoadingMediaList(false);
      }
    };

    const loadJobOutputs = async () => {
      if (!jobId || jobInputMediaItemsData.length === 0) {
        setIsLoadingOutputs(false);
        return;
      }

      setIsLoadingOutputs(true);
      try {
        const selectedMedia = jobInputMediaItemsData[selectedIndex];
        if (!selectedMedia) return;

        // Step 1: Get job items to get output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error(
            "Failed to load job items for outputs:",
            jobItemsResponse.data
          );
          snackbar.showErrorMessage("Failed to load job items.");
          setCurrentItemOutput(null);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setCurrentItemOutput(null);
          return;
        }

        // Step 2: Find the job item that corresponds to the selected input media
        const jobItem = jobItems.find(
          (item: any) => item.input_id === selectedMedia._id
        );
        if (!jobItem || !jobItem.output_id) {
          setCurrentItemOutput(null);
          return;
        }

        // Step 3: Get output media details using output_id
        try {
          const outputData = await GetJobOutputMediaService(jobItem.output_id);
          if (outputData) {
            // Handle verification status from metadata if available
            if (outputData.metadata?.verification) {
              outputData.verified =
                outputData.metadata.verification.verified || false;
              outputData.verification_notes =
                outputData.metadata.verification.verification_notes || "";
              outputData.verified_at =
                outputData.metadata.verification.verified_at;
              outputData.verified_by =
                outputData.metadata.verification.verified_by;
            }

            setCurrentItemOutput(outputData);
          } else {
            setCurrentItemOutput(null);
          }
        } catch (error) {
          console.error(
            `Failed to load output media ${jobItem.output_id}:`,
            error
          );
          setCurrentItemOutput(null);
        }
      } catch (error) {
        console.error("Error loading job outputs:", error);
        setCurrentItemOutput(null);
      } finally {
        setIsLoadingOutputs(false);
      }
    };

    // Load media items on component mount
    useEffect(() => {
      if (jobId) {
        loadJobMediaItems();
      }
    }, [jobId]);

    // Load outputs when selectedIndex or media items change
    useEffect(() => {
      if (jobId && jobInputMediaItemsData.length > 0) {
        loadJobOutputs();
      }
    }, [selectedIndex, jobId, jobInputMediaItemsData]);

    // Auto-open media dialog when media items are loaded and update dialog items when media changes
    useEffect(() => {
      if (!isLoadingMediaList && jobInputMediaItemsData.length > 0) {
        const updateMediaDialog = async () => {
          const convertedItems = await convertToMediaDialogFormat(
            jobInputMediaItemsData
          );
          setMediaDialogItems(convertedItems);

          // Open dialog if not already open
          if (!showMediaDialog) {
            setShowMediaDialog(true);
          }
        };
        updateMediaDialog();
      } else if (jobInputMediaItemsData.length === 0 && showMediaDialog) {
        // Close dialog if no media items
        setShowMediaDialog(false);
        setMediaDialogItems([]);
      }
    }, [
      isLoadingMediaList,
      jobInputMediaItemsData.length,
      jobInputMediaItemsData,
    ]);

    // Editing handlers
    const handleStartEdit = () => {
      if (!currentItemOutput?.result) return;
      setIsEditing(true);
      setEditedResult(currentItemOutput.result);
      setHasUnsavedChanges(false);
    };

    const handleCancelEdit = () => {
      setIsEditing(false);
      setEditedResult(null);
      setHasUnsavedChanges(false);
    };

    const handleSaveResult = async () => {
      if (!editedResult || !currentItemOutput || !jobId) return;

      setIsSaving(true);
      try {
        const response = await EditJobOutputService(
          jobId,
          currentItemOutput._id,
          editedResult
        );
        if (response.success) {
          snackbar.showSuccessMessage("Changes saved successfully!");
          setHasUnsavedChanges(false);
          setIsEditing(false);
          setCurrentItemOutput({
            ...currentItemOutput,
            result: editedResult,
          });
        } else {
          snackbar.showErrorMessage(response.data || "Failed to save changes");
        }
      } catch (error) {
        snackbar.showErrorMessage("Failed to save changes");
      } finally {
        setIsSaving(false);
      }
    };

    // Verification handlers
    const handleVerify = () => {
      setVerificationAction("verify");
      setRemarkDialogOpen(true);
    };

    const handleUnverify = () => {
      setVerificationAction("unverify");
      setRemarkDialogOpen(true);
    };

    const handleVerificationConfirm = async (remark: string) => {
      if (!currentItemOutput || !jobId) return;

      setIsVerifying(true);
      try {
        const isVerifying = verificationAction === "verify";
        const response = await VerifyJobOutputService(
          jobId,
          currentItemOutput._id,
          isVerifying,
          remark
        );

        if (response.success) {
          const action = isVerifying ? "verified" : "unverified";
          snackbar.showSuccessMessage(`Output ${action} successfully!`);

          // Update the current output with new verification status
          const updatedOutput = {
            ...currentItemOutput,
            verified: isVerifying,
            verification_notes: remark,
            verified_at: isVerifying ? new Date().toISOString() : undefined,
          };

          // Also update the metadata.verification field
          if (updatedOutput.metadata) {
            updatedOutput.metadata.verification = {
              verified: isVerifying,
              verification_notes: remark,
              verified_at: isVerifying ? new Date().toISOString() : undefined,
              verified_by: "current_user", // You might want to get this from user context
            };
          }

          setCurrentItemOutput(updatedOutput);
          setRemarkDialogOpen(false);
        } else {
          snackbar.showErrorMessage(
            response.data || `Failed to ${verificationAction} output`
          );
        }
      } catch (error) {
        snackbar.showErrorMessage(`Failed to ${verificationAction} output`);
      } finally {
        setIsVerifying(false);
      }
    };

    // Download handler
    const handleDownload = async () => {
      if (!currentItemOutput?.result) return;

      setIsDownloading(true);
      try {
        const filename = `output_${
          jobInputMediaItemsData[selectedIndex]?.filename || "result"
        }.json`;
        const jsonData = JSON.stringify(currentItemOutput.result, null, 2);
        const blob = new Blob([jsonData], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        snackbar.showSuccessMessage("JSON download started!");
      } catch (error) {
        snackbar.showErrorMessage("Failed to download result");
      } finally {
        setIsDownloading(false);
      }
    };

    // Delete media handler
    const handleDeleteMedia = async () => {
      if (!mediaToDelete?._id) return;

      setIsDeleting(true);
      try {
        const result = await DeleteMediaService(jobId, mediaToDelete._id);
        if (result.success) {
          snackbar.showSuccessMessage("Media deleted successfully!");

          // Find the index of the deleted media
          const deletedIndex = jobInputMediaItemsData.findIndex(
            (media) => media._id === mediaToDelete._id
          );

          // Update the selected index to show the next available media
          if (deletedIndex !== -1) {
            const newMediaCount = jobInputMediaItemsData.length - 1;
            if (newMediaCount === 0) {
              // No media left
              setSelectedIndex(0);
            } else if (deletedIndex >= newMediaCount) {
              // If we deleted the last item, select the previous one
              setSelectedIndex(newMediaCount - 1);
            } else {
              // Select the same index (which will now show the next item)
              setSelectedIndex(deletedIndex);
            }
          }

          setShowDeleteConfirm(false);
          setMediaToDelete(null);
          // Refresh media list and outputs
          setIsLoadingMediaList(true);
          loadJobMediaItems();
          loadJobOutputs();
        } else {
          snackbar.showErrorMessage(result.data || "Failed to delete media");
        }
      } catch (error) {
        console.error("Failed to delete media:", error);
        snackbar.showErrorMessage("Failed to delete media");
      } finally {
        setIsDeleting(false);
      }
    };

    // Convert media items to media dialog format
    const convertToMediaDialogFormat = async (mediaItems: MediaInfo[]) => {
      const convertedItems = mediaItems.map((media, index) => {
        const type = media.content_type?.startsWith("image/")
          ? "image"
          : media.content_type?.startsWith("audio/")
          ? "audio"
          : "file";
        const url = media.presigned_url || "";

        return {
          id: index,
          type,
          title: media.filename,
          url: url,
          thumbnail: url,
          product: {
            name: media.filename,
            price: media.content_type || "Unknown type",
          },
          mediaData: media, // Store original media data
        };
      });
      return convertedItems;
    };

    // Handle media dialog actions
    const handleMediaChange = (index: number) => {
      setActiveMediaIndex(index);
      setSelectedIndex(index);
    };

    // Handle media deletion
    const handleMediaDeleted = async (mediaId: string) => {
      try {
        const result = await DeleteMediaService(jobId, mediaId);
        if (result.success) {
          snackbar.showSuccessMessage("Media deleted successfully!");

          // Find the index of the deleted media
          const deletedIndex = jobInputMediaItemsData.findIndex(
            (media) => media._id === mediaId
          );

          // Update the selected index to show the next available media
          if (deletedIndex !== -1) {
            const newMediaCount = jobInputMediaItemsData.length - 1;
            if (newMediaCount === 0) {
              // No media left
              setSelectedIndex(0);
              setShowMediaDialog(false);
            } else if (deletedIndex >= newMediaCount) {
              // If we deleted the last item, select the previous one
              setSelectedIndex(newMediaCount - 1);
              setActiveMediaIndex(newMediaCount - 1);
            } else {
              // Select the same index (which will now show the next item)
              setSelectedIndex(deletedIndex);
              setActiveMediaIndex(deletedIndex);
            }
          }

          // Refresh media list and outputs
          setIsLoadingMediaList(true);
          setIsLoadingOutputs(true);
          loadJobMediaItems();
          loadJobOutputs();
        } else {
          snackbar.showErrorMessage(result.data || "Failed to delete media");
        }
      } catch (error) {
        console.error("Failed to delete media:", error);
        snackbar.showErrorMessage("Failed to delete media");
      }
    };

    // Handle retry analysis
    const handleRetryAnalysis = () => {
      setIsLoadingOutputs(true);
      loadJobOutputs();
    };

    return (
      <>
        {/* Media Dialog */}
        {showMediaDialog && mediaDialogItems.length > 0 && (
          <MediaDialog
            mediaItems={mediaDialogItems}
            activeIndex={activeMediaIndex}
            onMediaChange={handleMediaChange}
            onAddMediaClick={onAddMediaClick}
            onMediaDeleted={handleMediaDeleted}
          />
        )}

        {/* Main Content */}
        {isLoadingMediaList || isLoadingOutputs ? (
          <LoadingAnalysisState />
        ) : jobInputMediaItemsData.length === 0 && !isLoadingMediaList ? (
          <NoMediaFilesState onAddMediaClick={onAddMediaClick} />
        ) : currentItemOutput?.result?.status === "error" ? (
          <AnalysisErrorState
            title="Processing Failed"
            description="The analysis failed to process this media file successfully."
            errorMessage={
              currentItemOutput.result.message ||
              currentItemOutput.result.raw_response ||
              "Unknown error occurred during processing"
            }
            onRetry={handleRetryAnalysis}
            showRetryButton={false}
          />
        ) : currentItemOutput?.result?.data ? (
          <div className="mb-4">
            {/* Action Buttons */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2 text-lg font-semibold">
                <div className="p-1 bg-sky-100 rounded">
                  <FileText className="w-4 h-4 text-sky-600" />
                </div>
                {/* {`output_${
                  jobInputMediaItemsData[selectedIndex]?.filename || "result"
                }.json`} */}
                Media Output
                {/* Verification Badge */}
                {currentItemOutput.verified ? (
                  <Badge className="bg-green-100 text-green-800 border-green-300">
                    <Check className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-gray-600">
                    Unverified
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                {/* Action Buttons */}
                {isEditing ? (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                    >
                      <X className="w-4 h-4 mr-1" />
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveResult}
                      disabled={isSaving || !hasUnsavedChanges}
                    >
                      <Save className="w-4 h-4 mr-1" />
                      {isSaving ? "Saving..." : "Save Changes"}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleStartEdit}
                    >
                      <Edit3 className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={
                        currentItemOutput.verified
                          ? handleUnverify
                          : handleVerify
                      }
                      disabled={isVerifying}
                      className="text-green-700 border-green-400 bg-green-50 hover:bg-green-100 font-medium focus:ring-green-500"
                    >
                      <Check className="w-4 h-4 mr-1" />
                      {currentItemOutput.verified ? "Unverify" : "Verify"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleDownload}
                      disabled={isDownloading}
                      className="text-blue-700 border-blue-400 bg-blue-50 hover:bg-blue-100 font-medium focus:ring-blue-500"
                    >
                      <FileDown className="w-4 h-4 mr-1" />
                      Download Result
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* File Content */}
            <div className="border rounded-md p-4">
              <JobGenericFile
                data={currentItemOutput.result.data}
                filename={`output_${
                  jobInputMediaItemsData[selectedIndex]?.filename || "result"
                }.json`}
                showDownload={false}
                showVerification={false}
                showFilename={false}
                verificationStatus={
                  currentItemOutput.verified ? "verified" : "unverified"
                }
                className=""
                // Inline editing props
                isEditing={isEditing}
                editedData={editedResult?.data}
                onDataChange={(newData) => {
                  setEditedResult({
                    ...editedResult,
                    data: newData,
                  });
                  setHasUnsavedChanges(true);
                }}
              />
            </div>
          </div>
        ) : jobInputMediaItemsData.length > 0 &&
          !isLoadingMediaList &&
          !isLoadingOutputs &&
          (!currentItemOutput?.result ||
            (!currentItemOutput.result.data &&
              currentItemOutput.result.status !== "error")) ? (
          <PendingAnalysisState />
        ) : null}

        {/* Verification Remark Dialog */}
        <RemarkDialog
          open={remarkDialogOpen}
          onOpenChange={setRemarkDialogOpen}
          title={
            verificationAction === "verify"
              ? "Verify Output"
              : "Unverify Output"
          }
          description={
            verificationAction === "verify"
              ? "Please provide a remark for verifying this output. This will mark the output as verified."
              : "Please provide a remark for unverifying this output. This will remove the verification status."
          }
          placeholder={
            verificationAction === "verify"
              ? "Enter verification notes..."
              : "Enter reason for unverifying..."
          }
          confirmLabel={verificationAction === "verify" ? "Verify" : "Unverify"}
          onConfirm={handleVerificationConfirm}
          loading={isVerifying}
          initialRemark={currentItemOutput?.verification_notes || ""}
          required={true}
        />

        {/* Delete Confirmation Dialog */}
        <ConfirmationDialog
          open={showDeleteConfirm}
          onOpenChange={setShowDeleteConfirm}
          title="Delete Media File"
          description={`Are you sure you want to delete "${mediaToDelete?.filename}"? This action cannot be undone.`}
          confirmLabel="Delete"
          cancelLabel="Cancel"
          onConfirm={handleDeleteMedia}
          loading={isDeleting}
          variant="destructive"
        />
      </>
    );
  }
);

JobGenericFiles.displayName = "JobGenericFiles";

export default JobGenericFiles;
