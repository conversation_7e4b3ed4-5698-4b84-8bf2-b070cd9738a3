import api from "@/utils/axios.util";

export const DeleteMediaService = async (jobId: string, mediaId: string) => {
  try {
    const response = await api.delete(`/jobs/${jobId}/media/${mediaId}`);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while deleting media",
    };
  }
};

// New service to get all input media for a job with presigned URLs
// export const GetJobInputMediaService = async (jobId: string) => {
//   try {
//     const response = await api.get(`/jobs/${jobId}/media`, {
//       params: {
//         include_presigned_urls: true,
//         expiry: 86400,
//       },
//     });
//     return {
//       success: true,
//       status: response.status,
//       data: response.data,
//     };
//   } catch (error: any) {
//     return {
//       success: false,
//       status: error?.response?.status || 500,
//       data:
//         error?.response?.data?.detail ||
//         "An error occurred while getting job input media",
//     };
//   }
// };

// // New service to get all output media for a job with presigned URLs and results
// export const GetJobOutputMediaService = async (jobId: string) => {
//   try {
//     const response = await api.get(`/jobs/${jobId}/output-media`, {
//       params: {
//         include_presigned_urls: true,
//         include_results: true,
//         expiry: 86400,
//       },
//     });
//     return {
//       success: true,
//       status: response.status,
//       data: response.data,
//     };
//   } catch (error: any) {
//     return {
//       success: false,
//       status: error?.response?.status || 500,
//       data:
//         error?.response?.data?.detail ||
//         "An error occurred while getting job output media",
//     };
//   }
// };

// Legacy services - keeping for backward compatibility
export const GetInputInputMediaService = async (mediaId: string) => {
  try {
    const media = await api.get(`/media/${mediaId}`);
    const presignedUrl = await api.get(`/jobs/media/presigned-url`, {
      params: {
        object_name: media.data.object_name,
        expiry: 86400,
      },
    });

    const inputResult = {
      ...media.data,
      presigned_url: presignedUrl.data.presigned_url,
    };
    return inputResult;
  } catch (error: any) {
    console.error("Error in GetInputInputMediaService:", error);
    return null;
  }
};

export const GetJobOutputMediaService = async (mediaId: string) => {
  try {
    const media = await api.get(`/media/${mediaId}`);
    const presignedUrl = await api.get(`/jobs/media/presigned-url`, {
      params: {
        object_name: media.data.object_name,
        expiry: 86400,
      },
    });

    // make the api call to presignedUrl to get the output
    const output = await fetch(presignedUrl.data.presigned_url);
    const outputData = await output.json();

    const outputResult = {
      ...media.data,
      presigned_url: presignedUrl.data.presigned_url,
      result: outputData,
    };
    return outputResult;
  } catch (error: any) {
    console.error("Error in GetJobOutputMediaService:", error);
    return null;
  }
};
