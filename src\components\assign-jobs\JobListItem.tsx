import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  UserMinus,
  UserPlus,
  Calendar,
  ExternalLink,
  FolderIcon,
  Edit,
  Trash2,
  AudioLines,
  Image,
  FileSearch,
  Briefcase,
} from "lucide-react";
import { formatFriendlyDate } from "@/utils/commons.utils";
import { getStatusBadgeVariant } from "@/utils/status.utils";

type JobWithAssignee = Job & {
  assigned_to?: string;
  assigned_to_name?: string;
  project_name?: string;
};

interface JobListItemProps {
  job: JobWithAssignee;
  isSelected: boolean;
  onSelect: (jobId: string) => void;
  onJobClick: (jobId: string) => void;
  onProjectClick: (projectId: string) => void;
  onAssignJob: (jobId: string, assignee: string) => void;
  onUnassignJob: (jobId: string) => void;
  agents: User[];
  // Optional edit/delete functionality
  showEditDelete?: boolean;
  onEditJob?: (job: JobWithAssignee) => void;
  onDeleteJob?: (job: JobWithAssignee) => void;
}

export const JobListItem: React.FC<JobListItemProps> = ({
  job,
  isSelected,
  onSelect,
  onJobClick,
  onProjectClick,
  onAssignJob,
  onUnassignJob,
  agents,
  showEditDelete = false,
  onEditJob,
  onDeleteJob,
}) => {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === " " || event.key === "Enter") {
      event.preventDefault();
      onSelect(job._id);
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
    >
      <Card
        className={`border cursor-pointer py-5 bg-white transition-all duration-200 group relative focus-within:ring-2 focus-within:ring-sky-600 focus-within:ring-opacity-50 ${
          isSelected
            ? "border-sky-600 bg-sky-600 bg-opacity-5 shadow-md"
            : "border-[#E5E7EB] hover:border-sky-600/30"
        }`}
        tabIndex={0}
        onKeyDown={handleKeyDown}
        role="listitem"
        aria-selected={isSelected}
        aria-label={`Job: ${job.name} in project ${
          job.project_name || "Not Available"
        }`}
        onClick={() => onJobClick(job._id)}
      >
        <CardContent className="px-4">
          <div className="flex items-start gap-4">
            {/* Checkbox - visible only on hover */}
            <div className="flex-shrink-0">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => {
                  return onSelect(job._id);
                }}
                onClick={(e) => e.stopPropagation()}
                className={`w-5 h-5 mt-1 transition-opacity duration-200 ${
                  isSelected
                    ? "opacity-100"
                    : "opacity-0 group-hover:opacity-100"
                }`}
                aria-label={`Select job ${job.name}`}
              />
            </div>

            <div className="flex-1 space-y-1">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <button
                    onClick={() => onJobClick(job._id)}
                    className="text-lg font-semibold text-gray-800 hover:text-sky-900 transition-colors text-left flex items-center gap-2 cursor-pointer"
                  >
                    {job.name}
                    <ExternalLink className="w-4 h-4" />
                  </button>

                  {job.process_name && (
                    <button className="text-sm text-gray-600 hover:text-gray-700 font-medium transition-colors mt-1 flex items-center gap-2 cursor-pointer">
                      {job.process_name === "audio-transcribe-analysis" ? (
                        <AudioLines className="w-4 h-4" fillRule="evenodd" />
                      ) : job.process_name === "extract-image-data" ? (
                        <Image className="w-4 h-4" fillRule="evenodd" />
                      ) : job.process_name === "generic-entity-extraction" ? (
                        <FileSearch className="w-4 h-4" fillRule="evenodd" />
                      ) : (
                        <Briefcase className="w-4 h-4" fillRule="evenodd" />
                      )}
                      {job.process_name || "Not Available"}
                    </button>
                  )}

                  <button
                    onClick={() => onProjectClick(job.project_id)}
                    className="text-sm text-gray-600 hover:text-gray-700 font-medium transition-colors mt-1 flex items-center gap-2 cursor-pointer"
                  >
                    <FolderIcon className="w-4 h-4" fillRule="evenodd" />
                    {job.project_name || "Not Available"}
                    <ExternalLink className="w-3 h-3" />
                  </button>
                </div>

                <Badge
                  variant={getStatusBadgeVariant(job.status)}
                  className="text-xs font-medium px-3 py-1 rounded-full"
                >
                  {job.status.toUpperCase()}
                </Badge>
              </div>

              {/* Job metadata with action buttons */}
              <div className="flex items-center justify-between text-sm text-[#6B7280]">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {job.assigned_to_name ? (
                      <>
                        <User className="w-4 h-4" />
                        <span>{job.assigned_to_name}</span>
                      </>
                    ) : (
                      <>
                        <UserMinus className="w-4 h-4 text-[#DC3545]" />
                        <span className="text-[#DC3545]">Unassigned</span>
                      </>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{formatFriendlyDate(job.created_at)}</span>
                  </div>
                </div>

                {/* Action buttons - same row as metadata */}
                <div className="flex items-center gap-2">
                  {/* Assignment button */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 p-0 text-sky-600 hover:text-sky-700 hover:bg-sky-50"
                        title="Assign Job"
                      >
                        <UserPlus className="w-5 h-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-72">
                      {/* <DropdownMenuContent align="end" className="w-48"> */}
                      <DropdownMenuItem
                        className="text-sm font-medium text-black cursor-default pointer-events-none"
                        onSelect={(e) => e.preventDefault()}
                      >
                        Choose Assignee
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {agents.map((agent) => (
                        <DropdownMenuItem
                          key={agent._id}
                          onClick={(e) => {
                            e.stopPropagation();
                            onAssignJob(job._id, agent._id);
                          }}
                          className="flex items-center gap-2 text-sm cursor-pointer"
                        >
                          <User className="w-4 h-4 text-sky-600" />
                          {/* {agent.username} */}
                          <div className="flex items-center gap-2">
                            <span>{agent.username}</span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              {agent.role}
                            </span>
                          </div>
                        </DropdownMenuItem>
                      ))}
                      {job.assigned_to && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onUnassignJob(job._id)}
                            className="flex items-center gap-2 text-sm text-[#DC3545] cursor-pointer"
                          >
                            <UserMinus className="w-4 h-4" />
                            Unassign
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Edit and Delete buttons (optional) */}
                  {showEditDelete && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditJob?.(job);
                        }}
                        title="Edit Job"
                      >
                        <Edit className="w-5 h-5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteJob?.(job);
                        }}
                        title="Delete Job"
                      >
                        <Trash2 className="w-5 h-5" />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
