import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Zap, Users, FileText } from "lucide-react";
import { useRouter } from "next/router";
import ProjectDialog from "@/features/projects/ProjectDialog";
import DashboardJobDialog from "./DashboardJobDialog";

export const QuickActionsSection: React.FC = () => {
  const router = useRouter();
  const [projectDialogOpen, setProjectDialogOpen] = useState(false);
  const [jobDialogOpen, setJobDialogOpen] = useState(false);

  const handleProjectCreated = (projectData: any) => {
    // Redirect to the project details page using the project ID from the response
    if (projectData?._id) {
      router.push(`/projects/${projectData._id}`);
    }
  };

  const handleJobCreated = (projectData: any, jobData: any) => {
    // Redirect to the job details page using both project ID and job ID from the response
    if (projectData?._id && jobData?._id) {
      router.push(`/projects/${projectData._id}/job/${jobData._id}`);
    } else if (projectData?._id) {
      // Fallback to project page if job ID is not available
      router.push(`/projects/${projectData._id}`);
    }
  };

  const actions = [
    {
      title: "AI Playground",
      description: "Experiment with multimodal AI",
      icon: <Zap className="w-5 h-5 text-slate-600" />,
      onClick: () => router.push("/playground"),
    },
    {
      title: "New Project",
      description: "Create a new project",
      icon: <Plus className="w-5 h-5 text-emerald-600" />,
      onClick: () => setProjectDialogOpen(true),
    },
    {
      title: "New Job",
      description: "Start a new digitization job",
      icon: <FileText className="w-5 h-5 text-blue-600" />,
      onClick: () => setJobDialogOpen(true),
    },
    {
      title: "Assign Jobs",
      description: "Manage job assignments",
      icon: <Users className="w-5 h-5 text-violet-600" />,
      onClick: () => router.push("/assign-jobs"),
    },
  ];

  return (
    <>
      <Card className="bg-white shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200 rounded-md h-[600px] flex flex-col">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-slate-800">
            Quick Actions
          </CardTitle>
          <p className="text-sm text-slate-600 mt-2">
            Frequently used actions and shortcuts
          </p>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col justify-center space-y-4 px-6 pb-6">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="bg-white hover:bg-slate-50 border-gray-200 hover:border-slate-300 text-slate-800 h-auto p-6 justify-start transition-all duration-300 hover:scale-[1.01] hover:shadow-md w-full group animate-fade-in rounded-md"
              onClick={action.onClick}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex items-center gap-4 w-full">
                <div className="p-3 rounded-md bg-slate-50 group-hover:bg-slate-100 transition-all duration-200 flex-shrink-0">
                  {action.icon}
                </div>
                <div className="text-left flex-1 min-w-0">
                  <div className="font-semibold text-base text-slate-800">
                    {action.title}
                  </div>
                  <div className="text-sm text-slate-600 truncate">
                    {action.description}
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Project Dialog */}
      <ProjectDialog
        open={projectDialogOpen}
        setOpen={setProjectDialogOpen}
        onProjectCreated={handleProjectCreated}
      />

      {/* Job Dialog */}
      <DashboardJobDialog
        open={jobDialogOpen}
        setOpen={setJobDialogOpen}
        onJobCreated={handleJobCreated}
      />
    </>
  );
};
