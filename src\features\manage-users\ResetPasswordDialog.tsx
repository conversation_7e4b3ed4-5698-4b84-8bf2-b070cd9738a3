import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import Button from "@/components/custom/Button";
import { Loader2, Key, AlertTriangle } from "lucide-react";
import { ResetPasswordService } from "@/services/users.service";
import { snackbar } from "@/utils/snackbar.util";

export default function ResetPasswordDialog({
  open,
  setOpen,
  selectedUser,
  setSelectedUser,
  setShowNewPasswordDialogOpen,
  setNewPasswordFields,
}: ResetPasswordDialogProps) {
  const [loading, setLoading] = useState(false);

  const handleCloseDialog = (event: React.SyntheticEvent) => {
    setOpen(false);

    setTimeout(() => {
      setLoading(false);
      setSelectedUser(null);
    }, 500);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    setLoading(true);
    const resetPasswordResponse = await ResetPasswordService(selectedUser._id);
    if (resetPasswordResponse.success) {
      setShowNewPasswordDialogOpen(true);
      setNewPasswordFields({
        username: selectedUser.username,
        newPassword: resetPasswordResponse.data,
      });
      handleCloseDialog(event);
    } else {
      setLoading(false);
      snackbar.showErrorMessage(resetPasswordResponse.data);
    }
  };

  if (!selectedUser) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {/* <div className="w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div> */}
            <div>
              <h3 className="text-lg font-semibold">Reset Password</h3>
              <p className="text-sm text-gray-500 font-normal">
                This action will generate a new password
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="py-6">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <p className="text-sm text-amber-800">
              Are you sure you want to reset the password for{" "}
              <span className="font-semibold">{selectedUser.username}</span>?
            </p>
            <p className="text-xs text-amber-700 mt-2">
              A new password will be generated and displayed for you to share
              with the user.
            </p>
          </div>
        </div>
        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            loading={loading}
            startIcon={<Key className="h-4 w-4" />}
            variant="destructive"
          >
            Reset Password
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type ResetPasswordDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedUser: any;
  setSelectedUser: React.Dispatch<React.SetStateAction<any>>;
  setShowNewPasswordDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setNewPasswordFields: React.Dispatch<React.SetStateAction<NewPasswordFields>>;
};

type NewPasswordFields = {
  username: string;
  newPassword: string;
};
