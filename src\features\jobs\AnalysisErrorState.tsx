"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AnalysisErrorStateProps {
  className?: string;
  title?: string;
  description?: string;
  errorMessage?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
}

export default function AnalysisErrorState({
  className = "",
  title = "Analysis Error",
  description = "An error occurred while processing this media file. The analysis could not be completed.",
  errorMessage,
  onRetry,
  showRetryButton = true,
}: AnalysisErrorStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={`flex flex-col items-center justify-center p-20 text-center ${className}`}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
        className="p-4 bg-red-100 rounded-full mb-6"
      >
        <AlertTriangle className="w-12 h-12 text-red-600" />
      </motion.div>
      <motion.h3
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.4 }}
        className="text-2xl font-bold text-red-700 mb-3"
      >
        {title}
      </motion.h3>
      <motion.p
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.4 }}
        className="text-gray-600 text-lg leading-relaxed max-w-md mb-4"
      >
        {description}
      </motion.p>
      
      {errorMessage && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.4 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 max-w-lg"
        >
          <p className="text-sm text-red-800 font-medium mb-2">Error Details:</p>
          <p className="text-sm text-red-700 break-words">{errorMessage}</p>
        </motion.div>
      )}

      {showRetryButton && onRetry && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.4 }}
        >
          <Button
            onClick={onRetry}
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400 font-semibold px-6 py-3 rounded-lg shadow-sm transition-all duration-200"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            Retry Analysis
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}
