"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import Button from "@/components/custom/Button";
import {
  FileText,
  Download,
  Shield,
  Check,
  AlertCircle,
  ChevronDown,
  ChevronRight,
  CheckCircle,
  XCircle,
  // Hash,
  Calendar,
  Link,
  Image,
  Play,
} from "lucide-react";
import { formatFriendlyDate } from "@/utils/commons.utils";
import { motion, AnimatePresence } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface JobGenericFileProps {
  data: any;
  filename?: string;
  showDownload?: boolean;
  showVerification?: boolean;
  showFilename?: boolean;
  showRawToggle?: boolean;
  verificationStatus?: "verified" | "unverified" | "pending";
  onVerify?: () => void;
  onDownload?: () => void;
  className?: string;
  // Inline editing props
  isEditing?: boolean;
  editedData?: any;
  onDataChange?: (newData: any) => void;
}

// Utility functions for data type detection and formatting
const isDateString = (value: string): boolean => {
  if (typeof value !== "string") return false;
  const dateRegex =
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$|^\d{4}-\d{2}-\d{2}$/;
  return dateRegex.test(value) && !isNaN(Date.parse(value));
};

const isUrl = (value: string): boolean => {
  if (typeof value !== "string") return false;
  try {
    new URL(value);
    return value.startsWith("http://") || value.startsWith("https://");
  } catch {
    return false;
  }
};

const isEmail = (value: string): boolean => {
  if (typeof value !== "string") return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
};

const isPhone = (value: string): boolean => {
  if (typeof value !== "string") return false;
  const phoneRegex =
    /^[\+]?[1-9][\d]{0,15}$|^\(\d{3}\)\s?\d{3}-\d{4}$|^\d{3}-\d{3}-\d{4}$/;
  return phoneRegex.test(value.replace(/\s/g, ""));
};

const isImageUrl = (value: string): boolean => {
  if (!isUrl(value)) return false;
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i;
  return imageExtensions.test(value);
};

const isMediaUrl = (value: string): boolean => {
  if (!isUrl(value)) return false;
  const mediaExtensions =
    /\.(mp4|avi|mov|wmv|flv|webm|mp3|wav|ogg|aac|flac)(\?.*)?$/i;
  return mediaExtensions.test(value);
};

const getArrayType = (arr: any[]): string => {
  if (arr.length === 0) return "empty";

  const types = arr.map((item) => {
    if (item === null || item === undefined) return "null";
    if (Array.isArray(item)) return "array";
    return typeof item;
  });

  const uniqueTypes = [...new Set(types)];

  if (uniqueTypes.length === 1) {
    return uniqueTypes[0];
  } else if (
    uniqueTypes.every((type) => type === "object" || type === "null")
  ) {
    return "object";
  } else {
    return "mixed";
  }
};

export const JobGenericFile: React.FC<JobGenericFileProps> = ({
  data,
  filename = "output.json",
  showDownload = true,
  showVerification = false,
  showFilename = true,
  showRawToggle = true,
  verificationStatus = "unverified",
  onVerify,
  onDownload,
  className = "",
  // Inline editing props
  isEditing = false,
  editedData,
  onDataChange,
}) => {
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set()
  );
  const [renderError, setRenderError] = useState<string | null>(null);

  // Validate data on component mount and when data changes
  React.useEffect(() => {
    try {
      if (data === null || data === undefined) {
        setRenderError("No data provided");
        return;
      }

      // Try to access the data to see if it causes any errors
      JSON.stringify(data);
      setRenderError(null);
    } catch (error) {
      console.error("Error validating data in JobGenericFile:", error);
      setRenderError("Invalid data format");
    }
  }, [data]);

  const toggleSection = (path: string) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(path)) {
      newCollapsed.delete(path);
    } else {
      newCollapsed.add(path);
    }
    setCollapsedSections(newCollapsed);
  };

  // Helper function to update nested data using path array (from demo)
  const updateValue = (path: string[], newValue: any) => {
    if (!onDataChange) return;

    const currentData = editedData || data;
    const updated = JSON.parse(JSON.stringify(currentData));
    let current = updated;

    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    const lastKey = path[path.length - 1];

    // Handle type conversion (from demo)
    if (typeof current[lastKey] === "number" && !isNaN(Number(newValue))) {
      current[lastKey] = Number(newValue);
    } else if (typeof current[lastKey] === "boolean") {
      current[lastKey] = newValue === "true";
    } else if (current[lastKey] === null && newValue === "") {
      current[lastKey] = null;
    } else {
      current[lastKey] = newValue;
    }

    onDataChange(updated);
  };

  // Helper function to get nested value using path array
  const getNestedValue = (obj: any, path: string[]): any => {
    if (!path || path.length === 0) return obj;
    return path.reduce((current, key) => current?.[key], obj);
  };

  // Render editable value only (no key editing)
  const renderEditableValue = (
    currentKey: string,
    currentValue: any,
    pathArray: string[]
  ): React.ReactNode => {
    const currentData = editedData || data;
    const actualValue = getNestedValue(currentData, pathArray) ?? currentValue;

    return (
      <div className="flex items-center gap-3">
        {/* Non-editable Key */}
        <span className="font-semibold text-sm text-gray-700">
          {currentKey}:
        </span>

        {/* Editable Value */}
        <div className="flex-1">
          {typeof actualValue === "string" && actualValue.length > 50 ? (
            <Textarea
              value={actualValue || ""}
              onChange={(e) => updateValue(pathArray, e.target.value)}
              className="text-sm text-gray-900 min-h-[80px] resize-none border-gray-300 focus:border-blue-400 focus:ring-blue-400"
              placeholder="Value"
            />
          ) : (
            <Input
              value={actualValue?.toString() || ""}
              onChange={(e) => updateValue(pathArray, e.target.value)}
              className="text-sm text-gray-900 border-gray-300 focus:border-blue-400 focus:ring-blue-400"
              placeholder="Value"
            />
          )}
        </div>
      </div>
    );
  };

  const renderValue = (
    value: any,
    key?: string,
    pathArray: string[] = []
  ): React.ReactNode => {
    const currentPathArray = key ? [...pathArray, key] : pathArray;
    const currentData = editedData || data;
    const actualValue = getNestedValue(currentData, currentPathArray) ?? value;

    // Enhanced null/undefined handling
    if (actualValue === null || actualValue === undefined) {
      if (isEditing && key) {
        return renderEditableValue(key, actualValue, currentPathArray);
      }
      return (
        <div className="flex items-center gap-3">
          {key && (
            <span className="font-semibold text-sm text-gray-700">{key}:</span>
          )}
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            <span className="text-gray-500 italic text-sm bg-gray-100 px-2 py-0.5 rounded">
              {actualValue === null ? "null" : "undefined"}
            </span>
          </div>
        </div>
      );
    }

    // Boolean - Enhanced with toggle-style display
    if (typeof actualValue === "boolean") {
      if (isEditing && key) {
        return renderEditableValue(key, actualValue, currentPathArray);
      }
      return (
        <div className="flex items-center gap-3">
          {key && (
            <span className="font-semibold text-sm text-gray-700">{key}:</span>
          )}
          <div className="flex items-center gap-2">
            <div
              className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${
                actualValue
                  ? "bg-green-100 text-green-800 border border-green-200"
                  : "bg-red-100 text-red-800 border border-red-200"
              }`}
            >
              {actualValue ? (
                <>
                  <CheckCircle className="w-3 h-3" />
                  True
                </>
              ) : (
                <>
                  <XCircle className="w-3 h-3" />
                  False
                </>
              )}
            </div>
          </div>
        </div>
      );
    }

    // Number - Enhanced with better formatting
    if (typeof actualValue === "number") {
      if (isEditing && key) {
        return renderEditableValue(key, actualValue, currentPathArray);
      }
      const isInteger = Number.isInteger(actualValue);
      const isLarge = Math.abs(actualValue) >= 1000;

      return (
        <div className="flex items-center gap-3">
          {key && (
            <span className="font-semibold text-sm text-gray-700">{key}:</span>
          )}
          <div className="flex items-center gap-2">
            {/* <Hash className="w-3.5 h-3.5 text-gray-400" /> */}
            <span
              className={`text-sm font-mono ${
                isLarge ? "font-semibold text-blue-700" : "text-gray-900"
              }`}
            >
              {actualValue.toLocaleString()}
            </span>
            {!isInteger && (
              <span className="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">
                decimal
              </span>
            )}
          </div>
        </div>
      );
    }

    // String - check if long value (more than 50 characters)
    if (typeof actualValue === "string") {
      if (isEditing && key) {
        return renderEditableValue(key, actualValue, currentPathArray);
      }
      const isLongValue = actualValue.length > 50;

      if (isLongValue) {
        return (
          <div className="space-y-2">
            {key && (
              <div className="font-semibold text-sm text-gray-700">{key}:</div>
            )}
            <div className="pl-4">
              {isDateString(actualValue) ? (
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium text-gray-900">
                    {formatFriendlyDate(actualValue)}
                  </span>
                </div>
              ) : isImageUrl(actualValue) ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Image className="w-4 h-4 text-green-500" />
                    <a
                      href={actualValue}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                    >
                      {actualValue}
                    </a>
                  </div>
                  <img
                    src={actualValue}
                    alt="Preview"
                    className="max-w-xs max-h-32 rounded-lg border border-gray-300 object-cover shadow-sm"
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              ) : isMediaUrl(actualValue) ? (
                <div className="flex items-center gap-2">
                  <Play className="w-4 h-4 text-purple-500" />
                  <a
                    href={actualValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                  >
                    {actualValue}
                  </a>
                </div>
              ) : isUrl(actualValue) ? (
                <div className="flex items-center gap-2">
                  <Link className="w-4 h-4 text-blue-500" />
                  <a
                    href={actualValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                  >
                    {actualValue}
                  </a>
                </div>
              ) : isEmail(actualValue) ? (
                <a
                  href={`mailto:${actualValue}`}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  {actualValue}
                </a>
              ) : isPhone(actualValue) ? (
                <a
                  href={`tel:${actualValue}`}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  {actualValue}
                </a>
              ) : (
                <span className="text-sm text-gray-900 leading-relaxed break-words">
                  {actualValue}
                </span>
              )}
            </div>
          </div>
        );
      } else {
        return (
          <div className="flex items-center gap-3">
            {key && (
              <span className="font-semibold text-sm text-gray-700">
                {key}:
              </span>
            )}
            <div className="flex items-center gap-2">
              {isDateString(actualValue) ? (
                <>
                  <Calendar className="w-3.5 h-3.5 text-blue-500" />
                  <span className="text-sm font-medium text-gray-900">
                    {formatFriendlyDate(actualValue)}
                  </span>
                </>
              ) : isImageUrl(actualValue) ? (
                <>
                  <Image className="w-3.5 h-3.5 text-green-500" />
                  <a
                    href={actualValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm"
                  >
                    {actualValue}
                  </a>
                </>
              ) : isMediaUrl(actualValue) ? (
                <>
                  <Play className="w-3.5 h-3.5 text-purple-500" />
                  <a
                    href={actualValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm"
                  >
                    {actualValue}
                  </a>
                </>
              ) : isUrl(actualValue) ? (
                <>
                  <Link className="w-3.5 h-3.5 text-blue-500" />
                  <a
                    href={actualValue}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm"
                  >
                    {actualValue}
                  </a>
                </>
              ) : isEmail(actualValue) ? (
                <a
                  href={`mailto:${actualValue}`}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  {actualValue}
                </a>
              ) : isPhone(actualValue) ? (
                <a
                  href={`tel:${actualValue}`}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  {actualValue}
                </a>
              ) : (
                <span className="text-sm text-gray-900">{actualValue}</span>
              )}
            </div>
          </div>
        );
      }
    }

    // Arrays - Enhanced with better styling
    if (Array.isArray(actualValue)) {
      if (actualValue.length === 0) {
        return (
          <div className="flex items-center gap-3">
            {key && (
              <span className="font-semibold text-sm text-gray-700">
                {key}:
              </span>
            )}
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span className="text-gray-500 italic text-sm">Empty list</span>
            </div>
          </div>
        );
      }

      const arrayType = getArrayType(actualValue);

      // Array of Objects - render as table
      if (arrayType === "object") {
        return (
          <div className="space-y-3">
            {key && (
              <div className="flex items-center gap-2">
                <span className="font-semibold text-sm text-gray-700">
                  {key}:
                </span>
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium">
                  {actualValue.length}{" "}
                  {actualValue.length === 1 ? "item" : "items"}
                </span>
              </div>
            )}
            <div className="ml-4">
              {renderTable(actualValue, currentPathArray)}
            </div>
          </div>
        );
      }

      // Array of primitives - render as styled list with collapsible functionality
      const currentPath = currentPathArray.join(".");
      const isCollapsed = collapsedSections.has(currentPath);
      const hasManyItems = actualValue.length > 5;
      const displayItems = isCollapsed ? actualValue.slice(0, 3) : actualValue;

      return (
        <div className="space-y-3">
          {key && (
            <div className="flex items-center gap-2">
              {hasManyItems && (
                <button
                  onClick={() => toggleSection(currentPath)}
                  className="flex items-center justify-center w-5 h-5 rounded hover:bg-gray-200 transition-colors"
                >
                  {isCollapsed ? (
                    <ChevronRight className="w-3 h-3 text-gray-500" />
                  ) : (
                    <ChevronDown className="w-3 h-3 text-gray-500" />
                  )}
                </button>
              )}
              <span className="font-semibold text-sm text-gray-700">
                {key}:
              </span>
              <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-medium">
                {actualValue.length}{" "}
                {actualValue.length === 1 ? "item" : "items"}
              </span>
            </div>
          )}
          <div className="ml-4 space-y-2">
            {displayItems.map((item: any, index: number) => {
              const itemPathArray = [...currentPathArray, index.toString()];
              const actualItem =
                getNestedValue(editedData || data, itemPathArray) ?? item;

              return (
                <div key={index} className="flex items-start gap-3">
                  <span className="text-sm text-gray-500 font-mono mt-0.5 min-w-[1.5rem]">
                    {index + 1}.
                  </span>
                  <div className="flex-1 min-w-0">
                    {isEditing &&
                    (typeof actualItem === "string" ||
                      typeof actualItem === "number" ||
                      typeof actualItem === "boolean" ||
                      actualItem === null ||
                      actualItem === undefined) ? (
                      // Render editable array item for primitive values
                      <div className="w-full">
                        {typeof actualItem === "string" &&
                        actualItem.length > 50 ? (
                          <Textarea
                            value={actualItem || ""}
                            onChange={(e) =>
                              updateValue(itemPathArray, e.target.value)
                            }
                            className="text-sm text-gray-900 min-h-[80px] resize-none border-gray-300 focus:border-blue-400 focus:ring-blue-400 w-full"
                            placeholder="Value"
                          />
                        ) : (
                          <Input
                            value={actualItem?.toString() || ""}
                            onChange={(e) =>
                              updateValue(itemPathArray, e.target.value)
                            }
                            className="text-sm text-gray-900 border-gray-300 focus:border-blue-400 focus:ring-blue-400 w-full"
                            placeholder="Value"
                          />
                        )}
                      </div>
                    ) : (
                      // Render normal value (for objects, nested arrays, or display mode)
                      renderValue(actualItem, undefined, itemPathArray)
                    )}
                  </div>
                </div>
              );
            })}
            {isCollapsed && hasManyItems && (
              <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
                <span className="text-xs text-gray-500">
                  ... and {actualValue.length - 3} more items
                </span>
                <button
                  onClick={() => toggleSection(currentPath)}
                  className="text-xs text-blue-600 hover:text-blue-800 underline"
                >
                  Show all
                </button>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Objects - Enhanced nested rendering with collapsible functionality
    if (typeof actualValue === "object") {
      const entries = Object.entries(actualValue);
      const currentPath = currentPathArray.join(".");
      const isCollapsed = collapsedSections.has(currentPath);
      const hasMultipleProperties = entries.length > 3;

      return (
        <div className="space-y-3">
          {key && (
            <div className="flex items-center gap-2">
              {hasMultipleProperties && (
                <button
                  onClick={() => toggleSection(currentPath)}
                  className="flex items-center justify-center w-5 h-5 rounded hover:bg-gray-200 transition-colors"
                >
                  {isCollapsed ? (
                    <ChevronRight className="w-3 h-3 text-gray-500" />
                  ) : (
                    <ChevronDown className="w-3 h-3 text-gray-500" />
                  )}
                </button>
              )}
              <span className="font-semibold text-sm text-gray-700">
                {key}:
              </span>
              <span className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full font-medium">
                {entries.length} {entries.length === 1 ? "item" : "items"}
              </span>
            </div>
          )}
          {!isCollapsed && (
            <div className="ml-4 space-y-3">
              {entries.map(([objKey, objValue]) => (
                <div key={objKey} className="relative">
                  <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gray-300 rounded-full"></div>
                  <div className="pl-4">
                    {renderValue(objValue, objKey, currentPathArray)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    return <span className="text-sm text-gray-600">{String(actualValue)}</span>;
  };

  const renderTable = (
    data: any[],
    tablePathArray: string[] = []
  ): React.ReactNode => {
    if (data.length === 0) return null;

    // Get all unique keys from all objects
    const allKeys = Array.from(
      new Set(data.flatMap((item) => Object.keys(item)))
    );

    return (
      <div className="overflow-x-auto bg-white border border-gray-200 rounded-xl shadow-sm">
        <table className="min-w-full">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-200">
              {/* <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 w-16 bg-gray-100">
                <div className="flex items-center gap-1">
                  <Hash className="w-3 h-3" />
                </div>
              </th> */}
              {allKeys.map((key) => (
                <th
                  key={key}
                  className="px-4 py-3 text-left text-xs font-bold text-gray-700 bg-gray-50"
                >
                  <div className="flex items-center gap-1 capitalize">
                    {key.replace(/_/g, " ")}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {data.map((item, index) => (
              <tr
                key={index}
                className="hover:bg-gray-50 transition-colors duration-150"
              >
                {/* <td className="px-4 py-3 text-xs text-gray-500 font-mono border-r border-gray-100">
                  {index + 1}
                </td> */}
                {allKeys.map((key) => {
                  const cellPathArray = [
                    ...tablePathArray,
                    index.toString(),
                    key,
                  ];
                  const cellValue = item[key];
                  const actualCellValue =
                    getNestedValue(editedData || data, cellPathArray) ??
                    cellValue;

                  return (
                    <td key={key} className="px-4 py-3 text-sm max-w-xs">
                      <div className="break-words">
                        {isEditing &&
                        (typeof actualCellValue === "string" ||
                          typeof actualCellValue === "number" ||
                          typeof actualCellValue === "boolean" ||
                          actualCellValue === null ||
                          actualCellValue === undefined) ? (
                          // Render editable table cell for primitive values
                          typeof actualCellValue === "string" &&
                          actualCellValue.length > 50 ? (
                            <Textarea
                              value={actualCellValue || ""}
                              onChange={(e) =>
                                updateValue(cellPathArray, e.target.value)
                              }
                              className="text-sm text-gray-900 min-h-[80px] resize-none border-gray-300 focus:border-blue-400 focus:ring-blue-400 w-full"
                              placeholder="Value"
                            />
                          ) : (
                            <Input
                              value={actualCellValue?.toString() || ""}
                              onChange={(e) =>
                                updateValue(cellPathArray, e.target.value)
                              }
                              className="text-sm text-gray-900 border-gray-300 focus:border-blue-400 focus:ring-blue-400 w-full"
                              placeholder="Value"
                            />
                          )
                        ) : (
                          // Render normal value (for objects, arrays, or display mode)
                          renderValue(actualCellValue, key, [
                            ...tablePathArray,
                            index.toString(),
                          ])
                        )}
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      const jsonData = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const getVerificationBadge = () => {
    switch (verificationStatus) {
      case "verified":
        return (
          <Badge
            variant="default"
            className="bg-green-100 text-green-800 border-green-200"
          >
            <Check className="w-3 h-3 mr-1" />
            Verified
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="secondary"
            className="bg-yellow-100 text-yellow-800 border-yellow-200"
          >
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-300">
            <Shield className="w-3 h-3 mr-1" />
            Unverified
          </Badge>
        );
    }
  };

  // Show error state if there's a render error
  if (renderError) {
    return (
      <div className={`${className}`}>
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center gap-2 text-red-700">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Data Display Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">
            {renderError}: Unable to display the data in the expected format.
          </p>
          <details className="mt-2">
            <summary className="text-xs text-red-500 cursor-pointer">
              Show raw data
            </summary>
            <pre className="text-xs text-red-600 mt-1 p-2 bg-red-100 rounded overflow-auto max-h-32">
              {JSON.stringify(data, null, 2)}
            </pre>
          </details>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {(showFilename || showVerification || showRawToggle || showDownload) && (
        <div className="pb-3">
          <div className="flex items-center justify-between">
            {showFilename ? (
              <div className="flex items-center gap-2 text-lg font-semibold">
                <div className="p-1 bg-sky-100 rounded">
                  <FileText className="w-4 h-4 text-sky-600" />
                </div>
                {filename}
              </div>
            ) : (
              <div></div>
            )}
            <div className="flex items-center gap-2">
              {showVerification && getVerificationBadge()}

              {showDownload && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                  startIcon={<Download className="w-4 h-4" />}
                >
                  Download
                </Button>
              )}
              {showVerification &&
                verificationStatus !== "verified" &&
                onVerify && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onVerify}
                    startIcon={<Shield className="w-4 h-4" />}
                    className="border-green-300 text-green-700 hover:bg-green-50"
                  >
                    Verify
                  </Button>
                )}
            </div>
          </div>
        </div>
      )}
      <div>
        <AnimatePresence mode="wait">
          {(() => {
            try {
              return (
                <motion.div
                  key="formatted-view"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="space-y-6 overflow-auto"
                >
                  {renderValue(editedData || data, undefined, [])}
                </motion.div>
              );
            } catch (error) {
              console.error("Error rendering data:", error);
              return (
                <motion.div
                  key="error-view"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                  className="p-4 bg-red-50 border border-red-200 rounded-md"
                >
                  <div className="flex items-center gap-2 text-red-700">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">Rendering Error</span>
                  </div>
                  <p className="text-red-600 text-sm mt-1">
                    An error occurred while rendering the data.
                  </p>
                </motion.div>
              );
            }
          })()}
        </AnimatePresence>
      </div>
    </div>
  );
};
