import React from "react";
import { motion } from "framer-motion";
import { DashboardStats } from "@/features/dashboard/DashboardStats";
import { JobsProcessChart } from "@/features/dashboard/JobsProcessChart";
import { QuickActionsSection } from "@/features/dashboard/QuickActionsSection";
import { RecentActivitySection } from "@/features/dashboard/RecentActivitySection";
import { withAuth } from "@/utils/withAuth";
import useFetchProcesses from "@/hooks/useProcesses";
import Head from "next/head";

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

export default function Dashboard() {
  useFetchProcesses();

  return (
    <>
      <Head>
        <title>Dashboard - Aroma</title>
      </Head>
      <div className="min-h-screen">
        <div className="">
          <motion.div
            className="space-y-10"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {/* Header */}
            <motion.div className="text-left" variants={fadeInUp}>
              <h1 className="text-4xl font-bold text-gray-900 tracking-tight mb-3">
                Dashboard
              </h1>
              <p className="text-gray-600 text-lg">
                Comprehensive overview of your projects, jobs, and recent
                activity
              </p>
            </motion.div>

            {/* Dashboard Stats - 3 per row */}
            <motion.div variants={fadeInUp}>
              <DashboardStats />
            </motion.div>

            {/* Main Content Grid */}
            <motion.div
              className="grid grid-cols-1 lg:grid-cols-2 gap-8"
              variants={fadeInUp}
            >
              {/* Jobs Process Chart */}
              <motion.div
                variants={fadeInUp}
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <JobsProcessChart />
              </motion.div>

              {/* Quick Actions */}
              <motion.div
                variants={fadeInUp}
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <QuickActionsSection />
              </motion.div>
            </motion.div>

            {/* Recent Activity - Full Width */}
            <motion.div
              variants={fadeInUp}
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.2 }}
            >
              <RecentActivitySection />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </>
  );
}

export const getServerSideProps = withAuth();
