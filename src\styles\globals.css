@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

input::-ms-reveal,
input::-ms-clear {
  display: none;
}

* {
  font-family: "Poppins", sans-serif !important;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scrollbar-gutter: stable;
  }

  body {
    @apply bg-background text-foreground;
    scrollbar-gutter: stable;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary: #0284c7;
    --color-primary-hover: #0369a1;
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-error: #dc3545;
    --color-info: #17a2b8;

    --color-gray-50: #f7f7f7;
    --color-gray-100: #f0f0f0;
    --color-gray-200: #cccccc;
    --color-gray-400: #aaaaaa;
    --color-gray-600: #666666;
    --color-gray-800: #333333;
    --color-blue: #0284c7;
  }

  html {
    font-family: var(--font-poppins) !important;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-poppins);
    font-weight: 600;
  }
}

@layer components {
  .btn-primary {
    @apply bg-sky-600 hover:bg-sky-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  /* Scrollbar utilities */
  .scrollbar-gutter {
    scrollbar-gutter: stable;
  }

  .scrollbar-rounded {
    scrollbar-width: thin;
    scrollbar-color: #999999 transparent;
  }

  .scrollbar-rounded::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-rounded::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .scrollbar-rounded::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: content-box;
  }

  .scrollbar-rounded::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }

  .simple-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #e2e8f0 transparent;
  }

  .simple-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .simple-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .simple-scrollbar::-webkit-scrollbar-thumb {
    background-color: #e2e8f0;
    border-radius: 3px;
  }

  .simple-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #cbd5e1;
  }

  /* Layout stability utilities */
  .layout-stable {
    scrollbar-gutter: stable;
  }

  .layout-stable-both {
    scrollbar-gutter: stable both-edges;
  }

  /* Prevent layout shift on scrollbar appearance */
  .no-layout-shift {
    scrollbar-gutter: stable;
    overflow-anchor: none;
  }

  /* Container that maintains width regardless of scrollbar */
  .stable-container {
    width: 100%;
    max-width: 80rem; /* max-w-7xl equivalent */
    margin-left: auto;
    margin-right: auto;
    scrollbar-gutter: stable;
  }

  .btn-danger-outline {
    @apply border border-red-600 hover:bg-red-700 text-red-600 px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-success-outline {
    @apply border border-green-600 hover:bg-green-500 text-green-600 px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-warning {
    @apply bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-warning-outline {
    @apply border border-yellow-600 hover:bg-yellow-700 text-yellow-600 px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-info {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .btn-info-outline {
    @apply border border-blue-600 hover:bg-blue-700 text-blue-600 px-6 py-2.5 rounded-lg font-medium transition-colors duration-200 focus:outline-none cursor-pointer disabled:cursor-not-allowed;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
  }

  /* Custom animations for public pages */
  @keyframes float-slow {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, -15px) rotate(1deg); }
    50% { transform: translate(-5px, -25px) rotate(-1deg); }
    75% { transform: translate(-15px, -10px) rotate(0.5deg); }
  }

  @keyframes float-reverse {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-12px, 18px) rotate(-1deg); }
    50% { transform: translate(8px, 30px) rotate(1deg); }
    75% { transform: translate(18px, 12px) rotate(-0.5deg); }
  }

  @keyframes float-medium {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(15px, -20px) scale(1.05); }
    66% { transform: translate(-10px, 15px) scale(0.95); }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
  }

  @keyframes shimmer-reverse {
    0% { transform: translateX(100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(-100%); opacity: 0; }
  }

  @keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
  }

  @keyframes fade-in-up {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
  }

  @keyframes fade-in-left {
    0% { opacity: 0; transform: translateX(-30px); }
    100% { opacity: 1; transform: translateX(0); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.1); }
    50% { box-shadow: 0 0 30px rgba(255, 255, 255, 0.2), 0 0 40px rgba(59, 130, 246, 0.1); }
  }

  @keyframes gentle-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
  }

  .animate-float-slow {
    animation: float-slow 20s ease-in-out infinite;
  }

  .animate-float-reverse {
    animation: float-reverse 25s ease-in-out infinite;
  }

  .animate-float-medium {
    animation: float-medium 15s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 8s ease-in-out infinite;
  }

  .animate-shimmer-reverse {
    animation: shimmer-reverse 10s ease-in-out infinite;
  }

  .animate-grid-move {
    animation: grid-move 30s linear infinite;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
    opacity: 0;
  }

  .animate-fade-in-left {
    animation: fade-in-left 0.6s ease-out forwards;
    opacity: 0;
  }

  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .animate-gentle-bounce {
    animation: gentle-bounce 2s ease-in-out infinite;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* Grid pattern background */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
}
