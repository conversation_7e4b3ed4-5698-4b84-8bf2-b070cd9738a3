import {
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
} from "lucide-react";
import { format } from "date-fns";
import { motion } from "framer-motion";

interface ProjectCardProps {
  project: Project;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export default function ProjectCard({
  project,
  onView,
  onEdit,
  onDelete,
}: ProjectCardProps) {
  const { completed, pending, failed } = project.job_statistics.by_status;
  const total = project.job_statistics.total_jobs;
  const progress = total > 0 ? Math.round((completed / total) * 100) : 0;
  const description =
    project.description || "No description available for this project.";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -4, transition: { duration: 0.2 } }}
      className="bg-white border border-gray-200 rounded-md p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer"
      onClick={onView}
    >
      {/* Header */}
      <div className="mb-4">
        <h3
          className="text-xl font-semibold text-black hover:text-sky-600 transition-colors line-clamp-1 mb-2"
          title={project.name}
        >
          {project.name}
        </h3>
        <p
          className="text-sm text-gray-600 line-clamp-1 mt-1"
          title={description}
        >
          {description}
        </p>
      </div>

      {/* Progress */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-black">Progress</span>
          <span className="text-sm text-gray-600">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className={`h-2.5 rounded-full transition-all duration-300 bg-gray-500`}
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center gap-2 text-sm">
          <CheckCircle className="w-4 h-4 text-sky-500" />
          <span className="text-gray-600">Completed</span>
          <span className="font-semibold text-black">{completed}</span>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <Clock className="w-4 h-4 text-gray-500" />
          <span className="text-gray-600">Pending</span>
          <span className="font-semibold text-black">{pending}</span>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <AlertCircle className="w-4 h-4 text-black" />
          <span className="text-gray-600">Total</span>
          <span className="font-semibold text-black">{total}</span>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-gray-600">Failed</span>
          <span className="font-semibold text-black">{failed}</span>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>{format(new Date(project.created_at), "MMM dd, yyyy")}</span>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="border border-red-500 text-red-500 hover:bg-red-100 p-2 rounded-md transition-colors"
            title="Delete project"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="border border-sky-700 text-sky-700 hover:bg-sky-100 text-sm px-4 py-2 rounded-md transition-colors"
          >
            <Edit className="w-4 h-4 inline mr-2" />
            Edit
          </button>
        </div>
      </div>
    </motion.div>
  );
}
