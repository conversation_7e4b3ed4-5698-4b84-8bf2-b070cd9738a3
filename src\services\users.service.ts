import api from "@/utils/axios.util";

export const GetUsersService = async (
  page = 1,
  limit = 50,
  search?: string,
  role?: string
) => {
  try {
    const response = await api.get("/users", {
      params: {
        page,
        limit,
        search,
        role,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.message ||
        "An error occured while getting users",
    };
  }
};

export const InviteUserService = async (username: string, role: string) => {
  const data = {
    username,
    role,
  };

  try {
    const response = await api.post("/users/invite", data);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while inviting user",
    };
  }
};

export const RegisterUserService = async (
  username: string,
  role: string,
  password: string,
  token: string
) => {
  const data = {
    username,
    role,
    password,
    token,
  };

  try {
    const response = await api.post("/users/register", data);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while registering user",
    };
  }
};

export const ResetPasswordService = async (userId: string) => {
  const data = {
    subordinate_id: userId,
  };

  try {
    const response = await api.post("/users/reset_password", data);
    const responseMessage = response.data.message;
    const password = responseMessage.split(" ")[4];
    return {
      success: true,
      status: response.status,
      data: password,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.message ||
        "An error occured while resetting password",
    };
  }
};

export const GetRolesService = async () => {
  try {
    const response = await api.get("/roles/", {
      params: {
        page: 1,
        limit: 50,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occured while getting roles",
    };
  }
};

export const ChangeRoleService = async (userId: string, role: string) => {
  const data = {
    user_id: userId,
    role,
  };

  try {
    const response = await api.put("/update_roles", data);
    return {
      success: true,
      status: response.status,
      data: response.data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occured while changing user role",
    };
  }
};
