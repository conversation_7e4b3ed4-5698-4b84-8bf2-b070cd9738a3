import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Button from "@/components/custom/Button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, UserX } from "lucide-react";
import {
  AssignJobsToAgentService,
  UnassignJobsService,
} from "@/services/jobs.service";
import { snackbar } from "@/utils/snackbar.util";

interface BulkAssignDialogProps {
  open: boolean;
  onClose: () => void;
  selectedJobIds: string[];
  agents: User[];
  onAssignmentComplete: () => void;
}

export default function BulkAssignDialog({
  open,
  onClose,
  selectedJobIds,
  agents,
  onAssignmentComplete,
}: BulkAssignD<PERSON>ogProps) {
  const [selectedAgentId, setSelectedAgentId] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    if (!loading) {
      setSelectedAgentId("");
      onClose();
    }
  };

  const handleAssign = async () => {
    if (!selectedAgentId || selectedJobIds.length === 0) return;

    setLoading(true);

    try {
      let response;
      let message;

      if (selectedAgentId === "unassign") {
        response = await UnassignJobsService(selectedJobIds);
        message = `Successfully unassigned ${selectedJobIds.length} job(s)`;
      } else {
        response = await AssignJobsToAgentService(
          selectedJobIds,
          selectedAgentId
        );
        const selectedAgent = agents.find(
          (agent) => agent._id === selectedAgentId
        );
        message = `Successfully assigned ${selectedJobIds.length} job(s) to ${selectedAgent?.username || "agent"}`;
      }

      if (response.success) {
        snackbar.showSuccessMessage(message);
        onAssignmentComplete();
        handleClose();
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to process jobs");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-sky-600" />
            Bulk Assignment
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selection Summary */}
          <div className="bg-sky-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge className="bg-sky-100 text-sky-800 border-sky-200">
                {selectedJobIds.length} Jobs Selected
              </Badge>
            </div>
            <p className="text-sm text-sky-700">
              You are about to assign {selectedJobIds.length} job(s) to an
              agent. This action will update the assignment for all selected
              jobs.
            </p>
          </div>

          {/* Agent Selection */}
          <div className="space-y-3">
            <Label htmlFor="agent-select" className="text-sm font-medium">
              Select Agent
            </Label>
            <Select
              value={selectedAgentId}
              onValueChange={setSelectedAgentId}
              disabled={loading}
            >
              <SelectTrigger id="agent-select">
                <SelectValue placeholder="Choose an agent..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassign">
                  <div className="flex items-center gap-2">
                    <UserX className="w-4 h-4 text-red-500" />
                    <span>Unassign Jobs</span>
                  </div>
                </SelectItem>
                {agents.map((agent) => (
                  <SelectItem key={agent._id} value={agent._id}>
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-sky-100 rounded-full flex items-center justify-center">
                        <UserCheck className="w-3 h-3 text-sky-600" />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{agent.username}</span>
                        <span className="text-xs text-gray-500 capitalize">
                          {agent.role}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Warning for Unassign */}
          {selectedAgentId === "unassign" && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <UserX className="w-5 h-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-orange-800">Unassign Jobs</h4>
                  <p className="text-sm text-orange-700 mt-1">
                    This will remove the current assignment from all selected
                    jobs. The jobs will become unassigned and available for
                    reassignment.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Assignment Confirmation */}
          {selectedAgentId && selectedAgentId !== "unassign" && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <UserCheck className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-800">
                    Assignment Confirmation
                  </h4>
                  <p className="text-sm text-green-700 mt-1">
                    {selectedJobIds.length} job(s) will be assigned to{" "}
                    <span className="font-medium">
                      {agents.find((a) => a._id === selectedAgentId)?.username}
                    </span>
                    . Any existing assignments will be updated.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={!selectedAgentId || loading}
            loading={loading}
            startIcon={
              selectedAgentId === "unassign" ? (
                <UserX className="w-4 h-4" />
              ) : (
                <UserCheck className="w-4 h-4" />
              )
            }
          >
            {loading
              ? "Processing..."
              : selectedAgentId === "unassign"
                ? "Unassign Jobs"
                : "Assign Jobs"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
