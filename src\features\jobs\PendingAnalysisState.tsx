"use client";

import { motion } from "framer-motion";
import { Clock } from "lucide-react";

interface PendingAnalysisStateProps {
  className?: string;
  title?: string;
  description?: string;
}

export default function PendingAnalysisState({
  className = "",
  title = "Pending Media Analysis",
  description = "This media file is waiting to be processed. Analysis results will appear here once processing is complete.",
}: PendingAnalysisStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={`flex flex-col items-center justify-center p-20 text-center ${className}`}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.4, ease: "easeOut" }}
        className="p-4 bg-blue-100 rounded-full mb-6"
      >
        <Clock className="w-12 h-12 text-blue-600" />
      </motion.div>
      <motion.h3
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.4 }}
        className="text-2xl font-bold text-blue-700 mb-3"
      >
        {title}
      </motion.h3>
      <motion.p
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.4 }}
        className="text-gray-600 text-lg leading-relaxed max-w-md"
      >
        {description}
      </motion.p>
    </motion.div>
  );
}
