// Job statistics by status
interface JobStatisticsByStatus {
  pending: number;
  "in-progress": number;
  completed: number;
  failed: number;
  timeout: number;
  cancelled: number;
  "partially-completed": number;
}

// Job statistics by process type
interface JobStatisticsByProcessType {
  "extract-image-data": number;
  "audio-transcribe-analysis": number;
  "generic-entity-extraction": number;
}

// Complete job statistics structure
interface JobStatistics {
  by_status: JobStatisticsByStatus;
  by_process_type: JobStatisticsByProcessType;
  total_jobs: number;
}

// Main Project type based on actual API response
type Project = {
  name: string;
  description: string;
  _id: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  job_statistics: {
    by_status: {
      pending: number;
      completed: number;
      failed: number;
    };
    total_jobs: number;
  };
};
