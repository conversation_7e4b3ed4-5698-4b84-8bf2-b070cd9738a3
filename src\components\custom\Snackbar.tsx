import { useEffect } from "react";
import { toast } from "sonner";
import { hideSnackbar } from "@/redux/slices/snackbarSlice";
import { useAppDispatch, useAppSelector } from "@/redux";

export default function Snackbar() {
  const { open, type, message } = useAppSelector((state) => state.snackbar);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (open && message) {
      // Show the toast based on type
      switch (type) {
        case "success":
          toast.success(message);
          break;
        case "error":
          toast.error(message);
          break;
        case "warning":
          toast.warning(message);
          break;
        case "info":
        default:
          toast.info(message);
          break;
      }

      // Hide the snackbar after showing the toast
      dispatch(hideSnackbar());
    }
  }, [open, type, message, dispatch]);

  // This component doesn't render anything visible
  // The actual toast is rendered by the Toaster component in _app.tsx
  return null;
}
