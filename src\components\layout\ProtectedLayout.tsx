import React from "react";
import { Sidebar } from "./Sidebar";
import { useBreadcrumbNavigation } from "@/hooks/useBreadcrumbNavigation";
import { useMainScrollRestoration } from "@/hooks/useScrollRestoration";

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Track navigation for breadcrumb tree
  useBreadcrumbNavigation();

  // Reset scroll position when route changes
  useMainScrollRestoration();

  return (
    <div className="h-screen flex no-layout-shift">
      {/* Sidebar with embedded profile menu */}
      <aside className="bg-white overflow-hidden border-r">
        <Sidebar />
      </aside>

      {/* Main content area */}
      <main className="flex-1 overflow-y-auto bg-[#FEFEFE] scrollbar-rounded relative no-layout-shift">
        <div className="stable-container py-6 px-6 max-w-screen-xl mx-auto mt-2">
          {children}
        </div>
      </main>
    </div>
  );
}
