import { setProcesses } from "@/redux/slices/processesSlice";
import { GetProcessesService } from "@/services/process.service";
import { snackbar } from "@/utils/snackbar.util";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

const useFetchProcesses = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    (async () => {
      try {
        const processesResponse: ProcessesResponse = await GetProcessesService();
        if (processesResponse.success) {
          dispatch(setProcesses(processesResponse.data as Process[]));
        }
        else {
          console.error("useProcesses() => Error fetching processes: ", processesResponse.data);
          snackbar.showErrorMessage(processesResponse.data as unknown as string);
        }
      }
      catch (error: any) {
        console.error("useProcesses() => Error fetching processes: ", error);
        snackbar.showErrorMessage(error?.message || "An error occurred while fetching processes");
      }
    })();
  }, [dispatch]);
};

export default useFetchProcesses;

type Process = any;
type ProcessesResponse = any;