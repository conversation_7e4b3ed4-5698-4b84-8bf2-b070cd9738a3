import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: Role[] = [];

const rolesSlice = createSlice({
  name: "roles",
  initialState,
  reducers: {
    setRoles: (_state, action: PayloadAction<Role[]>) => {
      return action.payload;
    },
  },
});

export const { setRoles } = rolesSlice.actions;
export default rolesSlice.reducer;

export type Role = {
  _id: string;
  name: string;
};