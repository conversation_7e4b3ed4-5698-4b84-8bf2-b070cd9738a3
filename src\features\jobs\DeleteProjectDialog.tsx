import { useState } from "react";
import { DeleteProjectService } from "@/services/projects.service";
import { snackbar } from "@/utils/snackbar.util";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Button from "@/components/custom/Button";

export default function DeleteProjectDialog({
  open,
  setOpen,
  selectedProject,
  setSelectedProject,
  onProjectDeleted,
}: DeleteProjectDialogProps) {
  const [loading, setLoading] = useState(false);

  if (!selectedProject || !setSelectedProject) {
    return null;
  }

  const handleCloseDialog = (
    _event: React.SyntheticEvent,
    _reason?: string
  ) => {
    // if (reason && reason === closeDialogReasons.backdropClick || reason === closeDialogReasons.escapeKeyDown) {
    //   return;
    // }

    setOpen(false);

    setTimeout(() => {
      setLoading(false);
      setSelectedProject(null);
    }, 500);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    const deleteProjectResponse = await DeleteProjectService(
      selectedProject._id
    );
    if (deleteProjectResponse.success) {
      snackbar.showSuccessMessage(deleteProjectResponse.data?.detail);
      if (onProjectDeleted) {
        onProjectDeleted();
      }
    } else {
      setLoading(false);
      snackbar.showErrorMessage(deleteProjectResponse.data);
    }

    handleCloseDialog(event, "submitButtonClick");
  };

  if (!selectedProject) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && setOpen(false)}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Project</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the project "{selectedProject?.name}
            "? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={(event) => {
              handleCloseDialog(event, "closeButtonClick");
            }}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            loading={loading}
            onClick={handleSubmit}
          >
            Delete Project
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type DeleteProjectDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedProject?: Project | null;
  setSelectedProject?: React.Dispatch<React.SetStateAction<Project | null>>;
  onProjectDeleted?: () => void;
};
