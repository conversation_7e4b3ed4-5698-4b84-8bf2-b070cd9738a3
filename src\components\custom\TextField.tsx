import React, { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

type TextFieldBaseProps = {
  error?: boolean;
  helperText?: string;
  multiline?: boolean;
  rows?: number;
  type?: string;
};

type InputProps = React.InputHTMLAttributes<HTMLInputElement> &
  TextFieldBaseProps & { multiline?: false | undefined };
type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement> &
  TextFieldBaseProps & { multiline: true };

type TextFieldProps = InputProps | TextareaProps;

const TextField = React.forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  TextFieldProps
>(({ error, helperText, type, className, multiline, rows, ...props }, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = type === "password";

  return (
    <div>
      <div className="relative">
        {multiline ? (
          <textarea
            ref={ref as React.Ref<HTMLTextAreaElement>}
            rows={rows ?? 4}
            className={`w-full px-4 py-2.5 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 placeholder:text-sm
            ${
              error
                ? "border-red-200 focus:ring-red-600"
                : "border-gray-200 focus:ring-black"
            }
            ${
              (props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)
                .disabled
                ? "opacity-50 cursor-not-allowed"
                : ""
            }
            ${className || ""}`}
            {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
          />
        ) : (
          <input
            ref={ref as React.Ref<HTMLInputElement>}
            type={isPassword && showPassword ? "text" : type}
            className={`w-full px-4 py-2.5 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 placeholder:text-sm
            ${
              error
                ? "border-red-200 focus:ring-red-600"
                : "border-gray-200 focus:ring-black"
            }
            ${
              (props as React.InputHTMLAttributes<HTMLInputElement>).disabled
                ? "opacity-50 cursor-not-allowed"
                : ""
            }
            ${className || ""}`}
            {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
          />
        )}

        {!multiline && isPassword && (
          <button
            type="button"
            onClick={() => setShowPassword((prev) => !prev)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            tabIndex={-1}
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        )}
      </div>

      {helperText && <p className="text-xs mt-1 text-red-500">{helperText}</p>}
    </div>
  );
});

TextField.displayName = "TextField";

export default TextField;
