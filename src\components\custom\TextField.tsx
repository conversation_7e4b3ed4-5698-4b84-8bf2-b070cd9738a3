import React, { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

type TextFieldBaseProps = {
  error?: boolean;
  helperText?: string;
  multiline?: boolean;
  rows?: number;
  type?: string;
  rightButton?: React.ReactNode;
};

type InputProps = React.InputHTMLAttributes<HTMLInputElement> &
  TextFieldBaseProps & { multiline?: false | undefined };
type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement> &
  TextFieldBaseProps & { multiline: true };

type TextFieldProps = InputProps | TextareaProps;

const TextField = React.forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  TextFieldProps
>(
  (
    {
      error,
      helperText,
      type,
      className,
      multiline,
      rows,
      rightButton,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPassword = type === "password";
    const hasRightContent = rightButton || isPassword;

    return (
      <div>
        <div className="relative">
          {multiline ? (
            <textarea
              ref={ref as React.Ref<HTMLTextAreaElement>}
              rows={rows ?? 4}
              className={`w-full px-4 py-2.5 ${
                hasRightContent ? "pr-12" : "pr-4"
              } border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 placeholder:text-sm
            ${
              error
                ? "border-red-200 focus:ring-red-600"
                : "border-gray-200 focus:ring-black"
            }
            ${
              (props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)
                .disabled
                ? "opacity-50 cursor-not-allowed"
                : ""
            }
            ${className || ""}`}
              {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
            />
          ) : (
            <input
              ref={ref as React.Ref<HTMLInputElement>}
              type={isPassword && showPassword ? "text" : type}
              className={`w-full px-4 py-2.5 ${
                hasRightContent ? "pr-12" : "pr-4"
              } border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 placeholder:text-sm
            ${
              error
                ? "border-red-200 focus:ring-red-600"
                : "border-gray-200 focus:ring-black"
            }
            ${
              (props as React.InputHTMLAttributes<HTMLInputElement>).disabled
                ? "opacity-50 cursor-not-allowed"
                : ""
            }
            ${className || ""}`}
              {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
            />
          )}

          {hasRightContent && (
            <div
              className={`absolute ${
                multiline
                  ? "top-3 right-3"
                  : "right-3 top-1/2 transform -translate-y-1/2"
              } flex items-center gap-1`}
            >
              {rightButton}
              {!multiline && isPassword && (
                <button
                  type="button"
                  onClick={() => setShowPassword((prev) => !prev)}
                  className="text-gray-500 hover:text-gray-700"
                  tabIndex={-1}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              )}
            </div>
          )}
        </div>

        {helperText && (
          <p className="text-xs mt-1 text-red-500">{helperText}</p>
        )}
      </div>
    );
  }
);

TextField.displayName = "TextField";

export default TextField;
