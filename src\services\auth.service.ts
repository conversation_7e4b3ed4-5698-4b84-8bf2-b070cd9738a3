import api from "@/utils/axios.util";
import { deleteCookie } from "cookies-next";

export const LoginService = async (
  username: string,
  password: string,
  tenantId: string
) => {
  try {
    const params = new URLSearchParams();
    params.append("grant_type", "password");
    params.append("username", username);
    params.append("password", password);
    params.append("scope", "");
    params.append("client_id", tenantId);

    const response = await api.post("/login", params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });

    console.log(response.data);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    console.log(error);
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail || "An error occurred while logging in.",
    };
  }
};

export const LogoutService = () => {
  deleteCookie("access_token");
  deleteCookie("role");
  delete<PERSON><PERSON>ie("username");
  deleteCookie("id");

  // if window is not defined, do not redirect, also if current path includes /login, do not redirect
  if (
    typeof window !== "undefined" &&
    !window.location.pathname.includes("/login")
  ) {
    window.location.href = "/login";
  }
};

export const VerifyTokenService = async (token?: string) => {
  try {
    const response = await api.get("/verify_token", {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.message ||
        "An error occurred while verifying token",
    };
  }
};

export const ChangePasswordService = async (
  oldPassword: string,
  newPassword: string
) => {
  try {
    const response = await api.post("/users/change_password", {
      old_password: oldPassword,
      new_password: newPassword,
    });
    return {
      success: true,
      status: response.status,
      data: response.data?.message || "Password changed successfully",
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.detail ||
        "An error occurred while changing password",
    };
  }
};

export const GetTenantService = async (tenantSlug: string) => {
  try {
    const response = await api.get("get_tenant_id", {
      params: {
        slug: tenantSlug,
      },
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error: any) {
    return {
      success: false,
      status: error?.response?.status || 500,
      data:
        error?.response?.data?.message ||
        "An error occurred while fetching tenant",
    };
  }
};
