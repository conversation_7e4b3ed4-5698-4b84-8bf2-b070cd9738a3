import { useEffect } from "react";
import { useRouter } from "next/router";

/**
 * Hook to restore scroll position to top when navigating between routes
 * This ensures that new pages always start from the top instead of maintaining
 * the previous page's scroll position
 */
export const useScrollRestoration = (options?: {
  selector?: string;
  behavior?: ScrollBehavior;
  immediate?: boolean;
}) => {
  const router = useRouter();
  const {
    selector = "main",
    behavior = "auto",
    immediate = true,
  } = options || {};

  useEffect(() => {
    const scrollToTop = () => {
      const element = document.querySelector(selector) as HTMLElement;
      if (element) {
        element.scrollTo({
          top: 0,
          left: 0,
          behavior,
        });
      } else {
        // Fallback to window scroll if element not found
        window.scrollTo({
          top: 0,
          left: 0,
          behavior,
        });
      }
    };

    // Scroll to top immediately if requested
    if (immediate) {
      scrollToTop();
    }

    // Listen for route changes
    const handleRouteChangeStart = () => {
      scrollToTop();
    };

    const handleRouteChangeComplete = () => {
      // Small delay to ensure DOM is ready
      setTimeout(scrollToTop, 0);
    };

    router.events.on("routeChangeStart", handleRouteChangeStart);
    router.events.on("routeChangeComplete", handleRouteChangeComplete);

    return () => {
      router.events.off("routeChangeStart", handleRouteChangeStart);
      router.events.off("routeChangeComplete", handleRouteChangeComplete);
    };
  }, [router.events, router.asPath, selector, behavior, immediate]);
};

/**
 * Hook specifically for the main layout container
 * This is the most common use case for scroll restoration
 */
export const useMainScrollRestoration = () => {
  return useScrollRestoration({
    selector: "main",
    behavior: "auto",
    immediate: true,
  });
};

/**
 * Utility function to manually scroll to top
 * Useful for programmatic scroll restoration
 */
export const scrollToTop = (
  selector = "main",
  behavior: ScrollBehavior = "auto"
) => {
  const element = document.querySelector(selector) as HTMLElement;
  if (element) {
    element.scrollTo({
      top: 0,
      left: 0,
      behavior,
    });
  } else {
    // Fallback to window scroll if element not found
    window.scrollTo({
      top: 0,
      left: 0,
      behavior,
    });
  }
};
