import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import TextField from "@/components/custom/TextField";
import Select from "@/components/custom/Select";
import Button from "@/components/custom/Button";
import { X, RefreshCcw, UserCog } from "lucide-react";
import { ChangeRoleService } from "@/services/users.service";
import { useAppSelector } from "@/redux";
import { snackbar } from "@/utils/snackbar.util";

export default function ChangeRoleDialog({
  open,
  setOpen,
  selectedUser,
  setSelectedUser,
}: ChangeRoleDialogProps) {
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState("");
  const [roleHelperText, setRoleHelperText] = useState("");

  const roles = useAppSelector((state) => state.roles);

  useEffect(() => {
    if (selectedUser) {
      setRole(selectedUser.role);
    }
  }, [selectedUser]);

  const handleCloseDialog = (event: React.SyntheticEvent) => {
    setOpen(false);

    setTimeout(() => {
      setLoading(false);
      setRole("");
      setSelectedUser(null);
    }, 500);
  };

  const handleSubmit = async (event: React.SyntheticEvent) => {
    event.preventDefault();

    setLoading(true);
    const changeRoleResponse = await ChangeRoleService(selectedUser._id, role);
    if (changeRoleResponse.success) {
      snackbar.showSuccessMessage(changeRoleResponse.data);
      handleCloseDialog(event);
    } else {
      setLoading(false);
      snackbar.showErrorMessage(changeRoleResponse.data);
    }
  };

  if (!selectedUser) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {/* <div className="w-10 h-10 bg-gradient-to-br from-sky-100 to-sky-200 rounded-lg flex items-center justify-center">
              <UserCog className="h-5 w-5 text-sky-600" />
            </div> */}
            <div>
              <h3 className="text-lg font-semibold">Change User Role</h3>
              <p className="text-sm text-gray-500 font-normal">
                Update the role for this user
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="py-6 space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">User</label>
            <TextField
              disabled
              value={selectedUser?.username || ""}
              placeholder="Username"
              className="bg-gray-50"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Role <span className="text-red-500">*</span>
            </label>
            <Select
              value={role}
              onValueChange={(value) => {
                setRole(value);
                setRoleHelperText("");
              }}
              placeholder="Select a role"
              options={roles.map((role) => ({
                value: role.name,
                label: role.name,
              }))}
              error={!!roleHelperText}
              helperText={roleHelperText}
            />
          </div>
        </div>
        <DialogFooter className="gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={(event) => handleCloseDialog(event)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            loading={loading}
            startIcon={<RefreshCcw className="h-4 w-4" />}
          >
            Update Role
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

type ChangeRoleDialogProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedUser: any;
  setSelectedUser: React.Dispatch<React.SetStateAction<any>>;
};
