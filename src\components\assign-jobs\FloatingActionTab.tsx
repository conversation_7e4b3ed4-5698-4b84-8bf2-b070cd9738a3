import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { UserPlus, UserMinus, X } from "lucide-react";

interface FloatingActionTabProps {
  selectedCount: number;
  isVisible: boolean;
  onClearSelection: () => void;
  onBulkAssign: () => void;
  onBulkUnassign: () => void;
}

export const FloatingActionTab: React.FC<FloatingActionTabProps> = ({
  selectedCount,
  isVisible,
  onClearSelection,
  onBulkAssign,
  onBulkUnassign,
}) => {
  return (
    <AnimatePresence>
      {isVisible && selectedCount > 0 && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
        >
          <div
            className="bg-white border-gray-200 border-2 rounded-md shadow-lg px-6 py-4 flex items-center gap-4 min-w-[400px]"
            role="toolbar"
            aria-label={`${selectedCount} jobs selected. Bulk actions available.`}
          >
            {/* Selection Counter */}
            <div className="flex items-center gap-2 text-sm font-medium text-[#111827]">
              <div className="w-8 h-8 bg-sky-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                {selectedCount}
              </div>
              <span>
                {selectedCount === 1 ? "job selected" : "jobs selected"}
              </span>
            </div>

            {/* Divider */}
            <div className="w-px h-6 bg-[#E5E7EB]" />

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {/* Bulk Assign Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={onBulkAssign}
                className="h-9 px-3 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white transition-all duration-200"
                aria-label={`Assign ${selectedCount} selected jobs to an agent`}
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Assign
              </Button>

              {/* Bulk Unassign Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={onBulkUnassign}
                className="h-9 px-3 border-[#DC3545] text-[#DC3545] hover:bg-[#DC3545] hover:text-white transition-all duration-200"
                aria-label={`Unassign ${selectedCount} selected jobs`}
              >
                <UserMinus className="w-4 h-4 mr-2" />
                Unassign
              </Button>

              {/* Clear Selection Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="h-9 w-9 p-0 text-[#6B7280] hover:text-[#111827] hover:bg-[#F3F4F6] transition-all duration-200"
                aria-label="Clear job selection"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Floating indicator */}
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white border-r border-b border-[#E5E7EB] rotate-45" />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
