import { Fragment, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import Button from "@/components/custom/Button";
import { UserCheck, UserX, AlertTriangle, Loader2 } from "lucide-react";
import {
  AssignJobToAgentService,
  UnassignJobsService,
} from "@/services/jobs.service";
import { snackbar } from "@/utils/snackbar.util";

interface AssigneeDropdownProps {
  job: Job & { assigned_to?: string; assigned_to_name?: string };
  agents: User[];
  onAssignmentChange: () => void;
}

export default function AssigneeDropdown({
  job,
  agents,
  onAssignmentChange,
}: AssigneeDropdownProps) {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState("");
  const [loading, setLoading] = useState(false);

  const currentAssignee = agents.find((agent) => agent._id === job.assigned_to);
  const selectedAgent = agents.find((agent) => agent._id === selectedAgentId);

  const handleValueChange = (agentId: string) => {
    if (agentId === "unassigned") agentId = ""; // Convert back to empty string
    if (agentId === job.assigned_to) return; // No change

    setSelectedAgentId(agentId);
    setConfirmDialogOpen(true);
  };

  const handleConfirmAssignment = async () => {
    // Allow empty string for unassignment, but not undefined/null
    if (selectedAgentId === undefined || selectedAgentId === null) return;

    setLoading(true);

    try {
      let response;
      let message;

      if (selectedAgentId === "") {
        // Unassign job
        response = await UnassignJobsService([job._id]);
        message = `Job "${job.name}" unassigned successfully`;
      } else {
        // Assign job
        response = await AssignJobToAgentService(job._id, selectedAgentId);
        const agent = agents.find((a) => a._id === selectedAgentId);
        message = `Job "${job.name}" assigned to ${agent?.username || "agent"}`;
      }

      if (response.success) {
        snackbar.showSuccessMessage(message);
        onAssignmentChange();
        setConfirmDialogOpen(false);
      } else {
        snackbar.showErrorMessage(response.data);
      }
    } catch (error) {
      snackbar.showErrorMessage("Failed to process assignment");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAssignment = () => {
    setConfirmDialogOpen(false);
    setSelectedAgentId("");
  };

  return (
    <>
      <Select
        value={job.assigned_to || "unassigned"}
        onValueChange={handleValueChange}
        disabled={loading}
      >
        <SelectTrigger className="w-40">
          <SelectValue placeholder="Unassigned">
            {currentAssignee ? (
              <div className="flex items-center gap-2">
                <UserCheck className="h-3 w-3" />
                <span className="truncate">{currentAssignee.username}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <UserX className="h-3 w-3 text-red-500" />
                <span className="text-red-500">Unassigned</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="unassigned">
            <div className="flex items-center gap-2">
              <UserX className="h-3 w-3 text-red-500" />
              <span className="text-red-500">Unassigned</span>
            </div>
          </SelectItem>
          {agents.map((agent) => (
            <SelectItem key={agent._id} value={agent._id}>
              <div className="flex items-center gap-2">
                <UserCheck className="h-3 w-3" />
                <span>{agent.username}</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-1 py-0.5 rounded">
                  {agent.role}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Confirmation Dialog */}
      <Transition appear show={confirmDialogOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={() => setConfirmDialogOpen(false)}
        >
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/70" />
          </TransitionChild>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <DialogTitle
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2 mb-4"
                  >
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                    Confirm Assignment
                  </DialogTitle>

                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      {selectedAgentId === "" ? (
                        <>
                          This will <strong>unassign</strong> the job{" "}
                          <strong>"{job.name}"</strong>.
                        </>
                      ) : (
                        <>
                          This will assign the job <strong>"{job.name}"</strong>{" "}
                          to{" "}
                          <strong>{selectedAgent?.username || "agent"}</strong>.
                        </>
                      )}
                    </p>

                    {currentAssignee && selectedAgentId !== "" && (
                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                        <p className="text-sm text-amber-800">
                          <strong>Note:</strong> This job is currently assigned
                          to <strong>{currentAssignee.username}</strong>. The
                          assignment will be changed.
                        </p>
                      </div>
                    )}

                    {currentAssignee && selectedAgentId === "" && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                        <p className="text-sm text-red-800">
                          <strong>Note:</strong> This job is currently assigned
                          to <strong>{currentAssignee.username}</strong>. It
                          will be unassigned.
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2 mt-6">
                    <Button
                      variant="outline"
                      onClick={handleCancelAssignment}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleConfirmAssignment}
                      disabled={loading}
                      className={
                        selectedAgentId === ""
                          ? "bg-red-600 hover:bg-red-700"
                          : "btn-primary"
                      }
                    >
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : selectedAgentId === "" ? (
                        "Confirm Unassignment"
                      ) : (
                        "Confirm Assignment"
                      )}
                    </Button>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
