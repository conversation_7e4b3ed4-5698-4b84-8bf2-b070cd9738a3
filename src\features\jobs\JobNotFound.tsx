import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileX, ArrowLeft, FolderOpen } from "lucide-react";

export default function JobNotFound() {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-6 px-4 mt-16">
      <div className="bg-gray-100 p-6 rounded-full">
        <FileX className="w-16 h-16 text-gray-400" />
      </div>
      <h2 className="text-2xl font-bold text-gray-800">Job Not Found</h2>
      <p className="text-gray-600 max-w-md">
        The job you're looking for doesn't exist or might have been removed.
      </p>
      <div className="flex flex-wrap gap-4 justify-center">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Go Back
        </Button>

        <Link href="/projects">
          <Button className="flex items-center gap-2">
            <FolderOpen className="w-4 h-4" />
            Back to Projects
          </Button>
        </Link>
      </div>
    </div>
  );
}
