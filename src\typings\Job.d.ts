type Job = {
  _id: string;
  name: string;
  description: string;
  process_name: ProcessName;
  created_at: string;
  updated_at: string;
  created_by: string;
  project_id: string;
  status: string;
  items: JobItem[];
  assigned_to?: string; // User ID
  assigned_to_name?: string; // User name for display
  assigned_at?: string;
};

type JobItem = {
  input_id: string;
  output_id: string | null;
  status: string;
};

type JobItemMedia = {
  filename: string;
  content_type: string;
  description: string | null;
  bucket_name: string;
  object_name: string;
  _id: string;
  created_at: string;
  updated_at: string | null;
  created_by: string;
  job_id: string;
  uploaded_at: string;
  uploaded_by: string;
  metadata: {
    source_type: string;
    [key: string]: any;
  };
};
