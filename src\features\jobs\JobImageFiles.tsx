"use client";

import { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import Button from "@/components/custom/Button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ChevronLeft,
  ChevronRight,
  ImageIcon,
  FileText,
  ZoomIn,
  ZoomOut,
  Save,
  FileDown,
  Check,
  Shield,
  AlertCircle,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  VerifyJobOutputService,
  EditJobOutputService,
  GetPresignedUrlService,
  GetJobItemsService,
} from "@/services/jobs.service";
import {
  DeleteMediaService,
  GetInputInputMediaService,
  GetJobOutputMediaService,
} from "@/services/media.service";
import { snackbar } from "@/utils/snackbar.util";
import RemarkDialog from "@/components/custom/RemarkDialog";
import { MediaDialog } from "@/components/media-dialog";
import { motion } from "framer-motion";
import {
  LoadingAnalysisState,
  PendingAnalysisState,
  NoMediaFilesState,
  AnalysisErrorState,
} from "./index";

type JobMediaManagerProps = {
  jobId: string;
  onAddMediaClick: () => void;
};

export interface JobImageManagerRef {
  refreshMediaList: () => void;
}

type MediaInfo = {
  _id: string;
  filename: string;
  description: string | null;
  content_type: string;
  object_name: string;
  presigned_url?: string;
  uploaded_at: string;
  status?: string;
};

type JobItem = {
  input_id: string;
  output_id: string;
  status: string;
};

type DiagramInfo = {
  coordinates: number[];
  image_obj_name: string;
};

type QuestionData = {
  main_text: string;
  sub_questions: string[];
  diagrams: DiagramInfo[];
};

type OutputResult = {
  all_questions: QuestionData[];
};

type JobOutput = {
  output_id: string;
  source_media_id: string;
  result: OutputResult | null;
  verified?: boolean;
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
};

// Download service for JSON results
const downloadJsonResult = (result: OutputResult, filename: string) => {
  const jsonData = JSON.stringify(result, null, 2);
  const blob = new Blob([jsonData], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const JobImageManager = forwardRef<JobImageManagerRef, JobMediaManagerProps>(
  ({ jobId, onAddMediaClick }, ref) => {
    const [jobInputMediaItemsData, setJobInputMediaItemsData] = useState<
      MediaInfo[]
    >([]);
    const [selectedIndex, setSelectedIndex] = useState<number>(0);
    const [loadedUrls, setLoadedUrls] = useState<Record<string, string>>({});
    const [currentItemOutput, setCurrentItemOutput] =
      useState<JobOutput | null>(null);
    const [imageZoom, setImageZoom] = useState<number>(1);
    const [selectedDiagram, setSelectedDiagram] = useState<string | null>(null);
    // Editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editedResult, setEditedResult] = useState<OutputResult | null>(null);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [currentImageUrl, setCurrentImageUrl] = useState<string>("");
    const [diagramUrls, setDiagramUrls] = useState<Record<string, string>>({});
    const [isLoadingMediaList, setIsLoadingMediaList] = useState(true);
    const [isLoadingOutput, setIsLoadingOutput] = useState(false);

    // Media dialog state
    const [showMediaDialog, setShowMediaDialog] = useState(false);
    const [mediaDialogItems, setMediaDialogItems] = useState<any[]>([]);
    const [activeMediaIndex, setActiveMediaIndex] = useState(0);

    // Verification dialog state
    const [remarkDialogOpen, setRemarkDialogOpen] = useState(false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [verificationAction, setVerificationAction] = useState<
      "verify" | "unverify"
    >("verify");

    // Expose refresh function to parent component
    useImperativeHandle(ref, () => ({
      refreshMediaList: () => {
        if (jobId) {
          setIsLoadingMediaList(true);
          setIsLoadingOutput(true);
          loadJobMediaItems();
          loadJobOutputs();
        }
      },
    }));

    const loadJobMediaItems = async () => {
      if (!jobId) {
        setIsLoadingMediaList(false);
        return;
      }

      try {
        // Step 1: Get job items to get input_id and output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error("Failed to load job items:", jobItemsResponse.data);
          snackbar.showErrorMessage("Failed to load job items.");
          setJobInputMediaItemsData([]);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setJobInputMediaItemsData([]);
          return;
        }

        // Step 2: Get input media details using input_id from job items
        const inputMediaPromises = jobItems.map(async (item: any) => {
          if (!item.input_id) return null;

          try {
            const mediaData = await GetInputInputMediaService(item.input_id);
            if (mediaData) {
              return {
                ...mediaData,
                status: item.status, // Add status from job item
              };
            }
            return null;
          } catch (error) {
            console.error(
              `Failed to load input media ${item.input_id}:`,
              error
            );
            return null;
          }
        });

        const inputMediaResults = await Promise.all(inputMediaPromises);
        const validInputMedia = inputMediaResults.filter(
          (media) => media !== null
        );

        setJobInputMediaItemsData(validInputMedia);

        // Update loaded URLs cache for media dialog
        const urlsCache: Record<string, string> = {};
        validInputMedia.forEach((media: any) => {
          if (media.presigned_url) {
            urlsCache[media._id] = media.presigned_url;
          }
        });
        setLoadedUrls(urlsCache);
      } catch (error) {
        console.error("Failed to load job media items:", error);
        snackbar.showErrorMessage("Failed to load job media items.");
        setJobInputMediaItemsData([]);
      } finally {
        setIsLoadingMediaList(false);
      }
    };

    const loadJobOutputs = async () => {
      if (!jobId || jobInputMediaItemsData.length === 0) {
        setIsLoadingOutput(false);
        return;
      }

      try {
        // Step 1: Get job items to get output_id list
        const jobItemsResponse = await GetJobItemsService(jobId);
        if (!jobItemsResponse.success) {
          console.error(
            "Failed to load job items for outputs:",
            jobItemsResponse.data
          );
          snackbar.showErrorMessage("Failed to load job items.");
          setCurrentItemOutput(null);
          return;
        }

        const jobItems = jobItemsResponse.data.data;
        if (!jobItems || jobItems.length === 0) {
          setCurrentItemOutput(null);
          return;
        }

        // Step 2: Find the output for the selected input media
        const selectedMedia = jobInputMediaItemsData[selectedIndex];
        if (!selectedMedia) {
          setCurrentItemOutput(null);
          return;
        }

        // Find the job item that corresponds to the selected input media
        const jobItem = jobItems.find(
          (item: any) => item.input_id === selectedMedia._id
        );
        if (!jobItem || !jobItem.output_id) {
          setCurrentItemOutput(null);
          setEditedResult(null);
          return;
        }

        // Step 3: Get output media details using output_id
        try {
          const outputData = await GetJobOutputMediaService(jobItem.output_id);
          if (outputData) {
            // Handle verification status from metadata if available
            if (outputData.metadata?.verification) {
              outputData.verified =
                outputData.metadata.verification.verified || false;
              outputData.verification_notes =
                outputData.metadata.verification.verification_notes || "";
              outputData.verified_at =
                outputData.metadata.verification.verified_at;
              outputData.verified_by =
                outputData.metadata.verification.verified_by;
            }

            setCurrentItemOutput(outputData);
            if (outputData?.result) {
              setEditedResult(JSON.parse(JSON.stringify(outputData.result)));
              setHasUnsavedChanges(false);
            } else {
              setEditedResult(null);
            }
          } else {
            setCurrentItemOutput(null);
            setEditedResult(null);
          }
        } catch (error) {
          console.error(
            `Failed to load output media ${jobItem.output_id}:`,
            error
          );
          setCurrentItemOutput(null);
          setEditedResult(null);
        }
      } catch (error) {
        console.error("Failed to load job outputs:", error);
        snackbar.showErrorMessage("Failed to load job outputs.");
        setCurrentItemOutput(null);
      } finally {
        setIsLoadingOutput(false);
      }
    };

    // Load media items and outputs on component mount
    useEffect(() => {
      if (jobId) {
        loadJobMediaItems();
      }
    }, [jobId]);

    // Load outputs when selectedIndex or media items change
    useEffect(() => {
      if (jobId && jobInputMediaItemsData.length > 0) {
        loadJobOutputs();
      }
    }, [selectedIndex, jobId, jobInputMediaItemsData]);

    // Auto-open media dialog when media items are loaded and update dialog items when media changes
    useEffect(() => {
      if (!isLoadingMediaList && jobInputMediaItemsData.length > 0) {
        const updateMediaDialog = async () => {
          const convertedItems = await convertToMediaDialogFormat(
            jobInputMediaItemsData
          );
          setMediaDialogItems(convertedItems);

          // Open dialog if not already open
          if (!showMediaDialog) {
            setShowMediaDialog(true);
          }
        };
        updateMediaDialog();
      } else if (jobInputMediaItemsData.length === 0 && showMediaDialog) {
        // Close dialog if no media items
        setShowMediaDialog(false);
        setMediaDialogItems([]);
      }
    }, [
      isLoadingMediaList,
      jobInputMediaItemsData.length,
      jobInputMediaItemsData,
      showMediaDialog,
    ]);

    // Convert media data to MediaDialog format
    const convertToMediaDialogFormat = async (mediaList: MediaInfo[]) => {
      const convertedItems = mediaList.map((media, index) => {
        // Use presigned URL that's already included in the media data
        const url = media.presigned_url || "";

        return {
          id: index,
          type: "image" as const,
          title: media.filename,
          url: url,
          thumbnail: url,
          product: {
            name: media.filename,
            price: media.content_type || "Unknown type",
          },
          mediaData: media, // Store original media data
        };
      });
      return convertedItems;
    };

    // Handle media dialog actions
    const handleMediaChange = (index: number) => {
      setActiveMediaIndex(index);
      setSelectedIndex(index);
    };

    // Handle media deletion
    const handleMediaDeleted = async (mediaId: string) => {
      try {
        const result = await DeleteMediaService(jobId, mediaId);
        if (result.success) {
          snackbar.showSuccessMessage("Media deleted successfully!");

          // Find the index of the deleted media
          const deletedIndex = jobInputMediaItemsData.findIndex(
            (media) => media._id === mediaId
          );

          // Update the selected index to show the next available media
          if (deletedIndex !== -1) {
            const newMediaCount = jobInputMediaItemsData.length - 1;
            if (newMediaCount === 0) {
              // No media left
              setSelectedIndex(0);
            } else if (deletedIndex >= newMediaCount) {
              // If we deleted the last item, select the previous one
              setSelectedIndex(newMediaCount - 1);
            } else {
              // Select the same index (which will now show the next item)
              setSelectedIndex(deletedIndex);
            }
          }

          // Refresh media list and load outputs for the new selection
          loadJobMediaItems();
          loadJobOutputs();
        } else {
          snackbar.showErrorMessage(result.data || "Failed to delete media");
        }
      } catch (error) {
        console.error("Failed to delete media:", error);
        snackbar.showErrorMessage("Failed to delete media");
      }
    };

    const handleNext = () => {
      if (
        selectedIndex !== null &&
        selectedIndex < jobInputMediaItemsData.length - 1
      ) {
        setSelectedIndex(selectedIndex + 1);
        setImageZoom(1);
        setSelectedDiagram(null);
        setIsEditing(false);
        setEditedResult(null);
        setHasUnsavedChanges(false);
      }
    };

    const handlePrev = () => {
      if (selectedIndex !== null && selectedIndex > 0) {
        setSelectedIndex(selectedIndex - 1);
        setImageZoom(1);
        setSelectedDiagram(null);
        setIsEditing(false);
        setEditedResult(null);
        setHasUnsavedChanges(false);
      }
    };

    const handleZoomIn = () => {
      setImageZoom((prev) => Math.min(prev + 0.25, 3));
    };

    const handleZoomOut = () => {
      setImageZoom((prev) => Math.max(prev - 0.25, 0.5));
    };

    // Editing handlers
    const handleStartEdit = () => {
      if (currentItemOutput?.result) {
        setEditedResult(JSON.parse(JSON.stringify(currentItemOutput.result)));
        setIsEditing(true);
        setHasUnsavedChanges(false);
      }
    };

    const handleCancelEdit = () => {
      setIsEditing(false);
      setEditedResult(null);
      setHasUnsavedChanges(false);
    };

    const handleSaveResult = async () => {
      if (!editedResult || !currentItemOutput || !jobId) return;

      setIsSaving(true);
      try {
        const response = await EditJobOutputService(
          jobId,
          currentItemOutput._id,
          editedResult
        );
        if (response.success) {
          snackbar.showSuccessMessage("Changes saved successfully!");
          setHasUnsavedChanges(false);
          setIsEditing(false);
          setCurrentItemOutput({
            ...currentItemOutput,
            result: editedResult,
          });
        } else {
          snackbar.showErrorMessage(response.data || "Failed to save changes");
        }
      } catch (error) {
        snackbar.showErrorMessage("Failed to save changes");
      } finally {
        setIsSaving(false);
      }
    };

    const handleDownload = async () => {
      if (!currentItemOutput?.result) return;

      setIsDownloading(true);
      try {
        const filename = `result_${currentItemOutput._id}.json`;
        downloadJsonResult(currentItemOutput.result, filename);
        snackbar.showSuccessMessage("JSON download started!");
      } catch (error) {
        snackbar.showErrorMessage("Failed to download result");
      } finally {
        setIsDownloading(false);
      }
    };

    const handleVerifyClick = () => {
      if (!currentItemOutput) return;

      if (currentItemOutput.verified) {
        // If already verified, show unverify dialog
        setVerificationAction("unverify");
        setRemarkDialogOpen(true);
      } else {
        // If not verified, show verify dialog
        setVerificationAction("verify");
        setRemarkDialogOpen(true);
      }
    };

    const handleVerificationConfirm = async (remark: string) => {
      if (!currentItemOutput || !jobId) return;

      setIsVerifying(true);
      try {
        const isVerifying = verificationAction === "verify";
        const response = await VerifyJobOutputService(
          jobId,
          currentItemOutput._id,
          isVerifying,
          remark
        );

        if (response.success) {
          const action = isVerifying ? "verified" : "unverified";
          snackbar.showSuccessMessage(`Output ${action} successfully!`);

          // Update the current output with new verification status
          const updatedOutput = {
            ...currentItemOutput,
            verified: isVerifying,
            verification_notes: remark,
            verified_at: isVerifying ? new Date().toISOString() : undefined,
          };

          // Also update the metadata.verification field
          if (updatedOutput.metadata) {
            updatedOutput.metadata.verification = {
              verified: isVerifying,
              verification_notes: remark,
              verified_at: isVerifying ? new Date().toISOString() : undefined,
              verified_by: "current_user", // You might want to get this from user context
            };
          }

          setCurrentItemOutput(updatedOutput);
          setRemarkDialogOpen(false);
        } else {
          snackbar.showErrorMessage(
            response.data || `Failed to ${verificationAction} output`
          );
        }
      } catch (error) {
        snackbar.showErrorMessage(`Failed to ${verificationAction} output`);
      } finally {
        setIsVerifying(false);
      }
    };

    // Handle retry analysis
    const handleRetryAnalysis = () => {
      setIsLoadingOutput(true);
      loadJobOutputs();
    };

    const renderEditableQuestion = (
      question: QuestionData,
      questionIndex: number
    ) => {
      const subQuestions = question.sub_questions || [];
      const diagrams = question.diagrams || [];

      return (
        <Card
          key={questionIndex}
          className="shadow-sm border border-slate-200 bg-white hover:shadow-md transition-shadow duration-200"
        >
          <CardHeader className="pb-3 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              Question {questionIndex + 1}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4">
            {/* Main Question */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-l-4 border-blue-400">
              <div className="mb-2">
                <h4 className="font-semibold text-blue-900 text-sm uppercase tracking-wide mb-2">
                  Main Question:
                </h4>
              </div>
              {isEditing ? (
                <Textarea
                  value={
                    editedResult?.all_questions?.[questionIndex]?.main_text ||
                    question.main_text
                  }
                  onChange={(e) => {
                    if (!editedResult) return;
                    const newQuestions = [
                      ...(editedResult.all_questions || []),
                    ];
                    if (!newQuestions[questionIndex]) {
                      newQuestions[questionIndex] = { ...question };
                    }
                    newQuestions[questionIndex].main_text = e.target.value;
                    setEditedResult({
                      ...editedResult,
                      all_questions: newQuestions,
                    });
                    setHasUnsavedChanges(true);
                  }}
                  className="min-h-[80px] text-sm resize-none bg-white border-blue-200 focus:border-blue-400 focus:ring-blue-400"
                  placeholder="Enter question text..."
                />
              ) : (
                <p className="text-sm text-blue-800 leading-relaxed">
                  {question.main_text}
                </p>
              )}
            </div>

            {/* Sub Questions */}
            {subQuestions.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-semibold text-slate-700 text-sm uppercase tracking-wide">
                  Sub Questions:
                </h4>
                {subQuestions.map((subQuestion, subIndex) => (
                  <div
                    key={subIndex}
                    className="p-3 bg-gradient-to-r from-slate-50 to-slate-100 rounded-lg border-l-4 border-slate-400"
                  >
                    <div className="mb-2">
                      <span className="text-sm text-slate-600 font-semibold bg-slate-200 px-2 py-1 rounded-md">
                        Sub-question {subIndex + 1}
                      </span>
                    </div>
                    {isEditing ? (
                      <Textarea
                        value={
                          editedResult?.all_questions?.[questionIndex]
                            ?.sub_questions?.[subIndex] || subQuestion
                        }
                        onChange={(e) => {
                          if (!editedResult) return;
                          const newQuestions = [
                            ...(editedResult.all_questions || []),
                          ];
                          if (!newQuestions[questionIndex]) {
                            newQuestions[questionIndex] = { ...question };
                          }
                          if (!newQuestions[questionIndex].sub_questions) {
                            newQuestions[questionIndex].sub_questions = [
                              ...subQuestions,
                            ];
                          }
                          newQuestions[questionIndex].sub_questions[subIndex] =
                            e.target.value;
                          setEditedResult({
                            ...editedResult,
                            all_questions: newQuestions,
                          });
                          setHasUnsavedChanges(true);
                        }}
                        className="min-h-[60px] text-sm resize-none bg-white border-slate-200 focus:border-slate-400 focus:ring-slate-400"
                        placeholder="Enter sub-question text..."
                      />
                    ) : (
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {subQuestion}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Diagrams */}
            {diagrams.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-700">
                  Extracted Diagrams:
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {diagrams.map((diagram, diagramIndex) => (
                    <div key={diagramIndex} className="relative group">
                      <div
                        className={`border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                          selectedDiagram === diagram.image_obj_name
                            ? "border-blue-500 shadow-lg"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() =>
                          setSelectedDiagram(
                            selectedDiagram === diagram.image_obj_name
                              ? null
                              : diagram.image_obj_name
                          )
                        }
                      >
                        <img
                          src={
                            diagramUrls[diagram.image_obj_name] ||
                            "/placeholder.svg?height=150&width=200"
                          }
                          alt={`Diagram ${diagramIndex + 1}`}
                          className="w-full h-32 object-cover"
                          onError={(e) => {
                            e.currentTarget.src =
                              "/placeholder.svg?height=150&width=200";
                          }}
                        />
                      </div>
                      <div className="mt-2 text-xs text-gray-500">
                        <p>Coordinates: [{diagram.coordinates.join(", ")}]</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      );
    };

    useEffect(() => {
      // Use presigned URL that's already included in the media data
      if (jobInputMediaItemsData[selectedIndex]?.presigned_url) {
        const url = jobInputMediaItemsData[selectedIndex].presigned_url;
        setCurrentImageUrl(url);
        setLoadedUrls((prev) => ({
          ...prev,
          [jobInputMediaItemsData[selectedIndex]._id]: url,
        }));
      }
    }, [selectedIndex, jobInputMediaItemsData]);

    useEffect(() => {
      (async () => {
        if (editedResult?.all_questions) {
          const newDiagramUrls: Record<string, string> = {};
          let hasNewUrls = false;

          for (const question of editedResult.all_questions) {
            if (question.diagrams) {
              for (const diagram of question.diagrams) {
                if (diagram.image_obj_name) {
                  // Check if we already have this URL
                  if (!diagramUrls[diagram.image_obj_name]) {
                    // For diagrams, we'll need to fetch presigned URLs since they're not included in the main response
                    try {
                      const { data } = await GetPresignedUrlService(
                        diagram.image_obj_name
                      );
                      if (data?.presigned_url) {
                        newDiagramUrls[diagram.image_obj_name] =
                          data.presigned_url;
                        hasNewUrls = true;
                      }
                    } catch (error) {
                      console.error(
                        "Failed to fetch diagram presigned URL:",
                        error
                      );
                    }
                  }
                }
              }
            }
          }

          if (hasNewUrls) {
            setDiagramUrls((prev) => ({ ...prev, ...newDiagramUrls }));
          }
        }
      })();
    }, [editedResult]);

    // Simplified conditional rendering
    let content: JSX.Element | null = null;
    if (isLoadingMediaList || isLoadingOutput) {
      content = <LoadingAnalysisState />;
    } else if (jobInputMediaItemsData.length === 0 && !isLoadingMediaList) {
      content = (
        <NoMediaFilesState
          onAddMediaClick={onAddMediaClick}
          description="Upload files to see analysis results here."
          buttonText="Upload Media"
        />
      );
    } else if (currentItemOutput?.result?.status === "error") {
      content = (
        <AnalysisErrorState
          title="Image Processing Failed"
          description="The analysis failed to process this image successfully."
          errorMessage={
            currentItemOutput.result.message ||
            currentItemOutput.result.raw_response ||
            "Unknown error occurred during processing"
          }
          onRetry={handleRetryAnalysis}
          showRetryButton={false}
        />
      );
    } else if (currentItemOutput?.result) {
      content = (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-4"
        >
          {/* Header with title, status and actions */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.4 }}
            className="flex items-center justify-between mt-2"
          >
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-sky-100 rounded-lg">
                  <FileText className="w-6 h-6 text-sky-600" />
                </div>
                <div className="flex flex-col">
                  <h2 className="text-xl font-bold text-gray-800">
                    Image Output
                  </h2>
                  {/* {jobInputMediaItemsData[selectedIndex]?.filename && (
                    <p className="text-sm text-gray-600 truncate max-w-[300px]">
                      {jobInputMediaItemsData[selectedIndex].filename}
                    </p>
                  )} */}
                </div>
              </div>

              {/* Verification status and unsaved changes badges */}
              <div className="flex items-center gap-3">
                {currentItemOutput.verified ? (
                  <Badge
                    variant="outline"
                    className="text-green-700 border-green-400 bg-green-100 font-medium px-3 py-1"
                    title={currentItemOutput.verification_notes || "Verified"}
                  >
                    <Check className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                ) : (
                  <Badge
                    variant="outline"
                    className="text-gray-600 border-gray-300 bg-gray-50 font-medium px-3 py-1"
                  >
                    <Shield className="w-3 h-3 mr-1" />
                    Not Verified
                  </Badge>
                )}
                {hasUnsavedChanges && (
                  <Badge
                    variant="outline"
                    className="text-amber-700 border-amber-400 bg-amber-100 font-medium px-3 py-1"
                  >
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Unsaved Changes
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveResult}
                    disabled={!hasUnsavedChanges || isSaving}
                    className="bg-sky-600 hover:bg-sky-700 text-white font-medium shadow-md focus:ring-sky-500"
                  >
                    {isSaving ? (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                        className="w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-1"
                      />
                    ) : (
                      <Save className="w-4 h-4 mr-1" />
                    )}
                    {isSaving ? "Saving..." : "Save Changes"}
                  </Button>
                </>
              ) : (
                <>
                  <Button size="sm" variant="outline" onClick={handleStartEdit}>
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleVerifyClick}
                    disabled={isVerifying}
                    className="text-green-700 border-green-400 bg-green-50 hover:bg-green-100 font-medium focus:ring-green-500"
                  >
                    <Check className="w-4 h-4 mr-1" />
                    {currentItemOutput.verified ? "Unverify" : "Verify"}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleDownload}
                    disabled={isDownloading || !currentItemOutput}
                    className="text-blue-700 border-blue-400 bg-blue-50 hover:bg-blue-100 font-medium focus:ring-blue-500"
                  >
                    <FileDown className="w-4 h-4 mr-1" />
                    Download Result
                  </Button>
                </>
              )}
            </div>
          </motion.div>

          {/* Content grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="space-y-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              {/* <Card className="shadow-sm"> */}
              {/* <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <FileText className="w-5 h-5 text-sky-600" />
                    Extracted Content
                    <Badge
                      variant="outline"
                      className="text-sm font-medium px-3 py-1 bg-sky-50 text-sky-700 border-sky-200"
                    >
                      {editedResult?.all_questions?.length || 0} Questions Found
                    </Badge>
                  </CardTitle>
                </CardHeader> */}
              {/* <CardContent> */}
              <div className="overflow-y-auto space-y-4">
                {(() => {
                  // Use editedResult when editing, otherwise use original data
                  const questionsToRender = isEditing
                    ? editedResult?.all_questions
                    : currentItemOutput.result.all_questions;

                  return questionsToRender && questionsToRender.length > 0 ? (
                    questionsToRender.map((question, questionIndex) =>
                      renderEditableQuestion(question, questionIndex)
                    )
                  ) : (
                    <div className="flex flex-col items-center justify-center p-12 text-center bg-slate-50 rounded-xl border-2 border-dashed border-slate-300">
                      <div className="p-4 bg-slate-100 rounded-full mb-4">
                        <FileText className="w-8 h-8 text-slate-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-slate-600 mb-2">
                        No Questions Found
                      </h3>
                      <p className="text-slate-500 max-w-sm">
                        No questions were extracted from this image. The
                        analysis may still be in progress.
                      </p>
                    </div>
                  );
                })()}
              </div>
              {/* </CardContent> */}
              {/* </Card> */}
            </motion.div>
          </motion.div>
        </motion.div>
      );
    } else if (currentItemOutput && !currentItemOutput.result) {
      content = (
        <div className="flex flex-col items-center justify-center p-16 text-center">
          <AlertCircle className="w-16 h-16 text-orange-400 mb-4" />
          <h3 className="text-xl font-medium text-orange-600 mb-2">
            Processing Error
          </h3>
          <p className="text-gray-500">
            An error occurred while processing this image.
          </p>
        </div>
      );
    } else if (
      jobInputMediaItemsData.length > 0 &&
      !isLoadingMediaList &&
      !isLoadingOutput &&
      (!currentItemOutput?.result ||
        (!currentItemOutput.result.all_questions &&
          currentItemOutput.result.status !== "error"))
    ) {
      content = (
        <PendingAnalysisState description="This image is waiting to be processed. Analysis results will appear here once processing is complete." />
      );
    }

    return (
      <>
        {/* Media Dialog */}
        {showMediaDialog && mediaDialogItems.length > 0 && (
          <MediaDialog
            mediaItems={mediaDialogItems}
            activeIndex={activeMediaIndex}
            onMediaChange={handleMediaChange}
            onAddMediaClick={onAddMediaClick}
            onMediaDeleted={handleMediaDeleted}
          />
        )}
        {content}
        {selectedDiagram && (
          <div
            className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50"
            onClick={() => setSelectedDiagram(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <img
                src={
                  diagramUrls[selectedDiagram] ||
                  "/placeholder.svg?height=400&width=600"
                }
                alt="Selected diagram"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <Button
                size="sm"
                variant="secondary"
                className="absolute top-2 right-2"
                onClick={() => setSelectedDiagram(null)}
              >
                ✕
              </Button>
            </div>
          </div>
        )}

        {/* Verification Remark Dialog */}
        <RemarkDialog
          open={remarkDialogOpen}
          onOpenChange={setRemarkDialogOpen}
          title={
            verificationAction === "verify"
              ? "Verify Output"
              : "Unverify Output"
          }
          description={
            verificationAction === "verify"
              ? "Please provide a remark for verifying this output. This will mark the output as verified."
              : "Please provide a remark for unverifying this output. This will remove the verification status."
          }
          placeholder={
            verificationAction === "verify"
              ? "Enter verification notes..."
              : "Enter reason for unverifying..."
          }
          confirmLabel={verificationAction === "verify" ? "Verify" : "Unverify"}
          onConfirm={handleVerificationConfirm}
          loading={isVerifying}
          initialRemark={currentItemOutput?.verification_notes || ""}
          required={true}
        />
      </>
    );
  }
);

JobImageManager.displayName = "JobImageManager";

export default JobImageManager;
